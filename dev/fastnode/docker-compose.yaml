version: '3'
services:
  rabbitmq:
    restart: always
    image: rabbitmq:3.8.5-management
    container_name: rabbitmq
    hostname: rabbit
    ports:
      - 5672:5672
      - 15672:15672
    environment:
      TZ: Asia/Shanghai
      RABBITMQ_DEFAULT_USER: rabbit   #自定义登录账号
      RABBITMQ_DEFAULT_PASS: 123456 #自定义登录密码
    volumes:
      - ./data:/var/lib/rabbitmq
      - ./plugins:/plugins2
      # - ./conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
  mysql:
    image: biarms/mysql:5.7
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
    ports:
      - 3307:3306
    volumes:
      - ./data/mysql:/var/lib/mysql      