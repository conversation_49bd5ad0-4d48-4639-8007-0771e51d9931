const Sequelize = require('sequelize');
const Base = require('./base.js');
const KeyLocation = require('../../model/v2/key_location');
const Community = require('../../model/v2/community');
const Building = require('../../model/v2/building');
const Wordbook = require('../../model/v2/pt_wordbook');
const { Op } = require('sequelize');

module.exports = class extends Base {
  /**
     * GET /v2/key_location/:id
     * 获取单个重点部位信息，包括关联的小区、建筑和楼层
     */
  async getAction() {
    try {
      const id = this.get('id');
      console.log(`Fetching key location with ID: ${id}`);
      const keyLocation = await KeyLocation.findByPk(id, {
        include: [
          { model: Community, as: 'community' },
          { model: Building, as: 'building' }
        ],
        attributes: {
          include: [
            [Sequelize.col('community.name'), 'communityName'],
            [Sequelize.col('building.buildingNumber'), 'buildingName']
          ]
        }
      });
      if (!keyLocation) {
        return this.fail('未找到对应的重点部位');
      }
      return this.success(keyLocation);
    } catch (error) {
      return this.fail('获取重点部位信息失败', error);
    }
  }

  /**
     * GET /v2/key_location
     * 获取重点部位列表（带分页和过滤），包括关联的小区、建筑和楼层
     */
  async listAction() {
    try {
      const filters = this.get();
      const keyword = this.get('keyword');
      delete filters.page;
      delete filters.pageSize;
      delete filters.keyword;
      delete filters.pageDir;

      let orFilters = {};
      if (keyword) {
        orFilters = {
          '$community.address$': { [Op.like]: `%${keyword}%` },
          '$community.name$': { [Op.like]: `%${keyword}%` }
        };
      }

      const result = await this.listWithPagination(KeyLocation, filters, orFilters, {
        include: [
          { model: Community, as: 'community', attributes: [] },
          { model: Building, as: 'building', attributes: [] },
          { model: Wordbook, as: 'floor', attributes: [] }
        ],
        attributes: {
          exclude: ['community', 'building', 'floor'],
          include: [
            [Sequelize.col('community.name'), 'communityName'],
            [Sequelize.col('community.address'), 'communityAddress'],
            [Sequelize.col('building.buildingNumber'), 'buildingNumber']
          ]
        }
      });
      this.success(result);
    } catch (error) {
      this.fail('获取重点部位列表失败', error);
    }
  }

  /**
 * POST /v2/key_location
 * 创建新重点部位并关联小区、建筑和楼层
 */
  async postAction() {
    const data = this.post();
    try {
      // 检查 communityId, buildingId, floorId 是否存在并有效
      const community = await Community.findByPk(data.communityId);
      const building = await Building.findByPk(data.buildingId);

      if (!community) {
        return this.fail('关联的小区不存在');
      }
      if (!building) {
        data.buildingId = null;
      }

      // 创建重点部位
      const keyLocation = await KeyLocation.create(data);
      return this.success('重点部位创建成功', keyLocation);
    } catch (error) {
      return this.fail('重点部位创建失败', error);
    }
  }

  /**
     * PUT /v2/key_location/:id
     * 更新重点部位信息
     */
  async putAction() {
    const id = this.get('id');
    const data = this.post();
    try {
      const keyLocation = await KeyLocation.findByPk(id);
      if (!keyLocation) {
        return this.fail('未找到对应的重点部位');
      }
      await keyLocation.update(data);
      return this.success('重点部位更新成功');
    } catch (error) {
      return this.fail('重点部位更新失败', error);
    }
  }

  /**
     * DELETE /v2/key_location/:id
     * 删除重点部位
     */
  async deleteAction() {
    const id = this.get('id');
    try {
      const keyLocation = await KeyLocation.findByPk(id);
      if (!keyLocation) {
        return this.fail('未找到对应的重点部位');
      }
      await keyLocation.destroy();
      return this.success('重点部位删除成功');
    } catch (error) {
      return this.fail('重点部位删除失败', error);
    }
  }
};
