
const Base = require('../base.js');
const courseware = think.model('studymanage/courseware');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('studymanage/courseware'));
  }

  /**
   * @api {get} studymanage/courseware/courseSelect 课程下拉
   * @apiDescription 课程下拉
   * @apiName studymanage/courseware/courseSelect
   * @apiGroup studymanage
   * @apiSuccess {Object} data  查询的数据结果
   */
  async courseSelectAction() {
    try {
      const res = await courseware.courseSelect();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get} studymanage/courseware/list 课件列表
   * @apiDescription 课件列表
   * @apiName studymanage/courseware/list
   * @apiGroup studymanage
   * @apiParam {String} cname 课件名称
   * @apiParam {Number} _page 页码
   * @apiParam {Number} _limit 每页条数
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    try {
      const params = this.getParamOrPost();
      const res = await courseware.listData(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} studymanage/courseware/addData 课件新增
   * @apiDescription 课件新增
   * @apiName studymanage/courseware/addData
   * @apiGroup studymanage
   * @apiParam {String} cname 课件名称
   * @apiParam {Number} is_use 是否上架1是0否
   * @apiParam {Number} type 课件类型：1是视频，0是电子文档
   * @apiParam {String} duration 视频时长
   * @apiParam {String} relation 装备类型id，多个逗号分隔
   * @apiParam {String} groupid 课件上传的groupid
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addDataAction() {
    try {
      const params = this.post();
      const uid = (await this.isLogin()).uid;
      const res = await courseware.addData(params, uid);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} studymanage/courseware/editData 课件编辑
   * @apiDescription 课件编辑
   * @apiName studymanage/courseware/editData
   * @apiGroup studymanage
   * @apiParam {Number} id 列表id
   * @apiParam {Number} is_use 是否上架1是0否
   * @apiParam {String} cname 课件名称
   * @apiParam {Number} type 课件类型：1是视频，0是电子文档
   * @apiParam {String} duration 视频时长
   * @apiParam {String} relation 装备类型id，多个逗号分隔
   * @apiParam {String} groupid 课件上传的groupid
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editDataAction() {
    try {
      const params = this.post();
      const uid = (await this.isLogin()).uid;
      const res = await courseware.editData(params, uid);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} studymanage/courseware/isuseMany 批量上下架
   * @apiDescription 批量上下架
   * @apiName studymanage/courseware/isuseMany
   * @apiGroup studymanage
   * @apiParam {Array} ids 列表id数组
   * @apiParam {Number} is_use 是否上架1是0否
   * @apiSuccess {Object} data  查询的数据结果
   */
  async isuseManyAction() {
    try {
      const params = this.post();
      const uid = (await this.isLogin()).uid;
      const res = await courseware.isuseMany(params, uid);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} studymanage/courseware/delData 课件删除
   * @apiDescription 课件删除
   * @apiName studymanage/courseware/delData
   * @apiGroup studymanage
   * @apiParam {Number} id 列表id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async delDataAction() {
    try {
      const params = this.post();
      const res = await courseware.delData(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
