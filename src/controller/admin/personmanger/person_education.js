const Base = require('../../base.js');
const personeducation = think.model('admin/personmanger/person_education');

module.exports = class extends Base {
    async __before() {
        await this.setModelBase(this.model('admin/personmanger/person_education'));
    }

    /**
     * @api {get,post} admin/personmanger/person_education/list 获取教育经历登记列表
     * @apiDescription 获取教育经历登记列表
     * @apiName admin/personmanger/person_education/list
     * @apiGroup personmanger
     * @apiParam {Number} _page 页码
     * @apiParam {Number} _limit 每页条数
     * @apiParam {Number} departmentid 部门id
     * @apiParam {String} name 姓名 支持模糊查询
     * @apiParam {Number} degree 学历
     * @apiParam {Number} sex 性别
     * @apiSuccess {Object} data  查询的数据结果
     */
    async listAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0//(await this.isLogin()).uid;
            think.logger.debug('listAction ==> ', params, uid);
            const res = await personeducation.queryData(params, uid);
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {get,post} admin/personmanger/person_education/export 导出教育经历登记列表
     * @apiDescription 导出教育经历登记列表
     * @apiName admin/personmanger/person_education/export
     * @apiGroup personmanger
     * @apiParam {Number} _page 页码
     * @apiParam {Number} _limit 每页条数
     * @apiParam {Number} departmentid 部门id
     * @apiParam {String} name 姓名 支持模糊查询
     * @apiParam {Number} degree 学历
     * @apiParam {Number} sex 性别
     * @apiParam {Boolean} _isLoadAll
     * @apiSuccess {Object} data  查询的数据结果
     */
    async exportAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0;//(await this.isLogin()).uid;
            think.logger.debug('exportAction ==> ', params, uid);
            await personeducation.export(this.ctx, params, uid);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person_education/add 新增教育经历
     * @apiDescription 新增教育经历
     * @apiName admin/personmanger/person_education/add
     * @apiGroup personmanger
     * @apiParam {Number} person_id 人员信息的id
     * @apiParam {Number} degree 学历 1小学，2初中，3高中，4中专，5大专，6本科，7硕士，8博士
     * @apiParam {String} [major] 专业
     * @apiParam {Array} edu_exp '教育经历，数组array [{starttime:xx, endtime:xx, witness:xx, school:xx, job:xx}]
     * @apiParam {String} remark 备注
     * @apiParam {String} groupid 附件id
     * @apiSuccess {Object} data  添加情况
     */
    async addAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.loginUser()).uid;
            if (params.edu_exp) {
                params.edu_exp = JSON.stringify(params.edu_exp);
            }
            think.logger.debug('addAction ==> ', params, uid);
            const res = await personeducation.addData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person_education/update 更新教育经历
     * @apiDescription 更新教育经历
     * @apiName admin/personmanger/person_education/update
     * @apiGroup personmanger
     * @apiParam {Number} id 记录id
     * @apiParam {Number} person_id 人员信息的id
     * @apiParam {Number} degree 学历 1小学，2初中，3高中，4中专，5大专，6本科，7硕士，8博士
     * @apiParam {String} [major] 专业
     * @apiParam {Array} edu_exp '教育经历，数组array [{starttime:xx, endtime:xx, witness:xx, school:xx, job:xx}]
     * @apiParam {String} remark 备注
     * @apiParam {String} groupid 附件id
     * @apiSuccess {Object} data  添加情况
     */
    async updateAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.isLogin()).uid;
            if (params.edu_exp) {
                params.edu_exp = JSON.stringify(params.edu_exp);
            }
            think.logger.debug('updateAction ==> ', params, uid);
            const res = await personeducation.updateData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person_education/delete 删除教育经历信息
     * @apiDescription 删除教育经历信息
     * @apiName admin/personmanger/person_education/delete
     * @apiGroup personmanger
     * @apiParam {Array} ids 记录id数组
     */
    async deleteAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.isLogin()).uid;
            think.logger.debug('deleteAction ==> ', params, uid);
            const res = await personeducation.deleteData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }
}