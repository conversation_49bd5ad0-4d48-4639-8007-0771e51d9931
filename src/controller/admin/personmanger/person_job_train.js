const Base = require('../../base.js');
const personJobTrain = think.model('admin/personmanger/person_job_train');

module.exports = class extends Base {
    async __before() {
        await this.setModelBase(this.model('admin/personmanger/person_job_train'));
    }

    /**
     * @api {get,post} admin/personmanger/person_job_train/list 获取工作培训登记列表
     * @apiDescription 获取工作培训登记列表
     * @apiName admin/personmanger/person_job_train/list
     * @apiGroup personmanger
     * @apiParam {Number} _page 页码
     * @apiParam {Number} _limit 每页条数
     * @apiParam {Number} departmentid 部门id
     * @apiParam {Number} train_type 培训类别，1内部培训，2外部培训
     * @apiParam {Number} sex 性别
     * @apiParam {String} name 姓名 支持模糊查询
     * @apiSuccess {Object} data  查询的数据结果
     */
    async listAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0;//(await this.isLogin()).uid;
            think.logger.debug('listAction ==> ', params, uid);
            const res = await personJobTrain.queryData(params, uid);
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {get,post} admin/personmanger/person_job_train/export 导出工作培训登记列表
     * @apiDescription 导出工作培训登记列表
     * @apiName admin/personmanger/person_job_train/export
     * @apiGroup personmanger
     * @apiParam {Boolean} _isLoadAll 是否导出全部数据
     * @apiParam {Number} _page 页码
     * @apiParam {Number} _limit 每页条数
     * @apiParam {Number} departmentid 部门id
     * @apiParam {Number} train_type 培训类别，1内部培训，2外部培训
     * @apiParam {Number} sex 性别
     * @apiParam {String} name 姓名 支持模糊查询
     * @apiSuccess {Object} data  查询的数据结果
     */
    async exportAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0;//(await this.isLogin()).uid;
            think.logger.debug('exportAction ==> ', params, uid);
            await personJobTrain.export(this.ctx, params, uid);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person_job_train/add 新增工作培训经历
     * @apiDescription 新增工作培训经历
     * @apiName admin/personmanger/person_job_train/add
     * @apiGroup personmanger
     * @apiParam {Number} person_id 人员信息的id
     * @apiParam {Number} train_type 培训类型，1内部培训，2外部培训
     * @apiParam {String} [train_topic] 培训主题
     * @apiParam {String} starttime 开始时间 10位数时间戳
     * @apiParam {String} endtime 结束时间 10位数时间戳
     * @apiParam {String} [job] 担任职务
     * @apiParam {String} remark 备注
     * @apiParam {String} groupid 附件id
     * @apiSuccess {Object} data  添加情况
     */
    async addAction() {
        try {
            const params = this.post();
            const uid = 0 //(await this.loginUser()).uid;
            think.logger.debug('addAction ==> ', params, uid);
            const res = await personJobTrain.addData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person_job_train/update 更新人员信息
     * @apiDescription 更新人员信息
     * @apiName admin/personmanger/person_job_train/update
     * @apiGroup personmanger
     * @apiParam {Number} id 记录id
     * @apiParam {Number} person_id 人员信息的id
     * @apiParam {Number} train_type 培训类型，1内部培训，2外部培训
     * @apiParam {String} [train_topic] 培训主题
     * @apiParam {String} starttime 开始时间 10位数时间戳
     * @apiParam {String} endtime 结束时间 10位数时间戳
     * @apiParam {String} [job] 担任职务
     * @apiParam {String} remark 备注
     * @apiParam {String} groupid 附件id
     * @apiSuccess {Object} data  添加情况
     */
    async updateAction() {
        try {
            const params = this.post();
            const uid = 0 //(await this.isLogin()).uid;
            think.logger.debug('updateAction ==> ', params, uid);
            const res = await personJobTrain.updateData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person_job_train/delete 删除人员信息
     * @apiDescription 删除人员信息
     * @apiName admin/personmanger/person_job_train/delete
     * @apiGroup personmanger
     * @apiParam {Array} ids 记录id数组
     */
    async deleteAction() {
        try {
            const params = this.post();
            const uid = 0 //(await this.isLogin()).uid;
            think.logger.debug('deleteAction ==> ', params, uid);
            const res = await personJobTrain.deleteData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }
}