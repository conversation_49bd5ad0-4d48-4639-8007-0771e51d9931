const Base = require('../../base.js');
const personRewardPunish = think.model('admin/personmanger/person_reward_punish');

module.exports = class extends Base {
    async __before() {
        await this.setModelBase(this.model('admin/personmanger/person_reward_punish'));
    }

    /**
     * @api {get,post} admin/personmanger/person_reward_punish/list 获取奖惩记录列表
     * @apiDescription 获取奖惩记录列表
     * @apiName admin/personmanger/person_reward_punish/list
     * @apiGroup personmanger
     * @apiParam {Number} _page 页码
     * @apiParam {Number} _limit 每页条数
     * @apiParam {Number} departmentid 部门id
     * @apiParam {String} name 姓名 支持模糊查询
     * @apiParam {Number} type 性质，1奖励，2惩戒
     * @apiParam {Number} sex 性别 1男 2女
     * @apiSuccess {Object} data  查询的数据结果
     */
    async listAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0;//(await this.isLogin()).uid;
            think.logger.debug('listAction ==> ', params, uid);
            const res = await personRewardPunish.queryData(params, uid);
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {get,post} admin/personmanger/person_reward_punish/export 导出奖惩记录列表
     * @apiDescription 导出奖惩记录列表
     * @apiName admin/personmanger/person_reward_punish/export
     * @apiGroup personmanger
     * @apiParam {Boolean} _isLoadAll 是否导出全部数据
     * @apiParam {Number} _page 页码
     * @apiParam {Number} _limit 每页条数
     * @apiParam {Number} departmentid 部门id
     * @apiParam {String} name 姓名 支持模糊查询
     * @apiParam {Number} type 性质，1奖励，2惩戒
     * @apiParam {Number} sex 性别 1男 2女
     * @apiSuccess {Object} data  查询的数据结果
     */
    async exportAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0;//(await this.isLogin()).uid;
            think.logger.debug('exportAction ==> ', params, uid);
            await personRewardPunish.export(this.ctx, params, uid);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} /admin/personmanger/person_reward_punish/add 新增奖惩记录
     * @apiDescription 新增奖惩记录
     * @apiName admin/personmanger/person_reward_punish/add
     * @apiGroup personmanger
     * @apiParam {Number} person_id 人员信息的id
     * @apiParam {Number} type 性质，1奖励，2惩戒
     * @apiParam {String} event_content 事件内容
     * @apiParam {String} [time] 事件发生时间 10位数时间戳
     * @apiParam {String} remark 备注
     * @apiParam {String} [groupid] 附件id
     * @apiSuccess {Object} data  添加情况
     */
    async addAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.loginUser()).uid;
            if (params.edu_exp) {
                params.edu_exp = JSON.stringify(params.edu_exp);
            }
            think.logger.debug('addAction ==> ', params, uid);
            const res = await personRewardPunish.addData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} /admin/personmanger/person_reward_punish/update 更新奖惩记录
     * @apiDescription 更新奖惩记录
     * @apiName admin/personmanger/person_reward_punish/update
     * @apiGroup personmanger
     * @apiParam {Number} id 记录id
     * @apiParam {Number} person_id 人员信息的id
     * @apiParam {Number} type 性质，1奖励，2惩戒
     * @apiParam {String} event_content 事件内容
     * @apiParam {String} [time] 事件发生时间
     * @apiParam {String} remark 备注
     * @apiParam {String} [groupid] 附件id
     * @apiSuccess {Object} data  添加情况
     */
    async updateAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.isLogin()).uid;
            think.logger.debug('updateAction ==> ', params, uid);
            const res = await personRewardPunish.updateData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person_reward_punish/delete 删除奖惩记录
     * @apiDescription 删除奖惩记录
     * @apiName admin/personmanger/person_reward_punish/delete
     * @apiGroup personmanger
     * @apiParam {Array} ids 记录id数组
     */
    async deleteAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.isLogin()).uid;
            think.logger.debug('deleteAction ==> ', params, uid);
            const res = await personRewardPunish.deleteData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }
}