const Base = require('../../base.js');
const person = think.model('admin/personmanger/person');

module.exports = class extends Base {
    async __before() {
        await this.setModelBase(this.model('admin/personmanger/person'));
    }

    /**
     * @api {get,post} admin/personmanger/person/export 导出人员信息列表
     * @apiDescription 导出人员信息列表
     * @apiName admin/personmanger/person/export
     * @apiGroup personmanger
     * @apiParam {Boolean} _isLoadAll
     * @apiParam {String} [departmentid] 部门id
     * @apiParam {Number} [_page] 页码
     * @apiParam {Number} [_limit] 每页条数
     * @apiParam {Number} [status] 在职状态，1在职，2离职
     * @apiParam {String} [name] 姓名 支持模糊查询
     * @apiParam {Number} [sex] 性别
     * @apiSuccess {Object} data  查询的数据结果
     */
    async exportAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0;//(await this.isLogin()).uid;
            think.logger.debug('exportAction ==> ', params, uid);
            await person.export(this.ctx, params, uid);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }
    /**
     * @api {get,post} admin/personmanger/person/list 获取人员信息列表
     * @apiDescription 获取人员信息列表
     * @apiName admin/personmanger/person/list
     * @apiGroup personmanger
     * @apiParam {Number} [id] 记录id
     * @apiParam {String} [departmentid] 部门id
     * @apiParam {Number} [_page] 页码
     * @apiParam {Number} [_limit] 每页条数
     * @apiParam {Number} [status] 在职状态，1在职，2离职
     * @apiParam {String} [name] 姓名 支持模糊查询
     * @apiParam {Number} [sex] 性别
     * @apiSuccess {Object} data  查询的数据结果
     */
    async listAction() {
        try {
            const params = this.getParamOrPost();
            const uid = 0;//(await this.isLogin()).uid;
            think.logger.debug('listAction ==> ', params, uid);
            const res = await person.queryData(params, uid);
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person/add 新增人员信息
     * @apiDescription 新增人员信息
     * @apiName admin/personmanger/person/add
     * @apiGroup personmanger
     * @apiParam {String} name 姓名
     * @apiParam {Number} sex 性别 1男，2女
     * @apiParam {Number} status 在职状态，1在职，2离职
     * @apiParam {String} nation 民族
     * @apiParam {String} native_place 籍贯
     * @apiParam {Number} [age] 年龄
     * @apiParam {String} id_card 身份证
     * @apiParam {String} [birth_date] 出生日期 10位数时间戳
     * @apiParam {String} departmentid 部门id
     * @apiParam {String} department_name 部门名称
     * @apiParam {String} [email] 邮箱
     * @apiParam {String} [political_face] 政治面貌
     * @apiParam {String} [bank_card] 银行卡号
     * @apiParam {String} [phone] 联系号码
     * @apiParam {String} address 家庭住址
     * @apiParam {String} [groupid] 证件照id
     * @apiParam {Array} families 家庭成员及主要社会关系 [{name:姓名, relationship:关系, political_face:政治面貌, job_desc:工作或学习单位及职务}]
     * @apiParam {Array} jobs 工作履历 [{work_unit:工作单位, job: 职务, starttime: 开始时间, endtime:结束时间, remark:备注信息}]'
     * @apiParam {String} summary 个人总结
     * @apiParam {Array} other_remark 其他备注
     * @apiSuccess {Object} data  添加情况
     */
    async addAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.loginUser()).uid;
            if (params.families) {
                params.families = JSON.stringify(params.families)
            }
            if (params.jobs) {
                params.jobs = JSON.stringify(params.jobs)
            }
            if (params.other_remark) {
                params.other_remark = JSON.stringify(params.other_remark)
            }
            think.logger.debug('addAction ==> ', params, (this.loginUser()).uid);
            const res = await person.addData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person/update 更新人员信息
     * @apiDescription 更新人员信息
     * @apiName admin/personmanger/person/update
     * @apiGroup personmanger
     * @apiParam {Number} id 记录id
     * @apiParam {String} name 姓名
     * @apiParam {Number} sex 性别 1男，2女
     * @apiParam {Number} status 在职状态，1在职，2离职
     * @apiParam {Number} education 学历 ？这个可能不用？
     * @apiParam {String} nation 民族
     * @apiParam {String} native_place 籍贯
     * @apiParam {Number} [age] 年龄
     * @apiParam {String} id_card 身份证
     * @apiParam {Date} [birth_date] 出生日期
     * @apiParam {String} departmentid 部门id
     * @apiParam {String} [email] 邮箱
     * @apiParam {String} [political_face] 政治面貌
     * @apiParam {String} [bank_card] 银行卡号
     * @apiParam {String} [phone] 联系号码
     * @apiParam {String} address 家庭住址
     * @apiParam {String} groupid 证件照id
     * @apiSuccess {Object} data  添加情况
     */
    async updateAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.isLogin()).uid;
            if (params.families) {
                params.families = JSON.stringify(params.families)
            }
            if (params.jobs) {
                params.jobs = JSON.stringify(params.jobs)
            }
            if (params.other_remark) {
                params.other_remark = JSON.stringify(params.other_remark)
            }
            think.logger.debug('updateAction ==> ', params, uid);
            const res = await person.updateData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }


    /**
     * @api {post} admin/personmanger/person/delete 删除人员信息
     * @apiDescription 删除人员信息
     * @apiName admin/personmanger/person/delete
     * @apiGroup personmanger
     * @apiParam {Array} ids 记录id数组
     */
    async deleteAction() {
        try {
            const params = this.post();
            const uid = 0//(await this.isLogin()).uid;
            think.logger.debug('deleteAction ==> ', params, uid);
            const res = await person.deleteData(params, uid)
            return this.success(res);
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }
    /**
     * @api {post} admin/personmanger/person/tmpsave 临时保存信息
     * @apiDescription 临时保存信息
     * @apiName admin/personmanger/person/tmpsave
     * @apiGroup personmanger
     * @apiParam {String} type 类型
     * @apiParam {Object} data 数据
     */
    async tmpsaveAction() {
        try {
            const params = this.post();
            const uid = (await this.isLogin()).uid;
            think.logger.debug('tmpsaveAction ==> ', params, uid);
            await this.cache(`person_save_${uid}_${params.type}`, params.data);
            return this.success({status: 0, data: {}});
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }

    /**
     * @api {post} admin/personmanger/person/gettmp 获取临时缓存的数据
     * @apiDescription 获取临时缓存的数据
     * @apiName admin/personmanger/person/gettmp
     * @apiGroup personmanger
     * @apiParam {String} type 类型
     */
    async gettmpAction() {
        try {
            const params = this.post();
            const uid = (await this.isLogin()).uid;
            const data = await this.getCacheDataByKey(`person_save_${uid}_${params.type}`) || {};
            think.logger.debug('getCacheAction ==> ', data, uid);
            return this.success({status: 0, data});
        } catch (err) {
            think.logger.info(err);
            return this.fail(-1, err.message);
        }
    }
}