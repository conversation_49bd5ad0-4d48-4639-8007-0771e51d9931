const Base = require('../../base.js');
const Err = require('../../../common/errorCode');
const meetingModel = think.model('admin/emini/meetingmanage');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(meetingModel);
  }
  // 会议列表
  async meetinglistAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await meetingModel.meetinglist(param);
      // if (ret.status !== 0) {
      //   throw new Error(ret.msg);
      // }
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
  // 会议与会人员
  async getmeetinguserAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await meetingModel.getmeetinguser(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
  // 补签
  async signagainAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await meetingModel.signagain(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
  // 当前正在进行的会议
  async getCurrentmeetingAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await meetingModel.getCurrentmeeting(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
  // 当前正在进行的会议
  async getfiltermeetinglistAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await meetingModel.getfiltermeetinglist(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
  // 人员参会统计表
  async getalluserAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await meetingModel.getalluser(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }

  // 人员参会签到情况
  async signbyuserAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await meetingModel.signbyuser(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
};
