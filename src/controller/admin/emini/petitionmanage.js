const Base = require('../../base.js');
const Err = require('../../../common/errorCode');
const petitionModel = think.model('admin/emini/petitionmanage');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(petitionModel);
  }
  async disposalAction() {
    try {
      const config = think.config('sysconfig');
      const param = this.getParamOrPost();
      const ret = await petitionModel.disposal(param, config);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
};
