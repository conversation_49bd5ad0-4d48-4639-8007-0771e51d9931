const Base = require('../../base.js');
const accurateModel = think.model('admin/map/accuratealarm');

module.exports = class extends Base {
  // async __before() {
  //   await this.setModelBase(this.model('admin/supervision/regulations'));
  // }
  
  async getListAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await accurateModel.accurateList(param);
      if (ret.status === -1) return this.fail(-1, ret.msg);
      return this.success(ret.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async addAction() {
    try {
      const param = this.getParamOrPost();
      if (!param.created_at) {
        param.created_at = think.datetime(new Date());
      }
      const ret = await accurateModel.insertBase(param);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      return this.success(ret.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async eventLogAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await accurateModel.getEventLog(param);
      if (ret.status === -1) return this.fail(-1, ret.msg);
      return this.success(ret.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async handleAddAction() {
    try {
      const param = this.getParamOrPost();
      if (!param.created_at) {
        param.created_at = think.datetime(new Date());
      }
      const adminInfo = await this.isLogin();
      const ret = await accurateModel.addEventHandle(param, adminInfo);
      if (ret.status !== 0) {
        return this.fail(-1, ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.mesessage);
    }
  }
  async handleAddsAction() {
    try {
      const param = this.getParamOrPost();
      if (!param.created_at) {
        param.created_at = think.datetime(new Date());
      }
      const adminInfo = await this.isLogin();
      const ret = await accurateModel.addEventHandles(param, adminInfo);
      if (ret.status !== 0) {
        return this.fail(-1, ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.mesessage);
    }
  }
  async phoneBookAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await accurateModel.getPhoneBook(param);
      if (ret.status !== 0) {
        return this.fail(-1, ret.msg);
      }
      return this.success(ret.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.mesessage);
    }
  }

  /**
   * @api {close}  /admin/map/accuratealarm/closeBasicEventByPid 根据建筑id关闭报警事件
   * @apiGroup accuratealarm
   * @apiParam {string} pid 建筑群id
   * @apiSuccess {data} data
   * **/
  async closeBasicEventByPidAction() {
    try {
      const param = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await accurateModel.closeBasicEventByPid(param, user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.mesessage);
    }
  }
  
  async closeBasicEventByTimeAction() {
    try {
      const param = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await accurateModel.closeBasicEventByTime(param, user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.mesessage);
    }
  }

  // =====  根据生成时间段删除风险事件 =====
  async closeBasicEventByServerTimeAction() {
    try {
      const param = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await accurateModel.closeBasicEventByServerTime(param, user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.mesessage);
    }
  }
};
