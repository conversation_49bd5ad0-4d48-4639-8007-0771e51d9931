/**
 * auther : yangheng
 */
const Base = require("../../base");
const carLib = require("../../../lib/tracCar");
const moment = require("moment");
const statInterface = ['route','events','trips','stops','summary'];

/**
 * 实时检测
 */
module.exports = class extends Base {
  /**
   * @api {get} /admin/map/tracCar/getDeviceInfo 获取车辆信息
   * @apiDescription 获取车辆信息
   * @apiGroup tracCar
   * @apiSuccess {Object} data 查询的数据结果
   */
  async getDeviceInfoAction() {
    const params = this.getParamOrPost();
    const ret = await this.model(`vehicle_information`).where({deleted:0}).select();
    let res = await carLib.getDevices();
    let newRes = [];
    for(let val of res){
      for(let rval of ret){
        if(val.id == rval.devices_id){
          newRes.push(val);
        }
      }
    }
    return this.success(newRes);
  }

  /**
   * @api {get} /admin/map/tracCar/updateDevices 更新车辆信息
   * @apiDescription 更新车辆信息
   * @apiParam {Number} id
   * @apiGroup tracCar
   * @apiSuccess {Object} data 查询的数据结果
   */
  async updateDevicesAction() {
    const params = this.getParamOrPost();
    const res = await carLib.updateDevices(params);
    return this.success(res);
  }

   /**
   * @api {get} /admin/map/tracCar/getPositionsInfo 获取车辆定位详细信息列表
   * @apiDescription 获取车辆定位详细信息列表
   * @apiGroup tracCar
   * @apiSuccess {Object} data 查询的数据结果
   */
  async getPositionsInfoAction() {
    let params = this.getParamOrPost();
    if(params.hasOwnProperty('id')){
      params.id = params['id'].split(',');
    }
    const res = await carLib.getPositionsInfo(params);
    return this.success(res);
  }

  /**
   * @api {get} /admin/map/tracCar/getEventsInfo 统计列表-事件
   * @apiDescription 统计列表
   * @apiGroup tracCar
   * @apiSuccess {Object} data 查询的数据结果
   */
  async getEventsInfoAction() {
    const params = this.getParamOrPost();
    const to = new Date();
    to.setHours(to.getHours(), to.getMinutes() - to.getTimezoneOffset());
    // console.log(to.toISOString())
    const from = new Date(new Date().setMonth((new Date().getMonth()-1)));
    from.setHours(from.getHours(), from.getMinutes() - from.getTimezoneOffset());
    const res = await carLib.getEventsInfo({deviceId:1,groupId:1,type:'allEvents',from:from.toISOString(),to:to.toISOString(),page:1,start:0,limit:25});
    return this.success(res);
  }

  /**
   * @api {get} /admin/map/tracCar/getstatList 统计列表
   * @apiDescription 统计列表
   * @apiGroup tracCar
   * @apiSuccess {Object} data 查询的数据结果
   */
  async getstatListAction() {
    let params = this.getParamOrPost();
    let method = statInterface[params.method];
    if(!method) return [];
    delete params.method;
    const res = await carLib.getstatListInfo(params,method);
    return this.success(res);
  }
  
};
