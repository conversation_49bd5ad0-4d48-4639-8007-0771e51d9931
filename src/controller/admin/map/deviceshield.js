const Base = require('../../base.js');
const deviceShieldModel = think.model('admin/map/deviceshield');
module.exports = class extends Base {
  async getListAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await deviceShieldModel.getShieldList(param);
      if (ret.status === -1) return this.fail(-1, ret.msg);
      return this.success(ret.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async addAction() {
    try {
      const param = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const ret = await deviceShieldModel.insertShield(param, adminInfo);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
