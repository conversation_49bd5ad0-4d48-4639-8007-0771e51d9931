const Base = require('../base.js');
const Err = require('../../common/errorCode');
const hydrantModel = think.model('admin/firehydrant');
const hydrantService = think.service('equipment/hydrant');
module.exports = class extends Base {
  async __before() {
    await this.setModelBase(hydrantModel);
  }
  async listAction() {
    try {
      const param = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const userInfo = await think.model('admin').where({id: adminInfo.uid}).find();
      let ext = null;
      // if (param.street_code) {
      //   ext = null;
      // } else {
      //   ext = {filter: 'street_code', street_code: ['in', userInfo.street_code.split(',')]};
      // }
      if(param.num){
        ext = {filter: '_complex', _complex:{num:['like', '%'+param.num+'%'],name:['like','%'+param.num+'%'],_logic:'or'}};
      }

      const ret = await hydrantModel.queryBase(param, ext);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {post} /admin/firehydrant/getList 获取消防栓列表
   * @apiGroup firehydrant
   * @apiParam {Number} page 分页
   * @apiParam {Number} page_size 每页大小
   * @apiParam {Object} filter
   */
  async getListAction() {
    try {
      const params = await this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const userInfo = await think.model('admin').where({id: adminInfo.uid}).find();
      params.filter = params.filter || {};
      if (!params.filter.street_code) {
        params.filter.street_code = ['in', userInfo.street_code.split(',')];
      }
      const ret = await hydrantModel.getList(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
  /**
   * @api {post} /admin/firehydrant/count 消防栓数量信息
   * @apiGroup firehydrant
   */
  async countAction() {
    try {
      const adminInfo = await this.isLogin();
      const userInfo = await think.model('admin').where({id: adminInfo.uid}).find();
      const filter = {};
      if (userInfo.street_code) {
        filter.street_code = ['in', userInfo.street_code.split(',')];
      }
      const total = await hydrantModel.where(filter).count();
      const iotTotal = await hydrantModel.where(Object.assign({type: ['IN', [3, 4, 5]]}, filter)).count();
      const notIotTotal = await hydrantModel.where(Object.assign({type: ['IN', [0, 1, 2]]}, filter)).count();
      const exceptionTotal = await hydrantModel.where(Object.assign({status: 3}, filter)).count();
      const ret = {total, iotTotal, notIotTotal, exceptionTotal};
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {get,post} /admin/firehydrant/firepatrolrecord 请求消防栓寻查记录列表
   * @apiDescription 请求消防栓寻查记录列表
   * @apiName /admin/firehydrant/firepatrolrecord
   * @apiGroup firehydrant
   * @apiSuccess  data  查询的数据结果
   */
  async firepatrolrecordAction() {
    const params = this.getParamOrPost();
    const res = await this.model('admin/firehydrant').firepatrolrecord(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get,post} /admin/firehydrant/searecording 请求消防栓的维修记录
   * @apiDescription 请求消防栓的维修记录
   * @apiName /admin/firehydrant/searecording
   * @apiGroup firehydrant
   * @apiParam {int} id 消防栓的id
   * @apiSuccess  data  查询的数据结果
   */
  async searecordingAction() {
    const params = this.getParamOrPost();
    const res = await this.model('admin/firehydrant').searecording(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get,post} /admin/firehydrant/mainevent 请求消防栓的维修事件表
   * @apiDescription 请求消防栓的维修事件表
   * @apiName /admin/firehydrant/mainevent
   * @apiGroup firehydrant
   * @apiParam {String} number 对应的维修单号
   * @apiParam {String} status 对应的维修状态
   * @apiSuccess  data  查询的数据结果
   */
  async maineventAction() {
    const params = this.getParamOrPost();
    const res = await this.model('admin/firehydrant').mainevent(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get,post} /admin/firehydrant/devicestatus 按照设备状态来筛选对应的设备
   * @apiDescription 按照设备状态来筛选对应的设备
   * @apiName /admin/firehydrant/devicestatus
   * @apiGroup firehydrant
   * @apiParam {int} status 要查找的设备状态
   * @apiSuccess  data  查询的数据结果
   */
  async devicestatusAction() {
    const params = this.getParamOrPost();
    const res = await this.model('admin/firehydrant').devicestatus(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get,post} /admin/firehydrant/searchtime 按维保时间搜索对应的记录
   * @apiDescription 按维保时间搜索对应的记录
   * @apiName /admin/firehydrant/searchtime
   * @apiGroup firehydrant
   * @apiParam {String} start 开始时间 end结束时间
   * @apiSuccess  data  查询的数据结果
   */
  async searchtimeAction() {
    const params = this.getParamOrPost();
    const res = await this.model('admin/firehydrant').searchtime(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get,post} /admin/firehydrant/searchtype 请求关于消火栓的数据词典
   * @apiDescription 请求
   * @apiName /admin/firehydrant/searchtype
   * @apiGroup firehydrant
   * @apiSuccess  data  查询的数据结果
   */
  async searchtypeAction() {
    const params = this.getParamOrPost();
    const res = await this.model('admin/firehydrant').searchtype(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post,get} /[controllerPath|controllerName]/edit 修改数据 
   * @apiDescription 标准修改功能接口，支持基于list接口告知允许修改的字段列表动态构建表单的保存
   * @apiName /base/edit
   * @apiGroup controllerBase
   * @apiParam {String} id 需要修改的ID字段。
   * @apiParam {String} [fieldname] 需要提交的字段名，根据表单需要，有多少字段就提交多少个键值对。
   */
  async editAction() { 
    try {
      const paramas = this.getParamOrPost();
      const res = await this.modelBase.updateBase(paramas, await this.isLogin());
      if (res.status === 0) {
        if(paramas.fire_images){
          let res = await this.model('file_temp').where({fileid:paramas.fire_images}).find();
          if(!think.isEmpty(res)){
            await think.model('admin/filesave/file').claimFile(res.groupid);
          }
        }
        return this.success(res.data, res.msg);
      } else {
        return this.fail(-1, res.msg);
      }
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, res.msg);
    }
  }
};
