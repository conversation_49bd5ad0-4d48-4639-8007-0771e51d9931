const Base = require('../base.js');
const streetModel = think.model('admin/street');
const publicService = think.service('platform/public');
const companyModel = think.model('admin/company');
const firehydrantModel = think.model('admin/firehydrant');
const alarmFaultModel = think.model('basic_event');
const alarmManageFireModel = think.model('basic_event');
const statisticsService = think.service('equipment/statistics');
const buildingGroupService = think.service('information/buildingGroup');
const serviceProviderService = think.service('information/service');
const request = require('request-promise');
const config = require('../../config.js');
const statisticsModel = think.model('admin/statistics');
const moment = require('moment');
moment.locale('zh');

module.exports = class extends Base {
  /**
   * @api {post} /admin/statistics/offlinedevicelist 离线设备清单
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async offlinedevicelistAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await statisticsService.getofflinedevicelist(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/bgofflinelist 按建筑群查离线设备
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async bgofflinelistAction() {
    try {
      const params = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const ret = await statisticsService.getbgofflinelist(params, rangeGroup);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/bgofflinedetail 建筑群离线设备详情
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async bgofflinedetailAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await statisticsService.getbgofflinedetail(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/bgoptionlist
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async bgoptionlistAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await statisticsService.getbgoptionlist(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {post} /admin/statistics/serviceoptionlist
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async serviceoptionlistAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await statisticsService.getserviceoptionlist(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/serviceofflinelist 按服务商查离线设备
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async serviceofflinelistAction() {
    try {
      const params = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const ret = await statisticsService.getserviceofflinelist(params, rangeGroup);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/serviceofflinedetail 按服务商离线设备详情
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async serviceofflinedetailAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await statisticsService.getserviceofflinedetail(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/supervisionfail 电子督查单接收失败日志
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async supervisionfailAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await statisticsService.getsupervisionfail(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/humansupervisionfail 人工督查单接收失败日志
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async humansupervisionfailAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await statisticsService.gethumansupervisionfail(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/accessBuildingGroup 获取每个街道的建筑群数量，0未接入，1已接入，2已接入未完成
   * @apiGroup building
   * @apiParam {Number} accessstatus 接入状态
   */
  async accessBuildingGroupAction() {
    try {
      const params = this.getParamOrPost();
      const status = parseInt(params.accessstatus) ? parseInt(params.accessstatus) : 0;
      if (![0, 1, 2].includes(status)) {
        throw new Error('状态参数错误');
      }
      const ret = await streetModel.alias('s').field('s.id,s.street_name,count(*) as total').join({
        table: 'building_group', join: 'left', as: 'bg', on: ['s.street_code', 'bg.street_code']
      }).where(`bg.accessstatus=${status}`).group('s.id').select();
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {post} /admin/statistics/today 今天的统计数据
   * @apiGroup statistics
   */
  getMonthDay(today, i) {
    const preFun = (val) => {
      if (val < 10) {
        return '0' + val;
      }
      return val;
    };
    let year = today.getFullYear();
    let month = today.getMonth() + 1 - i;
    if (month <= 0) {
      year = year - 1;
      month = preFun(12 + month);
      return year + '-' + month + '-01 00:00:00';
    } else {
      month = preFun(month);
      return year + '-' + month + '-01 00:00:00';
    }
  }
  async todayAction() {
    const param = this.getParamOrPost();
    try {
      let nowDate = moment().format('YYYY-MM-DD');
      const nowDatas = moment().format('YYYY-MM-DD');
      if (parseInt(param.type) === 1) {
        // 按日
        nowDate = moment().format('YYYY-MM-DD');
      } else if (parseInt(param.type) === 2) {
        // 按周
        nowDate = moment().subtract(7, 'days').format('YYYY-MM-DD 00:00:00');
      } else if (parseInt(param.type) === 3) {
        // 按月
        nowDate = moment().subtract(1, 'months').format('YYYY-MM-DD 00:00:00');
      } else if (parseInt(param.type) === 4) {
        // 按年
        nowDate = moment().subtract(1, 'years').format('YYYY-MM-DD 00:00:00');
      }
      const obj = {
        type: 1,
        date: ['between', `${nowDate},${nowDatas}`]
      };
      const obj2 = {
        type: 2,
        date: ['between', `${nowDate},${nowDatas}`]
      };
      const obj3 = {
        type: 3,
        date: ['between', `${nowDate},${nowDatas}`]
      };
      const obj4 = {
        type: 1
      };
      const obj5 = {
        type: 2
      };
      const obj6 = {};
      if (param.usenature) {
        if (param.usenature.length > 0) {
          const usenatureLen = param.usenature.toString();
          const pid = await this.getUsenatureByPid(usenatureLen);
          obj.pid = ['IN', pid];
          obj2.pid = ['IN', pid];
          obj3.pid = ['IN', pid];
          obj4.pid = ['IN', pid];
          obj5.pid = ['IN', pid];
          obj6.building_group_id = ['IN', pid];
        };
      }
      // 今日新增事件
      const eventNum = await think.model('daily_count').field('sum(value) as num').where(obj).find();
      // 今日新增电子督查单
      const taskNum = await think.model('daily_count').field('sum(value) as num').where(obj2).find();
      // 今日新增人工督查单
      const sheetNum = await think.model('daily_count').field('sum(value) as num').where(obj3).find();

      // 全部事件
      const allEventNum = await think.model('daily_count').field('sum(value) as count').where(obj4).find();
      console.log('allEventNum', allEventNum);
      // 全部的电子督察单
      // const taskLogNum = await think.mongo('task_log', 'mongo').where({projectid: { '$ne': 0 }}).count();
      // const filteres = ` type in (1,2,3,4) and projectid!=0 `;
      // const taskNums = await this.model('task').where(filteres).count();
      let allTaskNum = await think.model('daily_count').field('sum(value) as num').where(obj5).find();
      if (config.server_env === 'jingan' && param.usenature && param.usenature.length > 1) {
        allTaskNum = allTaskNum.num + 340000;
      } else {
        allTaskNum = allTaskNum.num ? allTaskNum.num : 0;
      }
      // 全部的人工督察单
      const allSheetNum = await think.model('supervise_sheet_artificial').where(obj6).count();

      let filter = {created_at: {
        '$gte': moment(nowDate).unix(),
        '$lte': moment(nowDatas).endOf("days").unix()
      }}
      const fireCount = await think.mongo(`fire_basic_event_log`, 'mongo').where(filter).count();

      const res3 = {
        taskNum: taskNum.num ? taskNum.num : 0,
        sheetNum: sheetNum.num ? sheetNum.num : 0,
        eventNum: eventNum.num ? eventNum.num : 0,
        allTaskNum: allTaskNum,
        allSheetNum: allSheetNum,
        allEventNum: allEventNum.count ? allEventNum.count : 0,
        fireCount: fireCount || 0
      };
      const data = {res3};
      return this.success(data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/statistics/company 单位数量统计
   * @apiGroup statistics
   * @apiParam {Number} type 1消防责任主体,  2辖区单位, 3市政消火栓
   */
  async companyAction() {
    const param = this.getParamOrPost();
    let ret;
    const type = parseInt(param.type) || 0;
    if (type === 1) {
      const serviceTotal = await companyModel.alias('c').field('count(distinct(c.id)) as total').where(` c.company_type&1 and c.deleted=0 and c.certification_status=2 `).find();
      const maintenanceTotal = await companyModel.alias('c').field('count(distinct(c.id)) as total').where(`c.company_type&4 and c.deleted=0 and ((c.admin_id is null and c.certification_status!=1) or (c.certification_status=2))`).find();
      const manageTotal = await companyModel.where({company_type: ['EXP', '&2'], deleted: 0}).count();
      ret = {serviceTotal, maintenanceTotal, manageTotal};
    } else if (type === 2) {
      const cityPointNum = await companyModel.alias('c').where(` c.o_type=1 and c.company_type&2 and c.deleted=0 and  ((c.admin_id is null and c.certification_status!=1) or (c.certification_status=2)) `).count();
      const districtPointNum = await companyModel.alias('c').where(`c.o_type=2 and c.company_type&2 and c.deleted=0 and((c.admin_id is null and c.certification_status!=1) or (c.certification_status=2)) `).count();
      const smallNum = await companyModel.alias('c').where(` c.o_type=3 and c.company_type&2 and c.deleted=0 and ((c.admin_id is null and c.certification_status!=1) or (c.certification_status=2)) `).count();
      const normalNum = await companyModel.alias('c').where(` c.o_type=4 and c.company_type&2 and c.deleted=0 and ((c.admin_id is null and c.certification_status!=1) or (c.certification_status=2)) `).count();
      const otherNum = await companyModel.alias('c').where(` c.o_type=5 and c.company_type&2 and c.deleted=0 and ((c.admin_id is null and c.certification_status!=1) or (c.certification_status=2)) `).count();
      ret = {cityPointNum, districtPointNum, smallNum, normalNum, otherNum};
    } else if (type === 3) {
      const adminInfo = await this.isLogin();
      const userInfo = await think.model('admin').where({id: adminInfo.uid}).find();
      let filter;
      if (userInfo.street_code) {
        filter = ` street_code in (${userInfo.street_code.split(',')}) `;
      }
      const filter2 = filter + ` and status = 0 `;
      const filter3 = filter + ` and status = 3 `;
      const filter4 = filter + ` and status = 2 `;
      const filter5 = filter + ` and status = 1 `;
      const firehydrantTotal = await firehydrantModel.where(filter).count();
      const firehydrantNormal = await firehydrantModel.where(filter2).count();
      const firehydrantAlarm = await firehydrantModel.where(filter3).count();
      const firehydrantBreakdown = await firehydrantModel.where(filter4).count();
      const firehydrantOffline = await firehydrantModel.where(filter5).count();
      ret = {firehydrantTotal, firehydrantNormal, firehydrantAlarm, firehydrantBreakdown, firehydrantOffline};
    }
    return this.success(ret);
  }

  /**
   * @api {post} /admin/statistics/supervise 1安全评分,  2故障, 3火警, 4隐患
   * @apiGroup statistics
   * @apiParam {Number} type 1安全评分,  2故障, 3火警, 4隐患
   */
  async superviseAction() {
    try {
      const param = this.getParamOrPost();
      let list;
      const type = parseInt(param.type) || 0;
      if (type === 1) {
        list = await companyModel.where({company_type: ['EXP', '&2'], deleted: 0}).order('id desc').limit(0, 10).select();
      } else if (type === 2) {
        list = await alarmManageFireModel.alias('am').field('count(*) as count, bg.id, bg.name, bg.address').join({
          table: 'building_group', join: 'left', as: 'bg', on: ['am.project_id', 'bg.id']
        }).where({value: 2, status: 0}).group('am.project_id').order('count desc').limit(0, 10).select();
      } else if (type === 3) {
        list = await alarmManageFireModel.alias('am').field('count(*) as count, bg.id, bg.name, bg.address').join({
          table: 'building_group', join: 'left', as: 'bg', on: ['am.project_id', 'bg.id']
        }).where({value: 1, host_reset_status: 0}).group('am.project_id').order('count desc').limit(0, 10).select();
      } else if (type === 4) {
        const idO = await this.model('event_category').where({name: '隐患'}).find();
        let idL = [];
        list = [];
        if (!think.isEmpty(idL)) idL = await this.model('event_category_relation').where({cid: idO.id});
        if (idO.length > 0) {
          const ids = idL.map(m => m.eid);
          list = await alarmFaultModel.alias('am').field('count(*) as count, bg.id, bg.name, bg.address').join({
            table: 'building_group', join: 'left', as: 'bg', on: ['am.project_id', 'bg.id']
          }).where({status: 0, event_id: ['IN', ids]}).group('am.project_id').order('count desc').limit(0, 10).select();
        }
      }
      return this.success(list);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {post} /admin/statistics/tendency 报警趋势
   * @apiGroup statistics
   * @apiParam {Number} type  1近一周, 2最近一月, 3最近一年
   */
  async tendencyAction() {
    try {
      const param = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const type = parseInt(param.type);
      let ret;
      if (type === 1) {
        ret = await statisticsService.getWeekData(adminInfo);
      } else if (type === 2) {
        ret = await statisticsService.getMonthData(adminInfo);
      } else if (type === 3) {
        ret = await statisticsService.getYearData(adminInfo);
      }
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {post} /admin/statistics/proportion 系统占比
   * @apiGroup statistics
   */
  async proportionAction() {
    try {
      const adminInfo = await this.isLogin();
      const ret = await statisticsService.getProportionData(adminInfo);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 事件记录
   */
  async eventAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.getEvent(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 安全评分
   */
  async safetyScoreAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.safetyScore(params);
      for (const item of res.data) {
        if (item.level) {
          await buildingGroupService.calculateScore({pid: item.id});
        }
      }
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 故障发生频率
   */
  async faultAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.fault(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 电子督查单 count
   */
  async dzSupervisionAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.dzSupervision(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 人工督查单
   */
  async rgSupervisionAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.rgSupervision(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async todoEventAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/tendency').todoEvent(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 查询天气接口
  async getWeatherAction() {
    try {
      const url = 'https://free-api.heweather.net/s6/weather/now?location=shanghai&key=60ac1a91b08f493084b796bce7f7efd9';
      const retStr = await request.get(url);
      const data = JSON.parse(retStr);
      return this.success(data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * 火警、故障、屏蔽点击
   */
  async getAllClickAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.getAllClick(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 隐患 点击
   */
  async gethiddClickAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.gethiddClick(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 人工督查单和电子督查单数据合并 显示聚合
   */
  async getAllSupervisionAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.getAllSupervision(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 人工督查单和电子督查单数据合并 混合并显示聚合
   */
  async getBlendSupervisionAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.getBlendSupervision(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async headerTotalAction() {
    try {
      const user = await this.isLogin();
      const param = this.getParamOrPost();
      if (param.usenature.length > 0) {
        const usenatureLen = param.usenature.join(',');
        const pid = await this.getUsenatureByPid(usenatureLen);
        param.pid = pid;
      }
      const res = await buildingGroupService.headerTotal(user, param);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 电子督查单生成次数
  async electronSupervisionAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/tendency').electronSupervision(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 人工督查单生成次数
  async artificialSupervisionAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/tendency').artificialSupervision(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 设备异常报警次数
  async equipmentExceptionAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/tendency').equipmentException(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 平均响应时长
  async avgTimeAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/tendency').avgTime(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 设备平均报警数
  async equipmentAvgAction() {
    try {
      const user = await this.isLogin();
      const res = await this.model('admin/tendency').equipmentAvg(user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 电子督查单查看
  async taskInfoAction() {
    try {
      const user = await this.isLogin();
      const res = await this.model('admin/tendency').taskInfo(user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 人工督察单查看
  async sheetInfoAction() {
    try {
      const user = await this.isLogin();
      const res = await this.model('admin/tendency').sheetInfo(user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据修复
  /**
   * 取出已接入服务商的建筑群id  然后 根据id去mongo里面去统计 已处理的火警、隐患、屏蔽、故障的个数
   */
  async resetAction() {
    const res = await this.model('admin/tendency').reset();
    return this.success(res);
  }
  async serviceBuildingGroupAction() {
    try {
      const res = await serviceProviderService.serviceBuildingGroup();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async buildingGroupJoinStatusAction() {
    try {
      const adminInfo = await this.isLogin();
      const res = await buildingGroupService.buildingGroupJoinStatus(adminInfo);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async buildingGroupAlarmAction() {
    try {
      const res = await buildingGroupService.buildingGroupAlarm();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async buildingGroupFireSystemAction() {
    try {
      const ret = await buildingGroupService.buildingGroupFireSystem();
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async historyAlarmRotioAction() {
    try {
      const params = this.getParamOrPost();
      const ret = await buildingGroupService.historyAlarmRotio(params);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {post} /admin/statistics/tongjibystreet 调用存储过程
   * @apiGroup tongji
   */
  async tongjibystreetAction() {
    try {
      const user = await this.isLogin();
      const sql = ` select a.id, c.id as pid, c.name as bname, a.o_name as name,c.address as daddress, c.street_code, d.name as sname,c.accessstatus, c.pass_percent, c.regulatory_level
      from pt_company as a 
      left join pt_map_manage_building_group as b
      on a.id = b.company_id
      left join pt_building_group as c 
      on b.building_group_id  = c.id
      left join pt_area as d
      on d.code = c.street_code
      where a.deleted = 0 and a.company_type&2 and c.superviselevel = 1 and b.status = 2 and c.deleted = 0 and a.o_type = 1
      UNION ALL
      select a.id, c.id as pid, c.name as bname, a.o_name as name,c.address as daddress, c.street_code, d.name as sname, c.accessstatus, c.pass_percent, c.regulatory_level
      from pt_company as a 
      left join pt_map_building_company_rent as b
      on a.id = b.company_id
      left join pt_building_group as c 
      on b.building_group_id  = c.id
      left join pt_area as d
      on d.code = c.street_code
      where a.deleted = 0 and a.company_type&8 and c.superviselevel = 1 and c.deleted = 0 and a.o_type = 1 GROUP BY a.id`;
      const tongjibystreetInfo = await think.cache(`${user.username}_tongjibystreetInfo`);
      let ret;
      if (think.isEmpty(tongjibystreetInfo)) {
        console.log('无缓存');
        ret = await think.model('company').query(sql);
        for (const i of ret) {
          // 查询项目设备表数据
          const info = await think.model('projectdev').field('id').where(` pid = ${i.pid} `).group('systemid').count();
          const level = await think.model('supervise_facilities_equipment').where(` level_id = ${i.regulatory_level} `).group('system_id').select();
          const serverinfo = await think.model('map_service_building_group').where(` stop_status = 1 and building_group_id = ${i.pid} `).find();
          let serverInfos = 0;
          if (serverinfo.id) {
            const infos = await think.model('map_service_building_group').where(` building_group_id = ${i.pid} `).select();
            for (const j of infos) {
              serverInfos += j.stop_status;
            }
            if (serverInfos === infos.length) {
              i['stopCount'] = 1;
            } else {
              i['stopCount'] = 0;
            }
          }
          // 判断全部接入是 1 部分 接入是 0
          if (info === level.length) {
            i['partCount'] = 1;
          } else {
            i['partCount'] = 0;
          }
          if (info === 0) {
            i['accessstatus'] = 0;
          }
        }
        await think.cache(`${user.username}_tongjibystreetInfo`, `'${JSON.stringify(ret)}'`, { timeout: 24 * 60 * 60 * 1000 });
      } else {
        console.log('存在缓存');
        const str = tongjibystreetInfo.substring(1, tongjibystreetInfo.length - 1);
        ret = JSON.parse(str);
      }
      let streetCode = '';
      if (config.server_env === 'jingan') {
        streetCode = '310106000000';
      } else if (config.server_env === 'huangpu') {
        streetCode = '310101000000';
      } else {
        streetCode = '310106000000';
      }
      const streetInfo = await think.model('area').field('code, name').where(` parent_code = ${streetCode} `).select();
      for (const i of streetInfo) {
        const deleteData = ret.filter(j => j.street_code === i.code);
        const insatus = deleteData.filter(l => l.accessstatus === 1);
        const weistatus = deleteData.filter(l => l.accessstatus === 0);
        const wholeCount = insatus.filter(l => l.partCount === 0);
        const partCount = insatus.filter(l => l.partCount === 1);
        const stopCount = insatus.filter(l => l.stopCount === 1);
        i['allCount'] = deleteData.length;
        i['weiCount'] = weistatus.length;
        i['buCount'] = wholeCount.length;
        i['partCount'] = partCount.length;
        i['stopCount'] = stopCount.length;
      };
      return this.success(streetInfo);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * 火灾报警主机报警趋势
   */
  async fireAlarmAction() {
    try {
      const param = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const type = parseInt(param.type);
      let ret;
      if (type === 1) {
        ret = await buildingGroupService.getWeekData(adminInfo);
      } else if (type === 2) {
        ret = await buildingGroupService.getMonthData(adminInfo);
      } else if (type === 3) {
        ret = await buildingGroupService.getYearData(adminInfo);
      }
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * 其他系统主机报警趋势
   */
  async otherAlarmAction() {
    try {
      const param = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const type = parseInt(param.type);
      let ret;
      if (type === 1) {
        ret = await buildingGroupService.getWeekDatas(adminInfo);
      } else if (type === 2) {
        ret = await buildingGroupService.getMonthDatas(adminInfo);
      } else if (type === 3) {
        ret = await buildingGroupService.getYearDatas(adminInfo);
      }
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * 建筑管理单位 履责统计
   */
  async manageAccountStaticsAction() {
    try {
      const adminInfo = await this.isLogin();
      const ret = await buildingGroupService.manageAccountStatics(adminInfo);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 设施设备接入情况
  async equipmentAccessAction() {
    try {
      const ret = await buildingGroupService.equipmentAccess();
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 报警数据
  async alarmDataAction() {
    try {
      const ret = await buildingGroupService.alarmData();
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 重点单位
  async importCompanyAction() {
    try {
      const param = this.getParamOrPost();
      const user = await this.isLogin();
      const ret = await publicService.importCompany(param, user);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {post} /admin/statistics/allImportCompany 查询所有的重点单位已接入
   * @apiGroup building
   */
  async allImportCompanyAction() {
    try {
      const params = this.getParamOrPost();
      const user = await this.isLogin();
      const allImportCompany = await think.cache(`${user.username}_allImportCompany`);
      let filter = ` c.superviselevel = 1 and a.deleted = 0 `;
      if (params.street_code && params.street_code !== '') {
        filter = filter + ` and c.street_code = '${params.street_code}' `;
      }
      const sql = ` select a.id, c.id as pid, c.name as bname, a.o_name as name,c.address as daddress, 
        c.street_code, d.name as sname,c.accessstatus, c.pass_percent,a.o_address, a.o_linkman, a.o_linkphone, a.o_type, a.o_alias, a.content
        from pt_company as a 
        left join pt_map_manage_building_group as b
        on a.id = b.company_id
        left join pt_building_group as c 
        on b.building_group_id  = c.id
        left join pt_area as d
        on d.code = c.street_code
        where ${filter} and c.deleted = 0 and b.status = 2 and a.company_type&2 and a.o_type = 1
        UNION ALL
        select a.id, c.id as pid, c.name as bname, a.o_name as name,c.address as daddress, c.street_code, d.name as sname, c.accessstatus, c.pass_percent,a.o_address, a.o_linkman, a.o_linkphone, 
        a.o_type, a.o_alias, a.content
        from pt_company as a 
        left join pt_map_building_company_rent as b
        on a.id = b.company_id
        left join pt_building_group as c 
        on b.building_group_id  = c.id
        left join pt_area as d
        on d.code = c.street_code
        where ${filter} and c.deleted = 0 and a.company_type&8 and a.o_type = 1 GROUP BY a.id`;
      let res;
      if (think.isEmpty(allImportCompany)) {
        res = await think.model('company').query(sql);
        for (const i of res) {
          const info = await think.model('projectdev').field('id, systemid').where(` pid = ${i.pid} `).group('systemid').select();
          const buildInfo = await think.model('building_group').where(` id = ${i.pid} `).find();
          const level = await think.model('supervise_facilities_equipment').alias('a').field('a.*, b.typename').join({
            table: 'system_type',
            join: 'left',
            as: 'b',
            on: ['a.system_id', 'b.id']
          }).where(` level_id = ${buildInfo.regulatory_level} `).group('system_id').select();
          const serverinfo = await think.model('map_service_building_group').where(` stop_status = 1 and building_group_id = ${i.pid} `).find();
          let serverInfos = 0;
          // 接入设备数
          const projectdevNum = await think.model('projectdev').field('id').where(` pid = ${i.pid} `).select();
          // 判断 1:全部接入  0:部分接入 2:停止接入 否则未接入
          const systemId = [];
          const arrA = [];
          const arrB = [];
          for (const i of level) {
            arrA.push(i.system_id);
          }
          for (const i of info) {
            arrB.push(i.systemid);
          }
          level.forEach(item => {
            if (arrA.includes(item.system_id) && !arrB.includes(item.system_id)) {
              systemId.push(item);
            }
          });
          const systemName = [];
          for (const i of systemId) {
            systemName.push(i.typename);
          };
          if (level.length <= info.length) {
            i['partStatus'] = 1;
            i['projectdevNum'] = projectdevNum.length; // 接入设备数
            i['systemId'] = 0;
            i['noAccess'] = []; // 未接入系统名称&未接入系统数
          } else {
            i['partStatus'] = 0;
            i['systemId'] = level.length - info.length;
            i['projectdevNum'] = projectdevNum.length;
            i['noAccess'] = systemName.join(',');
          }
          if (info.length === 0) {
            i['accessstatus'] = 0;
            i['partStatus'] = 3;
            i['projectdevNum'] = 0;
            i['noAccess'] = systemName.join(',');
            i['systemId'] = level.length - info.length;
          }
          if (serverinfo.id) {
            const infos = await think.model('map_service_building_group').where(` building_group_id = ${i.pid} `).select();
            for (const j of infos) {
              serverInfos += j.stop_status;
            }
            if (serverInfos === infos.length) {
              i['partStatus'] = 2;
            }
          }
        }
        await think.cache(`${user.username}_allImportCompany`, `'${JSON.stringify(res)}'`, { timeout: 24 * 60 * 60 * 1000 });
      } else {
        const str = allImportCompany.substring(1, allImportCompany.length - 1);
        res = JSON.parse(str);
      }
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 查询街道
  async getStreetAction() {
    try {
      let streetCode;
      if (config.server_env === 'jingan') {
        streetCode = '310106000000';
      } else if (config.server_env === 'huangpu') {
        streetCode = '310101000000';
      } else {
        streetCode = '310106000000';
      }
      const res = await think.model('area').where({parent_code: streetCode}).select();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 报警事件查询（查询条件 时间和 pid）
  async alarmEventStatisticeAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.alarmEventStatistice(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 督查结果统计
  async supervisionResultAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.supervisionResult(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 督查单查询（查询条件 时间和 pid）
  async supervisionStatisticeAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.supervisionStatistice(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 报警事件类型分类统计（查询条件 时间和 pid）
  async alarmEventTypeStatisticeAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.alarmEventTypeStatistice(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 基础事件统计
  async basicEventTypeStatisticeAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.basicEventTypeStatistice(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 设备登记单位
  async equipmentRegistrationAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.equipmentRegistration(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 根据时间查询已企业认证单位
  async enterpriseCertificationAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.enterpriseCertification(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 根据时间筛选出已备案建筑
  async buildingRecordAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.buildingRecord(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心周报
  async dataWeeklyAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.dataWeekly(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心周报(报警事件数)
  async weeklyEventNumAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.weeklyEventNum(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心周报(人工督察未整改)
  async artificialTaskAction() {
    try {
      const params = this.getParamOrPost();
      const res = await buildingGroupService.artificialTask(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心月报(报警事件统计)
  async basicEventFormAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsModel.getBasicEventForm(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心月报(报警原因统计)
  async basicEventReasonAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsModel.getBasicEventReason(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心月报(系统24小时报警曲线)
  async toDayEventAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsModel.getToDayEvent(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心年报(街道督办次数)
  async streetSheetAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsModel.getStreetSheet(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心年报(督办单位类型统计)
  async companyTypeSheetAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsModel.getCompanyTypeSheet(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 数据中心年报(督办次数统计)
  async sheetTotalAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsModel.getSheetTotal(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {get} /admin/statistics/homePageSt  首页 统计建筑数、检测点位数、处理风险事件
   * @apiName /admin/statistics/homePageSt
   * @apiSuccess {Object} data 查询的数据结果
   */
  async homePageStAction() {
    try {
      const ret = { buildNum: 0, positionNum: 0, riskNum: 0 };
      let basicEventLog = 0;
      const yearTime = [
        '202001', '202002', '202003', '202004', '202005', '202006', '202007', '202008', '202009', '202010', '202011', '202012', 
        '202101', '202102', '202103', '202104', '202105', '202106', '202107', '202108', '202109', '202110', '202111', '202112',
        '202201', '202202', '202203', '202204', '202205', '202206', '202207', '202208', '202209', '202210', '202211', '202212'
      ];
      for (const i in yearTime) {
        const count = await think.mongo('basic_event_log_' + yearTime[i], 'mongo').count();
        basicEventLog += count;
      };
      const eventNums = await think.model('basic_event').where(` status = 0 `).count();
      // 全部的电子督察单
      const taskLogNum = await think.mongo('task_log', 'mongo').where({projectid: { '$ne': 0 }}).count();
      const filteres = ` type in (1,2,3,4) and projectid!=0 `;
      const taskNums = await this.model('task').where(filteres).count();
      // 全部的人工督察单
      const allSheetNum = await think.model('supervise_sheet_artificial').count();
      ret.riskNum = basicEventLog + eventNums + taskNums + taskLogNum + allSheetNum;

      // ret.buildNum = await think.model('building').where(` deleted = 0 `).count();
      ret.buildNum = await think.model('building_group').alias('a').join({
        table: 'building',
        join: 'left',
        as: 'b',
        on: ['a.id', 'b.building_group_id']
      }).where(` a.deleted = 0 and a.accessstatus = 1 and b.deleted = 0 and b.b_name !='周边环境' `).select();
      ret.buildNum = ret.buildNum.length;

      const proNum = await think.model('projectdev').where(` isdelete = 0 `).count();
      let num = await think.model('db_firesystem').field(` max(val) as val `).find();
      num = parseInt(num.val);
      let posNum = 0;
      while (num >= 1) {
        const count = await think.model(`firesystem_position_${num}`).count();
        posNum += count;
        num--;
      }
      ret.positionNum = proNum + posNum;
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async companyAccessQualityAction() {
    try {
      const sql = ` select a.id, c.id as pid, c.name as bname, a.o_name as name,c.address as daddress, c.street_code, d.name as sname,c.accessstatus, c.pass_percent
      from pt_company as a 
      left join pt_map_manage_building_group as b
      on a.id = b.company_id
      left join pt_building_group as c 
      on b.building_group_id  = c.id
      left join pt_area as d
      on d.code = c.street_code
      where a.deleted = 0 and a.company_type&2 and c.superviselevel = 1 and b.status = 2 and c.deleted = 0 and a.o_type = 1
      UNION ALL
      select a.id, c.id as pid, c.name as bname, a.o_name as name,c.address as daddress, c.street_code, d.name as sname, c.accessstatus, c.pass_percent
      from pt_company as a 
      left join pt_map_building_company_rent as b
      on a.id = b.company_id
      left join pt_building_group as c 
      on b.building_group_id  = c.id
      left join pt_area as d
      on d.code = c.street_code
      where a.deleted = 0 and a.company_type&8 and c.superviselevel = 1 and c.deleted = 0 and a.o_type = 1 GROUP BY a.id`;
      const ret = await think.model('company').query(sql);
      console.log(ret.length);
      for (const i of ret) {
        // 小于30
        const passPercent = i.pass_percent ? parseInt(i.pass_percent) : 0;
        if (passPercent < 30) {
          i['partCount'] = 0;
        } else if (passPercent >= 30 && passPercent < 50) {
          i['partCount'] = 1;
        } else if (passPercent >= 50 && passPercent < 70) {
          i['partCount'] = 2;
        } else if (passPercent >= 70 && passPercent < 90) {
          i['partCount'] = 3;
        } else if (passPercent >= 90) {
          i['partCount'] = 4;
        }
      }
      let streetCode = '';
      if (config.server_env === 'jingan') {
        streetCode = '310106000000';
      } else if (config.server_env === 'huangpu') {
        streetCode = '310101000000';
      } else {
        streetCode = '310106000000';
      }
      const streetInfo = await think.model('area').field('code, name').where(` parent_code = ${streetCode} `).select();
      for (const i of streetInfo) {
        const deleteData = ret.filter(j => j.street_code === i.code);
        // 平均达标率
        let sumCompliance = 0;
        for (const j of deleteData) {
          const num = j.pass_percent ? parseFloat(j.pass_percent) : 0;
          sumCompliance += num;
          const avgRate = sumCompliance / deleteData.length;
          const partRate = deleteData.filter(l => l.partCount === 0);
          const partRate2 = deleteData.filter(l => l.partCount === 1);
          const partRate3 = deleteData.filter(l => l.partCount === 2);
          const partRate4 = deleteData.filter(l => l.partCount === 3);
          const partRate5 = deleteData.filter(l => l.partCount === 4);
          i['allCount'] = deleteData.length;
          i['partRate30'] = partRate.length;
          i['partRate50'] = partRate2.length;
          i['partRate70'] = partRate3.length;
          i['partRate90'] = partRate4.length;
          i['partRates'] = partRate5.length;
          i['avgRate'] = avgRate.toFixed(2);
        }
      };
      return this.success(streetInfo);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 数据中心周报
  async getMonthlyReportAction() {
    try {
      // 获取当前月份
      const params = this.getParamOrPost();
      let nows;
      let Number;
      let years;
      if (params.times) {
        nows = new Date();
        const timesc = moment(params.times).format('x');
        years = moment(params.times).get('year');
        nows.setTime(timesc);
        Number = think.getNumber(parseInt(timesc));
        console.log('Number', Number);
      } else {
        nows = new Date();
        years = moment(nows).get('year');
        Number = think.getNumber(parseInt(nows.getTime()));
      }
      const month = nows.getMonth();
      const createAt = moment().set('year', years).set('month', month).startOf('month').format('YYYY-MM-DD HH:mm:ss');
      const createEnd = moment().set('year', years).set('month', month).endOf('month').format('YYYY-MM-DD HH:mm:ss');
      const createAtTimes = moment(createAt).unix();
      const createEndTimes = moment(createEnd).unix();
      // 当前月份的报警事件数和处理数和处理率
      // 待处理的报警事件数
      const pendingEventCount = await think.model('basic_event').field('sum(frequency) as count').where(` record_type != 1 and status = 0 and created_at between ${createAtTimes} and ${createEndTimes} `).find();
      // 全部报警事件
      const basciEventCount = await think.model('daily_count').field('sum(`value`) as count').where(` date between '${createAt}' and '${createEnd}' and type = 1 `).find();
      // 已处理报警事件
      const alardyEventCount = basciEventCount.count - pendingEventCount.count;
      // 报警事件处理率
      const eventTreatmentRate = (alardyEventCount / basciEventCount.count).toFixed(2);
      // 电子督查单待处理数
      const pendingNum = await think.model('task').field('count(1) as count').where(` type = 1 and createtime between ${createAtTimes} and ${createEndTimes} `).find();
      // 电子督查单已处理数
      const taskfilter = {
        createtime: {'$gte': parseInt(createAtTimes), '$lte': parseInt(createEndTimes)}
      };
      const alardyTaskNum = await think.mongo('task_log', 'mongo').where(taskfilter).select();

      // 电子督查单总数
      const taskNum = pendingNum.count + alardyTaskNum.length;
      // 电子督查单处理率
      const taskTreatmentRate = (alardyTaskNum.length / taskNum).toFixed(2);

      // 报警单位数
      const alarmCompanyNum = await think.model('supervise_sheet_artificial').where(` created_at between '${createAt}' and '${createEnd}' `).count();
      // 未及时整改单位数（处理中和待处理）
      const noRectifiedCompanyNum = await think.model('supervise_sheet_artificial').where(` status in (1, 2) and created_at between '${createAt}' and '${createEnd}' `).count();
      // 生成人工督查单大于2的单位数
      const sql = ` select *, count(1) as count from pt_supervise_sheet_artificial where created_at BETWEEN '${createAt}' and '${createEnd}'and company_id != 0 GROUP BY company_id HAVING count >= 2 `;
      const noRectifiedCompanyNums = await think.model('supervise_sheet_artificial').query(sql);
      // 未及时整改的单位列表
      const sqls = ` select a.id, b.o_name as companyName, b.o_linkman, b.o_linkphone, count(1) as count from pt_supervise_sheet_artificial as a
        left join pt_company as b
        on a.company_id = b.id
        where a.created_at BETWEEN '${createAt}' and '${createEnd}' and company_id != 0 GROUP BY company_id HAVING count >= 2`;
      const noRectifiedCompanyList = await think.model('supervise_sheet_artificial').query(sqls);
      // 人工督办单
      const manualSupervisionForm = await think.model('supervise_sheet_artificial_form').field('a.*, b.o_name as company_name').alias('a').join({
        table: 'company',
        join: 'left',
        as: 'b',
        on: ['a.company_id', 'b.id']
      }).where(` a.deleted = 0 and a.updated_at BETWEEN '${createAt}' and '${createEnd}' `).select();
      // 疑似火警数
      const alardyFireNum = await think.mongo('basic_event_log_' + Number, 'mongo').where({event_type: 2}).select();
      const fireCompanyNum = await think.mongo('basic_event_log_' + Number, 'mongo').where({event_type: 2}).group('company_id').count();
      const fireNum = await think.model('basic_event').where(` event_type = 2 and created_at between '${createAt}' and '${createEnd}' `).count();
      const data = {
        month: month + 1, // 月份
        basciEventCount: {count: basciEventCount.count}, // 全部报警事件
        alardyEventCount: alardyEventCount, // 已处理报警事件
        eventTreatmentRate: eventTreatmentRate * 100, // 报警率
        taskNum: {count: taskNum}, // 全部电子督查单
        alardyTaskNum: alardyTaskNum.length, // 已经处理的电子督查单
        taskTreatmentRate: taskTreatmentRate * 100, // 处理率eventStatistics
        alarmCompanyNum, // 发生报警的单位
        noRectifiedCompanyNum, // 未及时整改的单位
        noRectifiedCompanyNums: noRectifiedCompanyNums.length, // 2次以上未及时整改单位
        noRectifiedCompanyList, // 未整改列表
        manualSupervisionForm, // 人工督办单和数量
        allFireNum: alardyFireNum.length + fireNum, // 疑似火警
        fireCompanyNum // 涉及单位数
      };
      return this.success(data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err);
    }
  }

  // 超时未整改单位排名
  async notRectifiedCompanyAction() {
    try {
      const params = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const res = await statisticsService.notRectifiedCompany(params, rangeGroup);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 单位建筑群当前存在问题列表
  async buildingProblemRankingAction() {
    try {
      const param = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      if (param.usenature.length > 0) {
        const usenatureLen = param.usenature.join(',');
        const pid = await this.getUsenatureByPid(usenatureLen);
        param.pid = pid;
      }
      const ret = await statisticsService.buildingProblemRanking(param, rangeGroup);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async buildingProblemRankingTotalAction() {
    try {
      const param = this.getParamOrPost();
      if (param.usenature.length > 0) {
        const usenatureLen = param.usenature.join(',');
        const pid = await this.getUsenatureByPid(usenatureLen);
        param.pid = pid;
      }
      const ret = await statisticsService.buildingProblemRankingTotal(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 历史风险事件排行
  async equipmentExceptionsAction(params) {
    try {
      const param = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const ret = await statisticsService.equipmentExceptions(param, rangeGroup);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 督查单次数
  async electronSupervisionsAction() {
    try {
      const params = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const res = await statisticsService.electronSupervisions(params, rangeGroup);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 人工督查单生成次数
  async artificialSupervisionsAction() {
    try {
      const params = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const res = await statisticsService.artificialSupervisions(params, rangeGroup);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 平均响应时间
  async replyAvgAction() {
    try {
      const params = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const res = await statisticsService.replyAvg(params, rangeGroup);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // 获取事件类型
  async basicEventTplAction() {
    try {
      const res = await think.model('basic_event_tpl').select();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get} /admin/statistics/suspectedFire 疑似火警排名
   * @apiGroup statistics
   * @apiParam {string} type 排序类型 1：正序 2： 倒序
   * @apiParam {string} time 按时间查询 1.今日情况 2.最近一周 3.最近一月 4.最近一年
   * @apiParam {string} begin_time 开始日期
   * @apiParam {string} end_time 结束日期
   * @apiParam {string} building_group_name 建筑群名称
   * @apiParam {number} _page 页码
   * @apiParam {number} _limit 条数
   * @apiSuccess {Object} data  返回列表数据
   * **/
  async suspectedFireAction() {
    try {
      const params = this.getParamOrPost();
      const uid = (await this.isLogin()).uid;
      const rangeGroup = await this.getRangeGroup(uid);
      const res = await statisticsService.suspectedFire(params, rangeGroup);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get} /admin/statistics/hpPassengerFlowList 黄浦实时客流排名
   * @apiGroup hpstatistics
   * @apiSuccess {string} spotname  区域名称
   * @apiSuccess {number} realtimenumber  实时人流量
   * @apiSuccess {Object} data  返回列表数据
   * **/
  async hpPassengerFlowListAction() {
    try {
      const res = await statisticsService.hpPassengerFlowList();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get} /admin/statistics/hpPassengerFlowTop 黄浦实时客流峰值
   * @apiGroup hpstatistics
   * @apiSuccess {Array} name  区域名称
   * @apiSuccess {Array} newArry  列表数据
   * **/
  async hpPassengerFlowTopAction() {
    try {
      const res = await statisticsService.hpPassengerFlowTop();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get} /admin/statistics/hpTrendPeople 人流趋势
   * @apiGroup hpstatistics
   * @apiParam {string} time 筛选日期
   * @apiParam {string} type 区域筛选 WAITAN：外滩、YUYUAN：豫园、XINTIANDI：新天地、TIANZIFANG：田子坊
   * @apiSuccess {Array} timeArray  时间轴
   * @apiSuccess {Array} dataArray  列表数据
   * **/
  async hpTrendPeopleAction() {
    try {
      const params = this.getParamOrPost();
      const res = await statisticsService.hpTrendPeople(params);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get}  /admin/statistics/hpGetAllRegion 全部区域
   * @apiGroup hpstatistics
   * @apiSuccess {Array} spotcpde  code
   * @apiSuccess {Array} spotname  name
   * **/
  async hpGetAllRegionAction() {
    try {
      const res = await statisticsService.hpGetAllRegion();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get}  /admin/statistics/hpAddRegionPoint 绘制区域围栏
   * @apiGroup hpstatistics
   * @apiParam {string} spotcode 区域code
   * @apiParam {string} type 0高德地图，1黄浦区内网 默认 0
   * @apiParam {string} point 线条经纬度集合
   * @apiSuccess {data} data
   * **/
  async hpAddRegionPointAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/map').hpAddRegionPoint(params);
      if (res.status === -1) return this.fail(-1, res.msg);
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get}  /admin/statistics/hpGetRegionPoint 获取区域围栏
   * @apiGroup hpstatistics
   * @apiParam {string} spotcode 区域code
   * @apiParam {string} type 0高德地图，1黄浦区内网 默认 0高德地图
   * @apiSuccess {string} point 线条经纬度集合
   * @apiSuccess {data} data
   * **/
  async hpGetRegionPointAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/map').hpGetRegionPoint(params);
      if (res.status === -1) return this.fail(-1, res.msg);
      return this.success(res.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get}  /admin/statistics/hpHeatChart hp热力图
   * @apiGroup hpstatistics
   * @apiParam {string} type 0: 2D热力图  1:3D热力图
   * @apiSuccess {string} point 线条经纬度集合
   * @apiSuccess {data} data
   * **/
  async hpHeatChartAction() {
    try {
      const params = this.getParamOrPost();
      const res = await this.model('admin/map').hpHeatChart(params);
      if (res.status === -1) return this.fail(-1, res.msg);
      return this.success(res.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get}  /admin/statistics/getBuildingByStreetStatistic 街道建筑群接入统计
   * @apiGroup statistics
   * @apiSuccess {number} buildingTotal 建筑群总数
   * @apiSuccess {number} connectedBuildingCount 已接入
   * @apiSuccess {number} stopBuildingCount 停止接入
   * @apiSuccess {number} fullAccessCount 全部接入
   * @apiSuccess {number} partialAccessCount 部分接入
   * @apiSuccess {number} noAccessBuildingCount 未接入
   * @apiSuccess {data} data
   * **/
  async getBuildingByStreetStatisticAction() {
    try {
      const user = await this.isLogin();
      const res = await statisticsService.getBuildingByStreetStatistic(user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async statisticsBasicNumAction() {
    try {
      // const data = await think.mongo('basic_event_log_202012', 'mongo').field('project_id, count(*) as count').group('project_id').select();
      const data = await think.mongo('basic_event_log_202012', 'mongo').aggregate([
        {
          $project: {
            _id: 0,
            project_id: 1,
            count: 1
          }
        },
        {
          $group: {
            _id: {project_id: '$project_id'},
            project_id: {$first: '$project_id'},
            count: {$sum: 1}
          }
        }
      ]);
      const count = [];
      for (let i = 1; i <= 26; i++) {
        const buildData = await think.model('building_group').where(` usenature = ${i} `).select();
        const buildId = [];
        let num = 0;
        for (const i of buildData) {
          buildId.push(i.id);
        };
        for (const i of data) {
          if (buildId.includes(i.project_id)) {
            num += i.count;
          }
        }
        count.push(num);
      }

      return this.success(count);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async resetDataAction() {
    try {
      const sql = ` select * from pt_building_group where id not in (
		  select id from pt_building_group where usenature != '' ) `;
      const data = await think.model('building_group').query(sql);
      let res;
      for (const i of data) {
        const buildData = await think.model('building').where(` building_group_id = ${i.id} and use_kind != ''`).find();
        if (!think.isEmpty(buildData)) {
          res = await think.model('building_group').where(` id =${i.id} `).update({usenature: buildData.use_kind});
        } else {
          res = await think.model('building_group').where(` id =${i.id} `).update({usenature: 13});
        }
      }
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get}  /admin/statistics/eventData
   * @apiGroup statistics
   * @apiParam {string} start 开始时间
   * @apiParam {string} end 结束时间
   * @apiParam {string} eventType 事件类型
   * @apiParam {number} searchType 搜索模式 1=按月 2=按日
   * @apiParam {number} groupby 分组模式 0=不分组  1=事件分类 2=事件类型
   * @apiSuccess {data} data
   * @desc 文档https://doc.119.net/web/#/2?page_id=632
   * **/
  async eventDataAction() {
    const params = this.getParamOrPost();
    let sql = 'select sum(value) as value ,b.category_type_id as type,a.event_type';
    let searchType = params['searchType'] || 1;
    let groupby = params['groupby'] || 0;
    let start = params['start'];
    let end = params['end'];
    const eventType = params['eventType'];

    searchType = searchType - 0;
    groupby = groupby - 0;

    if (!start) {
      start = moment().format('YYYY-MM-DD');
    } else {
      start = moment(start).format('YYYY-MM-01');
    }

    if (!end) {
      if (searchType === 1) {
        end = moment(start).subtract(12, 'months').format('YYYY-MM-DD');
      } else {
        end = moment(start).subtract(30, 'days').format('YYYY-MM-DD');
      }
    } else {
      end = moment(end).endOf('month').format('YYYY-MM-DD');
    }

    let where = ' where date between "' + start + '" and "' + end + '"';
    if (eventType) {
      where += ' and b.category_type_id in (' + eventType + ')';
    }

    if (groupby === 2) {
      where += ' and type=1';
    }

    if (searchType === 1) {
      sql += ',DATE_FORMAT(date,"%Y-%m") as datefm from pt_daily_count a';
    } else {
      sql += ',DATE_FORMAT(date,"%Y-%m-%d") as datefm from pt_daily_count a';
    }

    sql += ' inner join pt_event_category_relation b on a.event_type=b.eid ' + where +
        ' group by event_type,datefm,b.category_type_id order by datefm';

    let data = await think.model('daily_count').query(sql);

    // 数据组合
    const result = {};
    data.forEach(function (d) {
      const key = d.type + '-' + d.datefm;
      if (!result.hasOwnProperty(key)) {
        delete d.event_type;
        d.date = d.datefm;
        delete d.datefm;
        result[key] = d;
      } else {
        result[key].value += d.value;
      }
    });

    data = [];
    for (const key in result) {
      data.push(result[key]);
    }

    return this.success(data);
  }

  /**
   * @api {get}  admin/statistics/alarmbypidandsid
   * @apiGroup statistics
   * @apiParam {Number} pid 建筑群id
   * @apiParam {number} sid 系统id
   * @apiParam {number} type 搜索模式 1: 7天 2:30天 3：12个月
   * @apiSuccess {data} data
   * **/
   async alarmbypidandsidAction() {
    try {
      const param = this.getParamOrPost();
      let ret = await buildingGroupService.alarmbypidandsid(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
/** 园区基础信息
 * @api {get}  admin/statistics/buildingBasicInfo
 * @apiGroup statistics
 * @apiSuccess {data} data
 * **/
 async buildingBasicInfoAction() {
  try {
    const param = this.getParamOrPost();
    let ret = await statisticsModel.buildingBasicInfo(param);
    return this.success(ret);
  } catch (err) {
    think.logger.info(err);
    return this.fail(-1, err.message);
  }
}
  /** 园区类型分布
 * @api {get}  admin/statistics/buildingTagInfo
 * @apiGroup statistics
 * @apiSuccess {data} data
 * **/
   async buildingTagInfoAction() {
    try {
      const param = this.getParamOrPost();
      let ret = await statisticsModel.buildingTagInfo(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**  消防安全管理
 * @api {get}  admin/statistics/safetyManage
 * @apiGroup statistics
 * @apiSuccess {data} data
 * **/
   async safetyManageAction() {
    try {
      const param = this.getParamOrPost();
      let ret = await statisticsModel.safetyManage(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**  园区概要数据
 * @api {get}  admin/statistics/outlineInfo
 * @apiGroup statistics
 * @apiSuccess {data} data
 * **/
   async outlineInfoAction() {
    try {
      const param = this.getParamOrPost();
      let ret = await statisticsModel.outlineInfo(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**  安全码排行榜
 * @api {get}  admin/statistics/safetycodeRank
 * @apiGroup statistics
 * @apiSuccess {data} data
 * **/
   async safetycodeRankAction() {
    try {
      const param = this.getParamOrPost();
      let ret = await statisticsModel.safetycodeRank(param);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
