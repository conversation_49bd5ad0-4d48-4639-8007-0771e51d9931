const Base = require('../../../base.js');

module.exports = class extends Base {
  /**
   * @api {get} admin/project/3d/totalSearch/ranking  排名
   * @apiDescription 排名
   * @apiName admin/project/3d/totalSearch/ranking
   * @apiGroup 3d
   * @apiParam {Number} pid 项目id
   * @apiParam {Number} value 1:维修次数排名 2:火警次数排名 3:故障次数排名 4:隐患次数排名
   * @apiSuccess {Object} data  查询的数据结果
   */
  async rankingAction() {
    const params = this.get();
    params.value = parseInt(params.value);
    let type = '';
    switch (params.value) {
      case 1:
        type = 'repair';
        break;
      case 2:
        type = 'fire';
        break;
      case 3:
        type = 'fault';
        break;
      case 4:
        type = 'modbus';
        break;
      case 5:
        type = 'shield';
        break;
      default:
        type = '';
    }
    params.type = type;
    const res = await this.model('admin/3d/totalSearch').frequentpoint(params);

    // const res = await this.model('project/index').rankingData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} admin/project/3d/totalSearch/alarmnumber  首页总数
   * @apiDescription 首页总数
   * @apiName admin/project/3d/totalSearch/alarmnumber
   * @apiGroup  3d
   * @apiParam {Number} pid 项目id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async alarmnumberAction() {
    const params = this.get();
    params.action = 'all';
    const res = await this.model('admin/3d/totalSearch').alarmNumberDataNew(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} admin/project/3d/totalSearch/alarmnumberday  报警日期趋势
   * @apiDescription   报警日期趋势
   * @apiName admin/project/3d/totalSearch/alarmnumberday
   * @apiGroup 3d
   * @apiParam {Number} pid 项目id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async alarmnumberdayAction() {
    const params = this.get();
    const res = await this.model('admin/3d/totalSearch').oldLineData(params.pid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} admin/project/3d/totalSearch/alarmnumberlog  报警月度趋势
   * @apiDescription 报警月度趋势
   * @apiName admin/project/3d/totalSearch/alarmnumberlog
   * @apiGroup 3d
   * @apiParam {Number} pid 项目id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async alarmnumberlogAction() {
    const params = this.get();
    const res = await this.model('admin/3d/totalSearch').alarmLineChart(params.pid, 'year');
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} admin/project/3d/totalSearch/getBuildDetail 建筑物仪表盘显示信息
   * @apiDescription 建筑物仪表盘显示信息
   * @apiName admin/project/3d/totalSearch/getBuildDetail
   * @apiGroup 3d
   * @apiParam {Number} pid 项目id
   * @apiParam {Number} bid 建筑物id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async getBuildDetailAction() {
    try {
      const {pid, bid} = this.get();
      const res = await this.model('admin/3d/totalSearch').getBuildDetail(bid, pid);
      this.success(res);
    } catch (err) {
      this.fail(-1, err.message);
    }
  }
  /**
   * @api {get} admin/project/3d/totalSearch/getFloorDetail 建筑物楼层仪表盘显示信息
   * @apiDescription 建筑物楼层仪表盘显示信息
   * @apiName admin/project/3d/totalSearch/getFloorDetail
   * @apiGroup 3d
   * @apiParam {Number} fid 楼层id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async getFloorDetailAction() {
    try {
      const {fid} = this.get();
      const res = await this.model('admin/3d/totalSearch').getFloorDetail(fid);
      this.success(res);
    } catch (err) {
      this.fail(-1, err.message);
    }
  }
  /**
   * @api {get} admin/project/3d/totalSearch/getReportFloor 获取报警楼层
   * @apiDescription 获取报警楼层
   * @apiName admin/project/3d/totalSearch/getReportFloor
   * @apiGroup 3d
   * @apiParam {Number} bid 建筑物id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async getReportFloorAction() {
    try {
      const {bid, pid} = this.get();
      const res = await this.model('admin/3d/totalSearch').getReportFloor(bid, pid);
      this.success(res);
    } catch (err) {
      this.fail(-1, err.message);
    }
  }
};
