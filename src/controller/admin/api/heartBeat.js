const Base = require('../../base.js');

module.exports = class extends Base {
  /**
   * @api {post,get} /admin/api/heartBeat/heartTest 测试程序是否稳定运行
   * @apiName /api/heartBeat/heartTest
   * @apiGroup Api
   */
  async heartTestAction() {
    const res = await this.model('admin/heartBeat').heartTest();
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success('', 'OK');
  }
};
