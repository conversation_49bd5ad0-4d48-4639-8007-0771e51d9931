const Base = require('../../base.js');
const lawEnforcementModel = think.model('admin/onlyinspector/lawEnforcement');

module.exports = class extends Base {
  // async __before() {
  //   await this.setModelBase(this.model('admin/onlyinspector/lawEnforcement'));
  // }
  async getListAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await equipmentModel.lawEnforcementList(param);
      if (ret.status === -1) return this.fail(-1, ret.msg);
      return this.success(ret.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async addAction() {
    try {
      const param = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const ret = await equipmentModel.lawEnforcementAdd(param, adminInfo);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
