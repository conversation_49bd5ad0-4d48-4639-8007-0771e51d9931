/**
 * 警情推送配置
 */

const Base = require('../../base.js');
const Err = require('../../../common/errorCode');

module.exports = class extends Base {
    /**
     * @api {get} /admin/alarmnotice/config/list   警情推送配置列表
     * @apiGroup MenuRule
     * @apiParam page 页码
     * @apiParam limit 每页数量
     * @apiSuccess {Array} data
     * @apiDescription 警情推送配置列表
     */
    async listAction() {
        try {
            let params = this.getParamOrPost();

            const ret = await this.model('admin/alarmnotice/config').list(params);

            return this.success(ret.data, ret.msg);
        } catch (err) {
            think.logger.info(err);
            return this.fail(err.code, err.msg);
        }
    }

    /**
     * @api {get} /admin/alarmnotice/config/get   获取警情推送配置
     * @apiGroup MenuRule
     * @apiParam {String} id id
     * @apiSuccess {Array} data
     * @apiDescription 获取警情推送配置
     */
    async getAction() {
        try {
            let params = this.getParamOrPost();

            const ret = await this.model('admin/alarmnotice/config').get(params);

            return this.success(ret.data, ret.msg);
        } catch (err) {
            think.logger.info(err);
            return this.fail(err.code, err.msg);
        }
    }

    /**
     * @api {post} /admin/alarmnotice/config/add   添加警情推送配置
     * @apiGroup MenuRule
     * @apiParam {String} name 名称
     * @apiParam {String} department_ids 部门id
     * @apiParam {String} area_codes 区域id
     * @apiParam {String} user_ids 用户id
     * @apiParam {String} remark 备注
     * @apiSuccess {Array} data
     * @apiDescription 添加警情推送配置
     */
    async addAction() {
        try {
            const param = this.post();
            const uid = (await this.isLogin()).uid;

            if (uid != 0) {
                param.uid = uid;
            }

            const ret = await this.model('admin/alarmnotice/config').add(param);

            return this.success(ret.data, ret.msg);
        } catch (err) {
            think.logger.info(err);
            return this.fail(err.code, err.msg);
        }
    }

    /**
     * @api {post} /admin/alarmnotice/config/edit   修改警情推送配置
     * @apiGroup MenuRule
     * @apiParam {String} id id
     * @apiParam {String} name 名称
     * @apiParam {String} department_ids 部门id
     * @apiParam {String} area_codes 区域id
     * @apiParam {String} user_ids 用户id
     * @apiParam {String} remark 备注
     * @apiSuccess {Array} data
     * @apiDescription 修改警情推送配置
     */
    async editAction() {
        try {
            const param = this.post();
            const uid = (await this.isLogin()).uid;

            if (uid != 0) {
                param.uid = uid;
            }

            const ret = await this.model('admin/alarmnotice/config').edit(param);

            return this.success(ret.data, ret.msg);
        } catch (err) {
            think.logger.info(err);
            return this.fail(err.code, err.msg);
        }
    }

    /**
     * @api {post} /admin/alarmnotice/config/delete   删除警情推送配置
     * @apiGroup MenuRule
     * @apiParam {String} id id
     * @apiSuccess {Array} data
     * @apiDescription 删除警情推送配置
     */
    async deleteAction() {
        try {
            const param = this.post();
            const uid = (await this.isLogin()).uid;

            if (uid != 0) {
                param.uid = uid;
            }

            const ret = await this.model('admin/alarmnotice/config').delete(param);

            return this.success(ret.data, ret.msg);
        } catch (err) {
            think.logger.info(err);
            return this.fail(err.code, err.msg);
        }
    }

};
