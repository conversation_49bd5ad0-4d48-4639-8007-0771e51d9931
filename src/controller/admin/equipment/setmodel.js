/**
 * auther :javen
 */
const Base = require('../../base.js');
module.exports = class extends Base {
  /**
   * @api {get} /equipment/setmodel/list 设备模型列表查询
   * @apiDescription 标准列表功能接口，直接动态搜索，动态字段，动态编辑，动态
   * @apiName  /equipment/setmodel/list
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} _page  分页数，默认1
   * @apiParam {Number} _limit    每页条数，默认值为10，最大不超过100条
   * @apiParam {Number} state  状态
   * @apiParam {String} [name] 名称
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    try {
      const params = this.get();
      const res = await this.model('equipment/setmodel').getListDate(params);
      if (res.status === -1) return this.fail(-1, res.msg);
      return this.success(res.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /equipment/setmodel/addmodel 新增模型(基本信息)
   * @apiDescription 增加模型
   * @apiName /equipment/setmodel/addmodel
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} type_id  设备大类id
   * @apiParam {String} name   设备名称
   * @apiParam {Number} isline 是否无线
   * @apiParam {Number} state  状态
   * @apiParam {Number} ispro_alarm_rule 是否需要在项目上独立配置报警规则，0：否；1：是
   * @apiParam {Number} patrol_model_id 巡查模型id
   * @apiParam {String}  industry_id 行业 逗号分隔'1,2'
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addmodelAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').addSetmodel(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/editmodel 编辑模型 (基本信息)
   * @apiDescription 编辑模型
   * @apiName /equipment/setmodel/editmodel
   * @apiGroup equipmentSetmodel、
   * @apiParam {Number} id 设备模型的id
   * @apiParam {Number} [type_id]  设备大类id
   * @apiParam {String} [name]   设备名称
   * @apiParam {Number} [isline] 是否无线
   * @apiParam {Number} [state]  状态
   * @apiParam {Number} [ispro_alarm_rule] 是否需要在项目上独立配置报警规则，0：否；1：是
   * @apiParam {Number} [patrol_model_id] 巡查模型id
   * @apiParam {String} [industry_id]  行业 逗号分隔'1,2'
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editmodelAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editSetmodel(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/addattributes 增加模型属性
   * @apiDescription 增加设备属性
   * @apiName /equipment/setmodel/addattributes
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id  设备模型id
   * @apiParam {String} name   名称
   * @apiParam {Number} type  属性类型 0：数值型；1：状态型
   * @apiParam {Number} attribute_id 属性id
   * @apiParam {Number} sortnum 排序
   * @apiParam {Number} [floatdata] 小数位
   * @apiParam {String} [unitmultiple] 单位换算
   * @apiParam {String} [featuresid]  功能码id
   * @apiParam {Number} [code]  功能码
   * @apiParam {String} [rgaddress] 寄存器地址val
   * @apiParam {Number} [rgcode] 寄存器值
   * @apiParam {Number} [unitid] 单位id
   * @apiParam {String} [dataformatid] 数据格式
   * @apiParam {String} [varcharid] 字节顺序id
   * @apiParam {String} [addressat]  地址位id
   * @apiParam {String} [acqcycle]  采集周期id
   * @apiParam {float}  [accuracy] 检测精度
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addattributesAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').addAttributes(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/editattributes 编辑模型属性
   * @apiDescription 编辑设备属性
   * @apiName /equipment/setmodel/editattributes
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 模型属性id
   * @apiParam {String} [name]   名称
   * @apiParam {Number} [type]  属性类型 0：数值型；1：状态型
   * @apiParam {Number} [attribute_id] 属性id
   * @apiParam {Number} [sortnum] 排序
   * @apiParam {Number} [floatdata] 小数位
   * @apiParam {String} [unitmultiple] 单位换算
   * @apiParam {String} [featuresid]  功能码id
   * @apiParam {Number} [code]  功能码
   * @apiParam {String} [rgaddress] 寄存器地址val
   * @apiParam {Number} [rgcode] 寄存器值
   * @apiParam {Number} [unitid] 单位id
   * @apiParam {String} [dataformatid] 数据格式
   * @apiParam {String} [varcharid] 字节顺序id
   * @apiParam {String} [addressat]  地址位id
   * @apiParam {String} [acqcycle]  采集周期id
   * @apiParam {float}  [accuracy] 检测精度
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editattributesAction() {
    const params = this.post();
    const modelAttrId = params.id;
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editAttributes(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    if (params && params.model_id && modelAttrId) {
      const obj = {
        modelid: params.model_id,
        attrid: modelAttrId
      };
      const cacheObj = {action: 'model_alarm', params: obj};
      think.cleanAlarmCache(cacheObj);
    }
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/deleteattributes 删除模型属性
   * @apiDescription  删除模型属性
   * @apiName /equipment/setmodel/deleteattributes
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 模型属性id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async deleteattributesAction() {
    const params = this.get();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').deleteAttributes(params, uid);
    if (res.status === 9000) return this.fail(9000, res.msg);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/attributes 设备属性select
   * @apiDescription 当前设备下所有的设备属性
   * @apiName /equipment/setmodel/attributes
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id  设备模型id
   * @apiSuccess (成功) {Object} data  查询的数据结果
   * @apiSuccessExample 返回成功示例:
   *      data: {
   *      "code": 0,
   *      "msg": "",
   *      "data": [
   *        {
   *          "id": 257,
   *          "name": "测试设备",
   *          "type": 1,
   *          "attribute_id": 21,
   *          "unitmultiple": ""
   *        }]
   *      }
   *
   * @apiError (失败) {Object} data 对应id的设备属性信息不存在
   * @apiErrorExample 返回失败示例:
   *     data: {
   *      "code": -1,
   *      "msg": "对应id的设备属性信息不存在"
   *      }
   *
   *
   */
  async attributesAction() {
    const params = this.get();
    const res = await this.model('equipment/setmodel').getAttributesSelect(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/editaddalarm 添加和编辑单条报警策略的离线报警
   * @apiDescription 添加和编辑单条报警策略的离线报警
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} model_id  设备模型id
   * @apiParam {Number} alarm_level_id 报警等级
   * @apiParam {String} msgset 通知方式 逗号分隔
   * @apiParam {String} alarmcontent 报警内容
   */

  async editaddalarmAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editAddAlarmData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    if (params.model_id) {
      const cacheObj = {
        action: 'model_alarm',
        params: {
          modelid: params.model_id
        }
      };
      think.cleanAlarmCache(cacheObj);
    }
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/addalarm 添加单条报警策略
   * @apiDescription 添加单条报警策略
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} model_id  设备模型id
   * @apiParam {Number} event_id  基础事件Id
   * @apiParam {Number} build_type  建筑性质
   * @apiParam {Number} typeid  属性集合表id
   * @apiParam {Number} devmodel_attribute_id  设备模型的属性id，设备模型属性表
   * @apiParam {Number} symbol_id  符号id
   * @apiParam {Number} event_value  报警值，数值型存数字，状态型存属性值的code
   * @apiParam {Number} event_content  报警内容
   * @apiParam {Number} legal_id  法规条例（多选）
   * @apiParam {Number} event_state  启用状态
   * @apiParam {Number} only_record  仅记录事件（0：否，1：是）
   * @apiParam {Number} from  来自设备事件还是模型事件 device：设备事件 model：设备事件
   */

  async addalarmAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const table = params.from === 'device' ? 'device_event_rule' : 'model_event_rule';
    const res = await this.model('equipment/setmodel').addAlarmData(params, uid, table);
    if (res.status === -1) return this.fail(-1, res.msg);
    // 模型删除缓存
    let attributIid = 0;
    if (parseInt(params.typeid) === 113) {
      attributIid = 113;
    } else {
      attributIid = params.devmodel_attribute_id;
    }
    if (params.model_id && attributIid) {
      const proObj = {
        action: 'model_alarm',
        params: {modelid: params.model_id, attrid: attributIid}
      };
      think.cleanAlarmCache(proObj);
    }
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/editalarm 编辑单条报警策略
   * @apiDescription 添加单条报警策略
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 报警策略的id
   * @apiParam {Number} model_id  设备模型id
   * @apiParam {Number} event_id  基础事件Id
   * @apiParam {Number} build_type  建筑性质
   * @apiParam {Number} typeid  属性集合表id
   * @apiParam {Number} type
   * @apiParam {Number} devmodel_attribute_id  设备模型的属性id，设备模型属性表
   * @apiParam {Number} symbol_id  符号id
   * @apiParam {Number} event_value  报警值，数值型存数字，状态型存属性值的code
   * @apiParam {Number} event_state  启用状态
   * @apiParam {Number} event_content  报警内容
   * @apiParam {Number} legal_id  法规条例（多选）
   * @apiParam {Number} only_record  仅记录事件（0：否，1：是）
   * @apiParam {Number} from  来自设备事件还是模型事件 device：设备事件 model：设备事件

   */

  async editalarmAction() {
    const params = this.post();
    const uid = await this.isLogin();
    const table = params.from === 'device' ? 'device_event_rule' : 'model_event_rule';
    const res = await this.model('equipment/setmodel').editAlarmData(params, uid, table);
    if (res.status === -1) return this.fail(-1, res.msg);
    // 模型删除缓存
    let attributIid = 0;
    if (parseInt(params.typeid) === 113) {
      attributIid = 113;
    } else {
      attributIid = params.devmodel_attribute_id;
    }
    if (params.model_id && attributIid) {
      const proObj = {
        action: 'model_alarm',
        params: {modelid: params.model_id, attrid: attributIid}
      };
      think.cleanAlarmCache(proObj);
    }
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/deletealarm 删除单条报警策略
   * @apiDescription 删除单条报警策略
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 报警策略的id
   * @apiParam {Number} model_id  设备模型id
   * @apiParam {Number} from  来自设备事件还是模型事件 device：设备事件 model：设备事件
   */

  async deletealarmAction() {
    const params = this.get();
    const uid = await this.isLogin();
    const table = params.from === 'model' ? 'model_event_rule' : 'device_event_rule';
    const res = await this.model('equipment/setmodel').deleteAlarmData(params, uid, table);
    if (res.status === -1) return this.fail(-1, res.msg);
    // 模型删除缓存
    let attributIid = 0;
    if (parseInt(params.typeid) === 113) {
      attributIid = 113;
    } else {
      attributIid = params.devmodel_attribute_id;
    }
    if (params.model_id && attributIid) {
      const proObj = {
        action: 'model_alarm',
        params: {modelid: params.model_id, attrid: attributIid}
      };
      think.cleanAlarmCache(proObj);
    }
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/eiditmodel 编辑巡查模板
   * @apiDescription 编辑设备模型巡查模板
   * @apiName /equipment/setmodel/eiditmodel
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id  设备模型id
   * @apiParam {String} [patrolrule]  巡查内容
   * @apiParam {String} [patrolrequest]  巡查周期
   * @apiParam {Number} [patrolteam] 巡查团队
   * @apiParam {Number} [patrolperiod] 巡查周期id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async eiditmodelAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editModelData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/findattrs 查询设备属性
   * @apiDescription 查询设备属性
   * @apiName /equipment/setmodel/findmodel
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 设备模型的id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async findattrsAction() {
    const params = this.get();
    const res = await this.model('equipment/setmodel').findAttrs(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/findalarm 查询报警策略
   * @apiDescription 查询报警策略
   * @apiName /equipment/setmodel/findalarm
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 设备模型的id
   * @apiParam {Number} from  来自设备事件还是模型事件 device：设备事件 model：设备事件
   * @apiSuccess {Object} data  查询的数据结果
   */
  async findalarmAction() {
    const params = this.get();
    try {
      const table = params.from === 'device' ? 'device_event_rule' : 'model_event_rule';
      const res = await this.model('equipment/devmodelvalue').findAlarmDate(params, table);
      if (res.status === -1) return this.fail(-1, res.msg);
      return this.success(res.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * @api {get} /equipment/setmodel/devicedata 获取检测装置select数据
   * @apiDescription 获取检测装置数据
   * @apiName /equipment/setmodel/devicedata
   * @apiGroup equipmentSetmodel
   * @apiSuccess {Object} data  查询的数据结果
   */
  async devicedataAction() {
    const res = await this.model('equipment/setmodel').getDevice();
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/devlist 获取设备信息select数据
   * @apiDescription 获取设备信息select数据
   * @apiName /equipment/setmodel/devlist
   * @apiGroup equipmentSetmodel
   * @apiSuccess {Object} data  查询的数据结果
   */
  async devlistAction() {
    const res = await this.model('equipment/setmodel').getdev();
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/detectiondevice 获取设备和检测装置Options
   * @apiDescription 获取设备和检测装置Options
   * @apiName /equipment/setmodel/detectiondevice
   * @apiGroup equipmentSetmodel
   * @apiSuccess {Object} data  查询的数据结果
   */
  async detectiondeviceAction() {
    const res = await this.model('equipment/setmodel').getDetectionDevice();
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/getwireddev 根据类型获取设备模型
   * @apiDescription 根据类型获取设备模型
   * @apiName /equipment/setmodel/getwireddev
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} systemid  系统类型id
   * @apiParam {Number} type  1表示有线设备 2表示报警主机 3表示无线设备
   * @apiSuccess {Object} data  查询的数据结果
   */
  async getwireddevAction() {
    const res = await this.model('equipment/setmodel').getWiredDev(this.get());
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/setmodel/findswitch 查找开关按钮配置
   * @apiDescription 查找开关按钮配置
   * @apiName /equipment/setmodel/findswitch
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 设备模型的id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async findswitchAction() {
    const params = this.get();
    const res = await this.model('equipment/setmodel').findSwitchData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/setmodel/editswitch 配置开关按钮
   * @apiDescription 配置开关按钮
   * @apiName  /equipment/setmodel/editswitch
   * @apiGroup equipmentSetmodel
   * @apiParam {Number} id 设备模型id
   * @apiParamExample {Array} data
   *  {
   *   [
   *     [id]: Number
   *     name: String  名称，
   *     model_attribute_id: Number模型属性id
   *     type: Number  属性类型 0：数值型；1：状态型
   *     attribute_id: Number 属性id,
   *     sortnum: Number 排序,
   *     featuresid: String 功能码id,
   *     rgaddress: String 寄存器地址val,
   *     dataformatid: String 数据格式,
   *     varcharid: String 字节顺序id,
   *     addressat: String 地址位id
   *   ]
   *  }
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editswitchAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editSwitchData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
 * @api {post} /equipment/setmodel/addGroup 新增组合
 * @apiDescription 新增组合
 * @apiName  /equipment/setmodel/editswitch
 * @apiParam {Number} name 组合名称
 * @apiParam {Number} pid 项目Id
 * @apiParam {Number} model_id 模型Id
 * @apiParam {Number} time_window 时间窗口
 * @apiParam {Number} state 使用状态 1=启用，0=禁用
 * @apiGroup equipmentSetmodel
 */
  async addGroupAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').addGroup(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} /equipment/setmodel/groupList 设备那里的组合报警列表
   * @apiDescription 组合列表
   * @apiParam {Number} pid 项目id
   */
  async groupListAction() {
    const params = this.get();
    const res = await this.model('equipment/setmodel').groupList(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} /equipment/setmodel/groupListModel 模型那里的组合规则列表
   * @apiDescription 组合列表
   * @apiParam {Number} model_id 模型id
   */
  async groupListModelAction() {
    const params = this.get();
    const res = await this.model('equipment/setmodel').groupListModel(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
 * @api {post} /equipment/setmodel/editGroup 修改组合
 * @apiDescription 修改组合
 * @apiName  /equipment/setmodel/editGroup
 * @apiParam {Number} id 组合id
 * @apiParam {Number} name 组合名称
 * @apiParam {Number} pid 项目Id
 * @apiParam {Number} model_id 模型Id
 * @apiParam {Number} time_window 时间窗口
 * @apiParam {Number} state 使用状态 1=启用，0=禁用
 */
  async editGroupAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editGroup(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} /equipment/setmodel/groupElList 组合规则列表
   * @apiDescription 组合列表
   * @apiName  /equipment/setmodel/editswitch
   * @apiParam {Number} model_id 模型id
   * @apiParam {Number} group_id 模型组合规则id
   * @apiParam {Number} pid
   * @apiParam {Number} devmodel_attribute_id
   */
  async groupElListAction() {
    const params = this.get();
    const res = await this.model('equipment/setmodel').groupElList(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
 * @api {post} /equipment/setmodel/addGroupEl 新增组规则
 * @apiDescription 新增组规则
 * @apiParam {Number} model_id  模型id
 * @apiParam {Number} pid  项目id
 * @apiParam {Number} group_id  组id
 * @apiParam {Number} dev_id  设备id
 * @apiParam {Number} event_id  基础事件Id
 * @apiParam {Number} build_type  建筑性质
 * @apiParam {Number} typeid  属性集合表id
 * @apiParam {Number} devmodel_attribute_id  设备模型的属性id，设备模型属性表
 * @apiParam {Number} symbol_id  符号id
 * @apiParam {Number} event_value  报警值，数值型存数字，状态型存属性值的code
 * @apiParam {Number} event_state  启用状态
 * @apiParam {Number} event_content  报警内容
 * @apiParam {Number} legal_id  法规条例（多选）
 * @apiParam {Number} only_record  仅记录事件（0：否，1：是）
 */
  async addGroupElAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').addGroupEl(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
  * @api {post} /equipment/setmodel/editGroupEl 修改组规则

  * @apiParam {Number} id 组合规则的id
  * @apiParam {Number} model_id  设备模型id
  * @apiParam {Number} group_id  组id
  * @apiParam {Number} dev_id  设备id
   * @apiParam {Number} pid  项目id
  * @apiParam {Number} event_id  基础事件Id
  * @apiParam {Number} build_type  建筑性质
  * @apiParam {Number} typeid  属性集合表id
  * @apiParam {Number} devmodel_attribute_id  设备模型的属性id，设备模型属性表
  * @apiParam {Number} symbol_id  符号id
  * @apiParam {Number} event_value  报警值，数值型存数字，状态型存属性值的code
  * @apiParam {Number} event_state  启用状态
  * @apiParam {Number} event_content  报警内容
  * @apiParam {Number} legal_id  法规条例（多选）
  * @apiParam {Number} only_record  仅记录事件（0：否，1：是）
   * */
  async editGroupElAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editGroupEl(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {post} /equipment/setmodel/delGroupEl 删除组规则
   * @apiParam {Number} id 组合规则的id
   * */
  async delGroupElAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').delGroupEl(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} /equipment/setmodel/searchDev 设备id查找
   * @apiParam {Number} pid 项目id
   * */
  async searchDevAction() {
    const params = this.get();
    const res = await this.model('equipment/setmodel').searchDev(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} /equipment/setmodel/searchBasicType 事件类型查找
   * @apiParam {Number} pid 项目id
   * */
  async searchBasicTypeAction() {
    const res = await this.model('equipment/setmodel').searchBasicType();
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
};
