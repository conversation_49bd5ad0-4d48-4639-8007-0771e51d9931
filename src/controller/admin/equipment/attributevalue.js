const Base = require('../../base');
module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('equipment/attributevalue'));
  }
  async addAction() {
    try {
      const param = this.getParamOrPost();
      param.avid = think.uuid('v1').replace(/-/g, '');
      const valId = parseInt(param.val_id) || 0;
      const info = await think.model('value').where({id: valId}).find();
      if (think.isEmpty(info)) {
        throw new Error('属性值不存在');
      }
      const count = await think.model('attribute_value').where({typeid: param.typeid, val_id: valId}).count();
      if (count > 0) {
        throw new Error('属性值已经添加过了');
      }
      param.val_id = valId;
      param.code = info.code;
      param.name = info.name;
      await think.model('equipment/attributevalue').insertBase(param);
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  async editAction() {
    try {
      const param = this.getParamOrPost();
      const id = parseInt(param.id) || 0;
      const valId = parseInt(param.val_id) || 0;
      const valInfo = await think.model('value').where({id: valId}).find();
      if (think.isEmpty(valInfo)) {
        throw new Error('属性值不存在');
      }
      const info = await think.model('attribute_value').where({id: id}).find();
      if (think.isEmpty(info)) {
        throw new Error('记录不存在');
      }
      const count = await think.model('attribute_value').where({typeid: info.typeid, val_id: valId, id: ['!=', id]}).count();
      if (count > 0) {
        throw new Error('属性值已经添加过了');
      }
      const data = {
        val_id: valId,
        code: valInfo.code,
        name: valInfo.name
      };
      await think.model('attribute_value').where({id: id}).update(data);
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
