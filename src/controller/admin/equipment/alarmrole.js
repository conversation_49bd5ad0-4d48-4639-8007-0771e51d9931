const Base = require('../../base');
module.exports = class extends Base {
  /**
   * @api {get} /equipment/alarmrole/list 列表
   * @apiDescription 项目角色模板列表
   * @apiName /equipment/alarmrole/list
   * @apiGroup equipmentAlarmrole
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit  数量
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    const params = this.get();
    const res = await think.model('equipment/alarmrole').listData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/alarmrole/select 列表下拉框
   * @apiDescription 列表下拉框
   * @apiName /equipment/alarmrole/select
   * @apiGroup equipmentAlarmrole
   * @apiParam {String} [name] 角色名称
   * @apiSuccess {Object} data  查询的数据结果
   */
  async selectAction() {
    const params = this.get();
    const res = await this.model('equipment/alarmrole').selectData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/alarmrole/add 新增
   * @apiDescription 新增项目角色模板
   * @apiName /equipment/alarmrole/add
   * @apiGroup equipmentAlarmrole
   * @apiParam {String} name 角色名称
   * @apiParam {String} sid 系统类型id '1,2,3,4'
   * @apiParam {String} alarmid 报警级别id '1,2,3'
   * @apiParam {String} alarm_type 报警类型 '1,2,3'
   * @apiParam {String} [remark] 备注说明
   * @apiParam {Number} state 是否启用
   * @apiParam {String}  rules 权限参数 '1,2,3'
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/alarmrole').addData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {post} /equipment/alarmrole/edit 修改
   * @apiDescription 修改项目角色模板
   * @apiName /equipment/alarmrole/edit
   * @apiGroup equipmentAlarmrole
   * @apiParam {Number} id 当前id
   * @apiParam {String} name 角色名称
   * @apiParam {String} sid 系统类型id '1,2,3,4'
   * @apiParam {String} alarmid 报警级别id '1,2,3'
   * @apiParam {String} alarm_type 报警类型 '1,2,3'
   * @apiParam {String} [remark] 备注说明
   * @apiParam {Number} state 是否启用
   * @apiParam {String}  rules 权限参数 '1,2,3'
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/alarmrole').editData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
};
