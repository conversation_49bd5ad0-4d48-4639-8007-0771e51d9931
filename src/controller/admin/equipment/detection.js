/**
 * auther :javen
 */
const Base = require('../../base');
const SUCC = require('../../../common/errorCode').publicSuccess;
module.exports = class extends Base {
  /**
   * @api {get} /equipment/detection/list 检测装置模型列表数据
   * @apiDescription 获取检测装置数据
   * @apiName  /equipment/detection/list
   * @apiGroup detection
   * @apiParam {Number} _page 页数
   * @apiParam {Number} _limit 数据条数
   * @apiParam {String} [serialnumber] 编码
   * @apiParam {String} [name] 名称
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    const params = this.get();
    const res = await this.model('equipment/detection').listData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res);
  }
  /**
   * @api {get} /equipment/detection/findone 检测装置模型单条数据
   * @apiDescription 检测装置单条数据
   * @apiName /equipment/detection/findone
   * @apiGroup detection
   * @apiParam {Number} id id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async findoneAction() {
    const params = this.get();
    const res = await this.model('equipment/detection').findOneData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res);
  }
  /**
   * @api {post} /equipment/detection/add 增加检测装置模型
   * @apiDescription 增加检测装置
   * @apiName /equipment/detection/add
   * @apiGroup detection
   * @apiParam {Number} type_id
   * @apiParam {String} name
   * @apiParam {Number} state
   * @apiParam {Number} devicetype
   * @apiParam {String} remark
   * @apiParam {Array} select
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/detection').addData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(0, SUCC.addSucc);
  }
  /**
   * @api {post} /equipment/detection/edit 编辑检测装置模型
   * @apiDescription 编辑检测装置
   * @apiName /equipment/detection/edit
   * @apiGroup detection
   * @apiParam {Number} id
   * @apiParam {Number} type_id
   * @apiParam {String} name
   * @apiParam {Number} devicetype 检测装置类型，1：modbus网关，2：风水检测装置，3：用户传输装置
   * @apiParam {Number} state
   * @apiParam {String} remark
   * @apiParam {Array} select
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/detection').editData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data, SUCC.editSucc);
  }

  /**
   * @api {post} /equipment/detection/addattributes 增加模型属性
   * @apiDescription 增加设备属性
   * @apiName /equipment/detection/addattributes
   * @apiGroup detection
   * @apiParam {Number} id  设备模型id
   * @apiParam {String} name   名称
   * @apiParam {Number} type  属性类型 0：数值型；1：状态型
   * @apiParam {Number} attribute_id 属性id
   * @apiParam {Number} sortnum 排序
   * @apiParam {Number} [floatdata] 小数位
   * @apiParam {String} [unitmultiple] 单位换算
   * @apiParam {Number} [unitid] 单位id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addattributesAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').addAttributes(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/detection/editattributes 编辑模型属性
   * @apiDescription 编辑设备属性
   * @apiName /equipment/detection/editattributes
   * @apiGroup detection
   * @apiParam {Number} id 模型属性id
   * @apiParam {String} [name]   名称
   * @apiParam {Number} [type]  属性类型 0：数值型；1：状态型
   * @apiParam {Number} [attribute_id] 属性id
   * @apiParam {Number} [sortnum] 排序
   * @apiParam {Number} [floatdata] 小数位
   * @apiParam {String} [unitmultiple] 单位换算
   * @apiParam {Number} [unitid] 单位id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editattributesAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').editAttributes(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {get} /equipment/detection/deleteattributes 删除模型属性
   * @apiDescription  删除模型属性
   * @apiName /equipment/detection/deleteattributes
   * @apiGroup detection
   * @apiParam {Number} id 模型属性id
   * @apiSuccess {Object} data  查询的数据结果
   */
  async deleteattributesAction() {
    const params = this.get();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/setmodel').deleteAttributes(params, uid);
    if (res.status === 9000) return this.fail(9000, res.msg);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/detection/editaddalarm 添加和编辑单条报警策略的离线报警
   * @apiDescription 添加和编辑单条报警策略的离线报警
   * @apiGroup detection
   * @apiParam {Number} model_id  设备模型id
   * @apiParam {Number} alarm_level_id 报警等级
   * @apiParam {String} msgset 通知方式 逗号分隔
   * @apiParam {String} alarmcontent 报警内容
   */

  async editaddalarmAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/detection').editAddAlarmData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/detection/addalarm 添加单条报警策略
   * @apiDescription 添加单条报警策略
   * @apiGroup detection
   * @apiParam {Number} model_id  设备模型id
   * @apiParam {Number} type 属性类型，0：数值型；1：状态型
   * @apiParam {Number} typeid 属性值id 大类属性
   * @apiParam {String} msgset 通知方式 逗号分隔
   * @apiParam {String} devmodel_attribute_id 设备属性id
   * @apiParam {Number} alarm_state 是否开启报警，0：否；1：是
   * @apiParam {Number} groupid 组合字段
   * @apiParam {Number} continued_time 持续时间
   * @apiParam {String} alarm_level_id 报警等级id
   * @apiParam {Number} event_record_status 是否记录事件（0：否，1：是）
   * @apiParam {String} symbol_id 符号id
   * @apiParam {String} symbol 符号display的值
   * @apiParam {Number} alarm_value 报警值 状态型的编码
   * @apiParam {String} msgset  通知方式val，逗号分隔
   * @apiParam {Number} isrecall 否重复通知 0：否；1：是
   * @apiParam {Number} retime 重复通知的时间
   * @apiParam {Number} recallmsg 报警恢复时是否通知 0：否；1：是
   * @apiParam {String} alarmcontent 报警内容
   * @apiParamExample {Array} noticeupgrade
   *    [
   *       {
   *            front_level_id: Number 原始等级id,
   *            after_level_id: Number 升级后等级id
   *            time_interval: Number 升级时间间隔
   *            msgset: String '1,2,3'逗号分隔
   *        }
   *     ]
   */

  async addalarmAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/detection').addAlarmData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/detection/editalarm 编辑单条报警策略
   * @apiDescription 编辑单条报警策略
   * @apiGroup detection
   * @apiParam {Number} id 报警策略的id
   * @apiParam {Number} model_id  设备模型id
   * @apiParam {Number} [type] 属性类型，0：数值型；1：状态型
   * @apiParam {Number} [typeid] 属性值id 大类属性
   * @apiParam {String} [msgset] 通知方式 逗号分隔
   * @apiParam {String} [devmodel_attribute_id] 设备属性id
   * @apiParam {Number} [alarm_state] 是否开启报警，0：否；1：是
   * @apiParam {Number} [groupid] 组合字段
   * @apiParam {Number} [continued_time] 持续时间
   * @apiParam {String} [alarm_level_id] 报警等级id
   * @apiParam {Number} [event_record_status] 是否记录事件（0：否，1：是）
   * @apiParam {String} [symbol_id] 符号id
   * @apiParam {String} [symbol] 符号display的值
   * @apiParam {Number} [alarm_value] 报警值 状态型的编码
   * @apiParam {String} [msgset]  通知方式val，逗号分隔
   * @apiParam {Number} [isrecall] 否重复通知 0：否；1：是
   * @apiParam {Number} [retime] 重复通知的时间
   * @apiParam {Number} [recallmsg] 报警恢复时是否通知 0：否；1：是
   * @apiParam {String} [alarmcontent] 报警内容
   * @apiParamExample {Array} [noticeupgrade]
   *        [
   *            {
   *              [id] : Number id
   *              front_level_id: Number 原始等级id,
   *              after_level_id: Number 升级后等级id
   *              time_interval: Number 升级时间间隔
   *              msgset: String '1,2,3'逗号分隔
   *             }
   *        ]
   */

  async editalarmAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/detection').editAlarmData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  /**
   * @api {post} /equipment/detection/deletealarm 删除单条报警策略
   * @apiDescription 删除单条报警策略
   * @apiGroup detection
   * @apiParam {Number} id 报警策略的id
   * @apiParam {Number} model_id  设备模型id
   */

  async deletealarmAction() {
    const params = this.get();
    const res = await this.model('equipment/detection').deleteAlarmData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
};
