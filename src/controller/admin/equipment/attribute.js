const Base = require('../../base');
module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('equipment/attribute'));
  }
  async addAction() {
    try {
      const param = this.getParamOrPost();
      param.aid = think.uuid('v1').replace(/-/g, '');
      const info = await think.model('equipment/attribute').insertBase(param);
      return this.success(info);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
