const Base = require('../../base');
module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('equipment/patrol'));
  }

  /**
   * @api {post} /equipment/patrol/add 巡查模板的添加
   * @apiDescription 获取检测装置数据
   * @apiName  /equipment/patrol/add
   * @apiGroup patrolModel
   * @apiParam {String} patroltype 巡查类型，wordbook取值
   * @apiParam {String} patrol_devname 巡查设备名称
   * @apiParam {String} patrolrule 巡查内容，从wordbook取值，多选，逗号分隔
   * @apiParam {String} patrolrequest 备注说明
   * @apiParam {Number} teamid 巡查团队，项目角色里取值
   * @apiParam {String} cycleid 巡查周期，wordbook里取值
   * @apiParam {Number} state 是否启用，0：否；1：是
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/patrol').insetData(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  async listAction() {
    const params = this.getParamOrPost();
    const res = await this.model('equipment/patrol').listData(params);
    // if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }

  async deleteAction() {
    return this.fail(-1, '不允许删除');
  }

  /**
   * @api {get} /equipment/patrol/select 巡查模板的添加
   * @apiDescription 获取检测装置数据
   * @apiName  /equipment/patrol/select
   * @apiGroup patrolModel
   * @apiParam {String} name  巡查设备名称
   * @apiSuccess {Object} data  查询的数据结果
   */
  async selectAction() {
    const params = this.get();
    const res = await this.model('equipment/patrol').selectData(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
};
