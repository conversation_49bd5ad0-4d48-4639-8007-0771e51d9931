const Base = require('../../../base');
module.exports = class extends Base {
  async __before() {
    this.smodel = think.extModel('equipmanage', 'emini/equip');
    this.extService = think.extService('equipmanage', 'emini/equip');
    this.utils = this.controller('ext/emini/utils');
}
  /**
   * @api {get} /admin/equipment/warehouse/approval/applylist 申领列表
   * @apiDescription 列表数据
   * @apiName /admin/equipment/warehouse/approval/applylist
   * @apiGroup warehouseapproval
   * @apiParam {Number} organize_id 机构id
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {Number} order_id 单号
   * @apiParam {String} person 申请人
   * @apiParam {Number} status 状态 
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit  数量
   * @apiSuccess {Object} data  查询的数据结果
   */
  async applylistAction() {
    const params = this.getParamOrPost();
    const adminInfo = await this.isLogin();
    const res = await think.model('equipment/warehouse/approval').applylist(params,adminInfo);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }

    /**
   * @api {get} /admin/equipment/warehouse/approval/repairlist 维修列表
   * @apiDescription 维修列表
   * @apiName /admin/equipment/warehouse/approval/repairlist 
   * @apiGroup warehouseapproval
   * @apiParam {Number} organize_id 机构id
   * @apiParam {Number} equip_type 装备类型
   * @apiParam {String} equip_name 装备名称
   * @apiParam {String} person 申请人
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit  数量
   * @apiSuccess {Object} data  查询的数据结果
   */
     async repairlistAction() {
      const params = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const res = await think.model('equipment/warehouse/approval').repairlist(params,adminInfo);
      if (res.status === -1) {
        return this.fail(-1, res.msg);
      }
      return this.success(res.data);
    }

    /**
   * @api {get} /admin/equipment/warehouse/approval/scraplist 报废列表
   * @apiDescription 报废列表
   * @apiName /admin/equipment/warehouse/approval/scraplist 
   * @apiGroup warehouseapproval
   * @apiParam {Number} organize_id 机构id
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {Number} equip_type 装备类型
   * @apiParam {number} scrap_reason 报废原因 1 到达年限 2损坏
   * @apiParam {String} person 申请人
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit  数量
   * @apiSuccess {Object} data  查询的数据结果
   */
     async scraplistAction() {
      const params = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      const res = await think.model('equipment/warehouse/approval').scraplist(params,adminInfo);
      if (res.status === -1) {
        return this.fail(-1, res.msg);
      }
      return this.success(res.data);
    }



  /**
   * @api {post} /admin/equipment/warehouse/approval/examinemode 审核单流程审批
   * @apiDescription 审核单流程审批
   * @apiName /admin/equipment/warehouse/approval/examinemode
   * @apiGroup warehouseapproval
   * @apiParam {string} orderid  申请单号
   * @apiParam {number} type  状态(0:修改， 1:通过， 2:驳回上一级, 3:驳回发起人 4:发起人撤销  5:申请人签字确认)
   * @apiParam {String} remarks  审批意见
   * @apiParam {string} img  签字图片base64
   * @apiSuccess {Object} data  新增结果
   */
  async examinemodeAction() {
    const params = this.getParamOrPost();
    const adminInfo = await this.isLogin();
    const userInfo = await think.model('admin').where({id: adminInfo.uid}).find();
    const userid = userInfo.qywx_userid;
    let obj = {
      uid:adminInfo.uid,
      name:adminInfo.nickname,
      userid: userid
    }
    const res = await this.smodel.editExamine(params,obj,this.utils);
    //const res = await think.model('equipment/warehouse/approval').addOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {get} /admin/equipment/warehouse/approval/getDetail 获取详情
   * @apiDescription 获取库单详情
   * @apiName /admin/equipment/warehouse/approval/getDetail
   * @apiGroup warehouseapproval
   * @apiParam {Number} id 列表id
   */
  async getDetailAction() {
    const params = this.getParamOrPost();
    const res = await think.model('equipment/warehouse/approval').getOrder(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/approval/update 修改入库单
   * @apiDescription 修改入库单
   * @apiName /admin/equipment/warehouse/approval/update
   * @apiGroup warehouseapproval
   * @apiParam {Number} id 入库单id
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {string} supplier 供应商
   * @apiParam {String} attachment_groupid 附件groupid
   * @apiParam {String} remarks 备注
   * @apiParam {Array} equipments 装备数组
   * @apiParam {Number} equipments.id 入库单装备id（对应详情）
   * @apiParam {Number} equipments.count 装备数量
   */
  async updateAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/approval').updateOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/approval/in 入库
   * @apiDescription 入库
   * @apiName /admin/equipment/warehouse/approval/in
   * @apiGroup warehouseapproval
   * @apiParam {Number} id 入库单id
   */
  async inAction() {
    const { id } = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/approval').inOrder(id, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/approval/approval 审批
   * @apiDescription 审批
   * @apiName /admin/equipment/warehouse/approval/approval
   * @apiGroup warehouseapproval
   * @apiParam {Number} id 入库单id
   * @apiParam {Number} status 审核状态，1通过 2驳回
   * @apiParam {String} opinion 审核意见，驳回时需要
   */
  async approvalAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/approval').approvalOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/approval/delete 删除入库单
   * @apiDescription 删除入库单
   * @apiName /admin/equipment/warehouse/approval/delete
   * @apiGroup warehouseapproval
   * @apiParam {Number} id 入库单id
   */
  async deleteAction() {
    const { id } = this.post();
    const res = await think.model('equipment/warehouse/approval').deleteOrder(id);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  async claimFile(groupid) {
    if(groupid){
      await think.model('admin/filesave/file').claimFile(groupid);
    }
  }

  /**
   * @api {post} /admin/equipment/warehouse/approval/approvalinfo 入库审批信息查看
   * @apiDescription 入库审批信息查看
   * @apiName /admin/equipment/warehouse/approval/approvalinfo
   * @apiGroup warehouseapproval
   * @apiParam {Number} id 入库单id
   * @apiSuccess {Object} data  查询的数据结果
   */
   async approvalinfoAction() {
    const params = this.getParamOrPost();
    const res = await think.model('equipment/warehouse/approval').approvalinfo(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }

    /**
   * @api {post} /admin/equipment/warehouse/approval/repairadd 维修明细添加
   * @apiDescription 维修明细添加
   * @apiName /admin/equipment/warehouse/approval/repairadd
   * @apiGroup warehouseapproval
   * @apiParam {Number} orderid 维修单号
   * @apiParam {float} allprice 总价
   * @apiParam {string} groupid 附件
   *  @apiParam {array} item [equip_name:装备名称，parts_name:配件名称，count:数量，price:价格]
   * @apiSuccess {Object} data  查询的数据结果
   */
     async repairaddAction() {
      const params = this.getParamOrPost();
      const res = await think.model('equipment/warehouse/approval').repairadd(params);
      if (res.status === -1) {
        return this.fail(-1, res.msg);
      }
      return this.success(res.data);
    }



};
