const Base = require('../../../base');
module.exports = class extends Base {
  /**
   * @api {get} /admin/equipment/warehouse/transfer/list 调拨单列表
   * @apiDescription 调拨单列表
   * @apiName /admin/equipment/warehouse/transfer/list
   * @apiGroup warehouseTransfer
   * @apiParam {Number} organize_id 机构id
   * @apiParam {Number} in_warehouse_id 入库仓库id
   * @apiParam {Number} out_warehouse_id 出库仓库id
   * @apiParam {string} order_type  调拨类型 0 下级申请 1 同级调拨 2 上级指派
   * @apiParam {string} person  申请人
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit  数量
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    const params = this.get();
    const res = await think.model('equipment/warehouse/transfer').listData(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }


  /**
   * @api {post} /admin/equipment/warehouse/transfer/add 调拨单新增
   * @apiDescription 调拨单新增
   * @apiName /admin/equipment/warehouse/transfer/add
   * @apiGroup warehouseTransfer
   * @apiParam {Number} organize_out 调出机构id
   * @apiParam {Number} organize_in 调机构id
   * @apiParam {Number} warehouse_out 调出机构id
   * @apiParam {Number} warehouse_in 调机构id
   * @apiParam {date} begin 调配开始 YYYY-MM-DD
   * @apiParam {date} end  调配结束 YYYY-MM-DD
   * @apiParam {Number} type  1 普通调拨 2 申请调拨
   * @apiParam {string} remakes 备注
   * @apiParam {array} lists 调拨明细列表  [{"equipment_id":装备id,"specification":[]规格,"desc":[]描述,"count":数量}]
   * @apiParam {Number} level 层级关系 0 下级到上级 1 同等级 2 上级到下级
   * @apiSuccess {Object} data  查询的数据结果
   */
   async addAction() {
    const params = this.getParamOrPost();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/transfer').addorder(params,user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }



    /**
   * @api {post} /admin/equipment/warehouse/transfer/mode  调拨单作废或者删除
   * @apiDescription 调拨单修改
   * @apiName /admin/equipment/warehouse/transfer/mode
   * @apiGroup warehouseTransfer
   * @apiParam {Number} id  调拨单id
   * @apiParam {Number} type  1 作废 2 删除
   * @apiSuccess {Object} data  查询的数据结果
   */
     async modeAction() {
      const params = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await think.model('equipment/warehouse/transfer').modeOrder(params,user);
      if (res.status === -1) {
        return this.fail(-1, res.msg);
      }
      return this.success(res.data);
    }


  /**
   * @api {post} /admin/equipment/warehouse/transfer/approval 审批
   * @apiDescription 审批
   * @apiName /admin/equipment/warehouse/transfer/approval
   * @apiGroup warehouseTransfer
   * @apiParam {Number} id 调拨单id
   * @apiParam {Number} status 审核状态，1通过 2驳回 
   * @apiParam {string} opinion 审批意见
   * @apiParam {Array} equipments 装备数组 [{id：装备明细id,count:修改后数量}]
   * @apiSuccess {Object} data  查询的数据结果
   */
   async approvalAction() {
    const params = this.getParamOrPost();
    const user =await this.isLogin();
    const res = await think.model('equipment/warehouse/transfer').approval(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }


  /**
   * @api {post} /admin/equipment/warehouse/transfer/approvalinfo 审批信息查看
   * @apiDescription 审批信息查看
   * @apiName /admin/equipment/warehouse/transfer/approvalinfo
   * @apiGroup warehouseTransfer
   * @apiParam {Number} id 调拨单id
   * @apiSuccess {Object} data  查询的数据结果
   */
   async approvalinfoAction() {
    const params = this.getParamOrPost();
    const res = await think.model('equipment/warehouse/transfer').approvalinfo(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }




};
