const Base = require('../../../base');
module.exports = class extends Base {
  /**
   * @api {get} /admin/equipment/warehouse/outstock/list 出库列表
   * @apiDescription 出库列表接口
   * @apiName /admin/equipment/warehouse/outstock/list
   * @apiGroup warehouseOutstock
   * @apiParam {Number} organize_id 机构id
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {Number} order_number 单号
   * @apiParam {Number} type 出库类型，1装备申领 2调拨出库 3报废出库
   * @apiParam {Number} status 状态，1审核中、2待出库、3部分出库、4已出库，5作废
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit  数量
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    const params = this.get();
    const res = await think.model('equipment/warehouse/outstock').listData(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/outstock/add 新增出库
   * @apiDescription 新增出库
   * @apiName /admin/equipment/warehouse/outstock/add
   * @apiGroup warehouseOutstock
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {Number} receiver 出库对象
   * @apiParam {Number} type 出库类型，1装备申领 2调拨出库 3报废出库
   * @apiParam {Array} equipments 装备数组
   * @apiParam {Number} equipments.equipment_id 装备id
   * @apiParam {Array} equipments.specification 装备规格（['颜色','尺寸']）
   * @apiParam {Array} equipments.desc 装备描述，与规格对应（['黑', 'L ']）
   * @apiParam {Number} equipments.count 出库数量
   * @apiParam {String} remarks 备注
   * @apiParam {Bool} outstock 是否直接出库
   * @apiSuccess {Object} data  新增结果
   */
  async addAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/outstock').addOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {get} /admin/equipment/warehouse/outstock/getDetail 获取出库单详情
   * @apiDescription 获取出库单详情
   * @apiName /admin/equipment/warehouse/outstock/getDetail
   * @apiGroup warehouseOutstock
   * @apiParam {Number} id 出库单id
   */
  async getDetailAction() {
    const { id } = this.get();
    const res = await think.model('equipment/warehouse/outstock').getOrder(id);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/outstock/update 修改出库单
   * @apiDescription 修改出库单
   * @apiName /admin/equipment/warehouse/outstock/update
   * @apiGroup warehouseOutstock
   * @apiParam {Number} id 入库单id
   * @apiParam {Array} equipments 装备数组
   * @apiParam {Number} equipments.id 出库单装备id（对应详情）
   * @apiParam {Number} equipments.count 装备数量
   */
  async updateAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/outstock').updateOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/outstock/out 出库
   * @apiDescription 出库
   * @apiName /admin/equipment/warehouse/outstock/out
   * @apiGroup warehouseOutstock
   * @apiParam {Number} id 出库单id
   * @apiParam {Array} equipments 出库装备数组
   * @apiParam {Number} equipments.id 出库单装备id（对应详情的equipments的id）
   * @apiParam {Number} equipments.count 装备数量
   * @apiParam {Number} equipments.number_id
   */
  async outAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/outstock').outOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/outstock/approval 审批
   * @apiDescription 审批
   * @apiName /admin/equipment/warehouse/outstock/approval
   * @apiGroup warehouseOutstock
   * @apiParam {Number} id 出库单id
   * @apiParam {Number} status 审核状态，1通过 2驳回
   * @apiParam {String} opinion 审核意见，驳回时需要
   */
  async approvalAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/outstock').approvalOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/outstock/delete 删除出库单
   * @apiDescription 删除出库单
   * @apiName /admin/equipment/warehouse/outstock/delete
   * @apiGroup warehouseOutstock
   * @apiParam {Number} id 入库单id
   */
  async deleteAction() {
    const { id } = this.post();
    const res = await think.model('equipment/warehouse/outstock').deleteOrder(id);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }


  /**
   * @api {post} /admin/equipment/warehouse/outstock/approvalinfo 出库审批信息查看
   * @apiDescription 出库审批信息查看
   * @apiName /admin/equipment/warehouse/outstock/approvalinfo
   * @apiGroup warehouseOutstock
   * @apiParam {Number} id 出库单id
   * @apiSuccess {Object} data  查询的数据结果
   */
   async approvalinfoAction() {
    const params = this.getParamOrPost();
    const res = await think.model('equipment/warehouse/outstock').approvalinfo(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }


    /**
    * @api {post}  /admin/equipment/warehouse/outstock/equipstock 具体装备库存查询
    * @apiDescription  具体装备型号库存查询
    * @apiName  /admin/equipment/warehouse/outstock/
    * @apiGroup ext
    * @apiParam {number} warehouse_id  仓库id
    * @apiParam {number} euipment_id  装备id
    * @apiParam {array} specification 装备规格["颜色","尺寸"]
    * @apiParam {array} desc  装备规格一一对应描述["黄色","L"]
    * @apiSuccess {Object} data 查询的数据结果
    */
 
    async equipstockAction() {
      const params = this.getParamOrPost();
      const res = await think.model('equipment/warehouse/outstock').getEquipstock(params);
      if (res.status === -1) return this.fail(-1, res.msg);
      return this.success(res.data);
    }


      /**
   * @api {post} /admin/equipment/warehouse/outstock/numberlist 出库编号查询
   * @apiDescription 出库编号查询
   * @apiName /admin/equipment/warehouse/outstock/numberlist
   * @apiGroup warehouseOutstock
   * @apiParam {string} number_id 装备编号
   * @apiSuccess {Object} data  查询的数据结果
   */
   async numberlistAction() {
    const params = this.getParamOrPost();
    const res = await think.model('equipment/warehouse/outstock').numberlist(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }

      /**
   * @api {get} /admin/equipment/warehouse/outstock/devicelist 设备列表
   * @apiDescription 设备列表获取
   * @apiName /admin/equipment/warehouse/outstock/devicelist
   * @apiGroup warehouseOutstock
   * @apiSuccess {Object} data  查询的数据结果
   */
      async devicelistAction() {
      const params = this.getParamOrPost();
      const res = await think.model('equipment/warehouse/outstock').devicelist(params);
      if (res.status === -1) {
        return this.fail(-1, res.msg);
      }
      return this.success(res.data);
    }



};
