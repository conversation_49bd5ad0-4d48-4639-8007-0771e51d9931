const Base = require('../../../base');
module.exports = class extends Base {
  /**
   * @api {get} /admin/equipment/warehouse/instock/list 入库列表
   * @apiDescription 入库列表接口
   * @apiName /admin/equipment/warehouse/instock/list
   * @apiGroup warehouseInstock
   * @apiParam {Number} organize_id 机构id
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {Number} order_number 单号
   * @apiParam {Number} type 入库类型，1归还入库 2调拨入库 3采购入库
   * @apiParam {Number} status 状态，1审核中、2待入库、3已入库，4已作废（驳回）
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit  数量
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    const params = this.get();
    const res = await think.model('equipment/warehouse/instock').listData(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/instock/add 新增入库
   * @apiDescription 新增入库
   * @apiName /admin/equipment/warehouse/instock/add
   * @apiGroup warehouseInstock
   * @apiParam {Number} outstock_id 关联出库单id，归还入库时必填
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {Number} supplier 供应商
   * @apiParam {Number} type 入库类型，1归还入库 2调拨入库 3采购入库
   * @apiParam {Array} equipments 装备数组
   * @apiParam {Number} equipments.equipment_id 装备id
   * @apiParam {Array} equipments.specification 装备规格（['颜色','尺寸']）
   * @apiParam {Array} equipments.desc 装备描述，与规格对应（['黑', 'L ']）
   * @apiParam {Number} equipments.count 入库数量
   * @apiParam {String} attachment_groupid 附件groupid
   * @apiParam {String} remarks 备注
   * @apiParam {Bool} instock 是否直接入库
   * @apiSuccess {Object} data  新增结果
   */
  async addAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/instock').addOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    if (params.attachment_groupid) {
      await this.claimFile(params.attachment_groupid);
    }
    return this.success(res.data);
  }
  /**
   * @api {get} /admin/equipment/warehouse/instock/getDetail 获取入库单详情
   * @apiDescription 获取入库单详情
   * @apiName /admin/equipment/warehouse/instock/getDetail
   * @apiGroup warehouseInstock
   * @apiParam {Number} id 入库单id
   */
  async getDetailAction() {
    const { id } = this.get();
    const res = await think.model('equipment/warehouse/instock').getOrder(id);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/instock/update 修改入库单
   * @apiDescription 修改入库单
   * @apiName /admin/equipment/warehouse/instock/update
   * @apiGroup warehouseInstock
   * @apiParam {Number} id 入库单id
   * @apiParam {Number} warehouse_id 仓库id
   * @apiParam {string} supplier 供应商
   * @apiParam {String} attachment_groupid 附件groupid
   * @apiParam {String} remarks 备注
   * @apiParam {Array} equipments 装备数组
   * @apiParam {Number} equipments.id 入库单装备id（对应详情）
   * @apiParam {Number} equipments.count 装备数量
   */
  async updateAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/instock').updateOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/instock/in 入库
   * @apiDescription 入库
   * @apiName /admin/equipment/warehouse/instock/in
   * @apiGroup warehouseInstock
   * @apiParam {Number} id 入库单id
   */
  async inAction() {
    const { id } = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/instock').inOrder(id, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/instock/approval 审批
   * @apiDescription 审批
   * @apiName /admin/equipment/warehouse/instock/approval
   * @apiGroup warehouseInstock
   * @apiParam {Number} id 入库单id
   * @apiParam {Number} status 审核状态，1通过 2驳回
   * @apiParam {String} opinion 审核意见，驳回时需要
   */
  async approvalAction() {
    const params = this.post();
    const user = await this.isLogin();
    const res = await think.model('equipment/warehouse/instock').approvalOrder(params, user);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  /**
   * @api {post} /admin/equipment/warehouse/instock/delete 删除入库单
   * @apiDescription 删除入库单
   * @apiName /admin/equipment/warehouse/instock/delete
   * @apiGroup warehouseInstock
   * @apiParam {Number} id 入库单id
   */
  async deleteAction() {
    const { id } = this.post();
    const res = await think.model('equipment/warehouse/instock').deleteOrder(id);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }
  async claimFile(groupid) {
    if(groupid){
      await think.model('admin/filesave/file').claimFile(groupid);
    }
  }

  /**
   * @api {post} /admin/equipment/warehouse/instock/approvalinfo 入库审批信息查看
   * @apiDescription 入库审批信息查看
   * @apiName /admin/equipment/warehouse/instock/approvalinfo
   * @apiGroup warehouseInstock
   * @apiParam {Number} id 入库单id
   * @apiSuccess {Object} data  查询的数据结果
   */
   async approvalinfoAction() {
    const params = this.getParamOrPost();
    const res = await think.model('equipment/warehouse/instock').approvalinfo(params);
    if (res.status === -1) {
      return this.fail(-1, res.msg);
    }
    return this.success(res.data);
  }




};
