/**
 * auther :javen
 */
const Base = require('../../base');
module.exports = class extends Base {
  /**
   * @api {get} /equipment/buildpatrol/list 建筑巡查模板列表
   * @apiDescription 建筑巡查模板列表
   * @apiName /equipment/buildpatrol/list
   * @apiGroup buildpatrol
   * @apiParam {Number} _page 页
   * @apiParam {Number} _limit 多少数据
   * @apiParam {Number} state 是否启用的状态  0,1,2
   * @apiParam {String} [type] 巡查类型名称
   * @apiSuccess {Object} data  查询的数据结果
   */
  async listAction() {
    const params = this.get();
    const res = await this.model('equipment/buildpatrol').getList(params);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} /equipment/buildpatrol/add 建筑巡查模板添加
   * @apiDescription 建筑巡查模板添加
   * @apiName /equipment/buildpatrol/add
   * @apiGroup buildpatrol
   * @apiParam {String} type 类型
   * @apiParam {Number} patrolrule 内容
   * @apiParam {Number} patrolrequest 对象
   * @apiParam {Number} teamid 要求
   * @apiParam {Number} cycleid 周期
   * @apiParam {Number} state 是否启用的状态 0，1
   * @apiSuccess {Object} data  查询的数据结果
   */
  async addAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/buildpatrol').addDate(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
  /**
   * @api {get} /equipment/buildpatrol/edit 建筑巡查模板编辑
   * @apiDescription 建筑巡查模板编辑
   * @apiName /equipment/buildpatrol/edit
   * @apiGroup buildpatrol
   * @apiParam {Number} id
   * @apiParam {String} type 类型
   * @apiParam {Number} patrolrule 内容
   * @apiParam {Number} patrolrequest 对象
   * @apiParam {Number} teamid 要求
   * @apiParam {Number} cycleid 周期
   * @apiParam {Number} state 是否启用的状态 0，1
   * @apiSuccess {Object} data  查询的数据结果
   */
  async editAction() {
    const params = this.post();
    const uid = (await this.isLogin()).uid;
    const res = await this.model('equipment/buildpatrol').editDate(params, uid);
    if (res.status === -1) return this.fail(-1, res.msg);
    return this.success(res.data);
  }
};
