const Base = require('../../base.js');

const smsNoticeLogModel = think.model('admin/smsNoticeLog');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(smsNoticeLogModel);
  }
  /**
   * @api {post} /admin/notice/smslog/again 补发通知
   * @apiGroup noticesms
   * @apiParam {Number} id 短信通知id
   */
  async againAction() {
    try {
      const params = this.getParamOrPost();
      const id = parseInt(params.id) ? parseInt(params.id) : 0;
      const list = await smsNoticeLogModel.where({notice_id: id, status: 1}).select();
      if (list.length > 0) {
        for (const v of list) {

        }
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
