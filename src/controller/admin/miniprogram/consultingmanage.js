const Base = require('../../base.js');
const Err = require('../../../common/errorCode');
const consultingmanageModel = think.model('admin/miniprogram/consultingmanage')

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('admin/miniprogram/consultingmanage'));
  }
  async reportAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await consultingmanageModel.report(param);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
};
