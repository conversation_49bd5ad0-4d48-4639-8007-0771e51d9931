const Base = require('../../base.js');
const MSG = require('../../../common/errorCode');
const userMap = new Map();
const moment = require('moment');
module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('mini/rota'));
  }
  async deleteAction() {
    return this.fail(-1, 'fail');
  }
  /**
   * 小程序
   * 添加报备数据
   */
  async addAction() {
    try {
      const user = await this.isLogin();
      if (think.isEmpty(user)) return this.fail(-1, '用户未登陆授权');
      const p = this.getParamOrPost();
      for (const item in p) {
        if (p[item] === 'null') p[item] = '';
      }
      const res = await think.model('mini/rota').addRotaData(p, user.id);
      return this.success(res);
    } catch (e) {
      think.logger.info(e);
      return this.fail(-1, e.message);
    }
  }
  /**
   * 小程序
   * 单位信息查看
   */
  async companyAction() {
    try {
      const param = this.getParamOrPost();
      const userinfo = await this.isLogin();
      const ret = await think.model('mini/rota').queryCompany(param, userinfo.uid);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 密钥校验
   * @returns {Promise<any>}
   */
  async secretAction() {
    try {
      const param = this.post();
      const secret = param.secret;
      if (!secret) {
        throw new Error('参数错误');
      }
      const info = await think.model('admin').where({username: secret}).find();
      const userInfo = await this.isLogin();
      if (!think.isEmpty(info)) {
        userMap.set(userInfo.id, true);
        return this.success();
      } else {
        throw new Error('密码错误,请联系管理员');
      }
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * 小程序
   * 日期选择
   */
  async timeAction() {
    try {
      const ret = await think.model('mini/rota').queryTime();
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * 小程序
   * 编辑已报备过的数据
   */
  async editAction() {
    try {
      const paramas = this.getParamOrPost();
      for (const item in paramas) {
        if (paramas[item] === 'null') paramas[item] = '';
      }
      delete paramas.rota_date;
      const user = await this.isLogin();
      const isEdit = await this.model('mini_rota').where({id: paramas.id, uid: user.id}).find();
      if (think.isEmpty(isEdit)) return this.fail(-1, '没有找到要修改的数据');
      paramas.cid = isEdit.cid;
      paramas.isdoing = isEdit.isdoing;
      if (isEdit.isdoing === 1) {
        const oDatetime = new Date(isEdit.rota_date).getTime() / 1000;
        if (getnow() > oDatetime) return this.fail(-1, '数据已超过过修改时间');
      }
      const updateObj = {
        updatetime: getnow()
      };
      if (paramas.orther_name) updateObj.other_name = paramas.orther_name;
      await this.model('mini_company').where({id: isEdit.cid}).update(updateObj);
      const ret = await this.modelBase.updateBase(paramas, user);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  /**
   * PC
   * 取消已报备过的数据
   */
  async mydelAction() {
    try {
      const paramas = this.getParamOrPost();
      if (think.isEmpty(paramas.rota_gid)) return this.fail(-1, '时间组rota_gid');
      if (think.isEmpty(paramas.id)) return this.fail(-1, '缺少单位id参数');
      const isEdit = await this.model('mini_rotagroup').where({cid: paramas.id, type: 1, rota_gid: paramas.rota_gid}).find();
      if (think.isEmpty(isEdit)) return this.fail(-1, '该单位没有报备过');
      await this.model('mini_rota').where({cid: isEdit.cid, rota_gid: paramas.rota_gid}).delete();
      await this.model('mini_rotagroup').where({rota_gid: paramas.rota_gid, cid: isEdit.cid}).update({uid: 0, isdoing: 1, type: 0});
      return this.success(0, 'success');
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  /**
   * 小程序
   * 查询当前用户的报备数据
   */
  async mydataAction() {
    try {
      const paramas = this.getParamOrPost();
      if (!paramas.id && think.isEmpty(paramas.cid)) return this.fail(-1, '缺少单位id');
      if (!paramas.id && think.isEmpty(paramas.rota_gid)) return this.fail(-1, '缺少rota_gid');
      if (paramas.cid === 'undefined') {
        return this.fail(-1, '缺少单位id');
      }
      const user = await this.isLogin();
      const whereObj = {};
      if (!(paramas.checked && userMap.get(user.id))) {
        whereObj['a.uid'] = user.id;
      }
      if (paramas.id) {
        whereObj['a.id'] = paramas.id;
      }
      if (paramas.cid && paramas.cid !== 'undefined') {
        whereObj['a.cid'] = paramas.cid;
      }
      if (!think.isEmpty(paramas.rota_gid)) whereObj['a.rota_gid'] = paramas.rota_gid;
      const res = await this.model('mini_rota').alias('a')
        .field('a.*,c.cname,c.address,c.belong_town').join({
          table: 'mini_company',
          join: 'left',
          as: 'c',
          on: ['cid', 'id']
        }).where(whereObj).order('a.rota_date desc').select();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  /**
   * 小程序
   * 查询当前用户的报备的单位
   */
  async mycompanyAction() {
    try {
      const paramas = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await this.model('mini_rotagroup').alias('a')
        .field('a.uid,a.type,a.isdoing,a.rota_gid,a.updatetime,b.cname,b.address,b.belong_town,b.other_name,b.id')
        .join({
          table: 'mini_company',
          join: 'left',
          as: 'b',
          on: ['cid', 'id']
        })
        .where({'a.uid': user.id, 'a.type': 1}).select();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  /**
   * 小程序
   * 取消报备
   */
  async delsignAction() {
    return this.fail(-1, '当前不能取消报备');
    try {
      const paramas = this.getParamOrPost();
      if (think.isEmpty(paramas.cid)) return this.fail(-1, '缺少单位id');
      const user = await this.isLogin();
      await this.model('mini_company').where({id: paramas.cid, uid: user.id}).update({updatetime: getnow(), uid: 0, type: 0, isdoing: 1, other_name: ''});
      await this.model('mini_rota').where({cid: paramas.cid, uid: user.id}).delete();
      return this.success(0, 'success');
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  /**
   * 小程序
   * 是否报备
   */
  async havesignAction() {
    try {
      const paramas = this.getParamOrPost();
      if (think.isEmpty(paramas.cid)) return this.fail(-1, '缺少单位id');
      const rotaDate = await this.model('mini_rotadate').order('id desc').find();
      const user = await this.isLogin();
      const rd = await this.model('mini_rotagroup').where({rota_gid: rotaDate.gid, cid: paramas.cid}).find();
      if (!think.isEmpty(rd.uid) && rd.type === 1) return this.fail(-1, '该单位已报备，如有疑问请联系管理员');
      // await this.model('mini_rota').where({cid: paramas.cid, uid: user.id}).delete();
      return this.success(0, 'success');
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  // 未报备的数据列表
  async unsignAction() {
    const p = this.getParamOrPost();
    try {
      const ret = await think.model('mini/rota').unsign(p);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 未复工列表
  async undolistAction() {
    const p = this.getParamOrPost();
    try {
      const ret = await think.model('mini/rota').undingList(p);
      return this.success(ret);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  // 报备数据
  async listAction() {
    try {
      const p = this.getParamOrPost();
      if (think.isEmpty(p.rota_gid)) throw new Error('缺少参数rota_gid');
      if (p.rota_date) p.rota_date = typeof p.rota_date === 'string' ? JSON.parse(p.rota_date) : p.rota_date;
      if (think.isEmpty(p.rota_date) || !think.isArray(p.rota_date)) throw new Error('缺少参数rota_date');
      const searchObj = {
        // 'a.isdoing': 1,
        'a.type': 1
      };
      const searchArr = p._search ? p._search.split(',') : [];
      searchArr.forEach(item => {
        if (item === 'cname' || item === 'address') {
          searchObj[`c.${item}`] = ['like', `%${p[item]}%`];
        } else if (item === 'belong_town') {
          searchObj[`c.${item}`] = p[item];
        } else if (item === 'rota_gid') {
          searchObj[`b.${item}`] = p[item];
        } else if (item === 'rota_date') {
          searchObj['b.rota_date'] = ['IN', p.rota_date];
        }
      });
      const _page = p._page || 1;
      const _limit = p._limit || 10;
      const sort = p.sort=="ASC"?"ASC":"desc"
      const rd = await this.model('mini_rotagroup').alias('a').field('c.cname,c.address,c.belong_town,b.*')
        .join('pt_mini_rota b on a.rota_gid=b.rota_gid and a.cid=b.cid')
        .join({
          table: 'mini_company',
          join: 'left',
          as: 'c',
          on: ['cid', 'id']
        }).where(searchObj).page(_page, _limit).order(`a.cid,b.rota_date ${sort}`).countSelect();
      for (const val of rd.data) {
        val.rota_date = moment(val.rota_date).format('YYYY/MM/DD');
      }
      return this.success(rd);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
  async streetAction() {
    try {
      const res = await this.model('mini_company').field('id,belong_town').group('belong_town').select();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  async countAction() {
    try {
      const p = this.getParamOrPost();
      const res = await think.model('mini/rota').countData(p);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  async datelistAction() {
    try {
      const res = await think.model('mini/rota').dateList();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  async groupdropAction() {
    try {
      const res = await think.model('mini/rota').groupdropList();
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, MSG.serverError);
    }
  }
  // async addtimeAction() {
  //   try {
  //     const p = this.getParamOrPost();
  //     if (!p.gtitle) throw new Error('缺少gtitle');
  //     if (!p.time || !(p.time instanceof Array)) throw new Error('缺少报备时间');
  //     if (!p.time[0]) throw new Error('缺少开始时间');
  //     if (!p.time[1]) throw new Error('缺少结束时间');
  //     const addObj = {
  //       gtitle: p.gtitle,
  //       s_time: p.time[0],
  //       e_time: p.time[1],
  //       remark: p.remark
  //     };
  //     const s = new Date(p.time[0]).getTime();
  //     const e = new Date(p.time[1]).getTime();
  //     const day = (e - s) / 1000 / 60 / 60 / 24;
  //     if (day < 1) throw new Error('开始和结束时间最少要相差1天');
  //     const model = this.model('mini_rotagroupid');
  //     const check = await model.where(`s_time>='${p.time[0]}' and e_time<='${p.time[1]}'`).select();
  //     if (think.isEmpty(check)) {
  //       const rd = await model.add(addObj);
  //       if (rd > 0) {
  //         const addManyData = [];
  //         for (let i = 0; i < day + 1; i++) {
  //           let oneDayTimeStamp = s;
  //           if (i > 0) {
  //             oneDayTimeStamp = (60 * 60 * 24 * 1000 * i) + s;
  //           }
  //           const oneDay = think.datetime(oneDayTimeStamp, 'YYYY-MM-DD');
  //           const dateArr = (oneDay.split('-')).slice(1);
  //           const dateDesc = dateArr[0] + '月' + dateArr[1] + '日';
  //           const obj = {
  //             rota_date: oneDay,
  //             rota_date_desc: dateDesc,
  //             gid: rd
  //           };
  //           addManyData.push(obj);
  //         }
  //         await this.model('mini_rotadate').addMany(addManyData);
  //         return this.success(rd);
  //       } else {
  //         throw new Error('创建失败');
  //       }
  //     } else {
  //       throw new Error('该时间段已经存在');
  //     }
  //   } catch (err) {
  //     think.logger.info(err);
  //     return this.fail(-1, err.message);
  //   }
  // }

  async addtimeAction() {
    try {
      const p = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await think.model('mini/rota').addtime(p, user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {post} /admin/miniprogram/rota/addReportObj 添加报备对象
   * @apiDescription addReportObj
   * @apiName  /admin/miniprogram/rota/addReportObj
   * @apiGroup miniprogram
   * @apiParam {string} rota_gid task表id
   * @apiParam {string} cid 单位id
   * @apiSuccess {Object} data 查询的数据结果
   */
  async addReportObjAction(){
    try {
      const p = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await think.model('mini/rota').addReportObj(p, user);
      if(res.code === -1 ) return this.fail(-1, res.msg);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

   /**
   * @api {get} /admin/miniprogram/rota/getCompany 获取单位
   * @apiDescription getCompany
   * @apiName  /admin/miniprogram/rota/getCompany
   * @apiGroup miniprogram
   * @apiSuccess {Object} data 查询的数据结果
   */
  async getCompanyAction(){
    try {
      const p = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await think.model('mini/rota').getCompany(p, user);
      return this.success(res);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  /**
   * @api {get} /admin/miniprogram/rota/getReportObj 获取报备对象
   * @apiDescription getReportObj
   * @apiName  /admin/miniprogram/rota/getReportObj
   * @apiGroup miniprogram
   * @apiParam {string} rota_gid task表id
   * @apiParam {string} cid 单位id
   * @apiSuccess {Object} data 查询的数据结果
   */
  async getReportObjAction(){
    try {
      const p = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await think.model('mini/rota').getReportObj(p, user);
      if(res.code === -1 ) return this.fail(-1, res.msg);
      return this.success(res.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

 /**
   * @api {post} /admin/miniprogram/rota/delReportObj 删除报备对象
   * @apiDescription delReportObj
   * @apiName  /admin/miniprogram/rota/delReportObj
   * @apiGroup miniprogram
   * @apiParam {string} rota_gid task表id
   * @apiParam {string} cid 单位id
   * @apiSuccess {Object} data 查询的数据结果
   */
  async delReportObjAction(){
    try {
      const p = this.getParamOrPost();
      const user = await this.isLogin();
      const res = await think.model('mini/rota').delReportObj(p, user);
      return this.success({});
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }

  // async timelistAction() {
  //   const p = this.getParamOrPost();
  //   const _page = p._page ? p._page : 1;
  //   const _limit = p._limit ? p._limit : 10;
  //   try {
  //     const searchObj = {};
  //     if (p._search) {
  //       const searchArr = p._search.split(',');
  //       searchArr.map(item => {
  //         if (p[item] || p[item] === 0) {
  //           if (item === 'gtitle') {
  //             searchObj[item] = ['like', `%${p[item]}%`];
  //           } else {
  //             searchObj[item] = p[item];
  //           }
  //         };
  //       });
  //     }
  //     const res = await this.model('mini_rotagroupid').where(searchObj).page(_page, _limit).countSelect();
  //     return this.success(res);
  //   } catch (e) {
  //     return this.fail(-1, e.message);
  //   }
  // }

  /**
   * @api {post} /admin/miniprogram/rota/summary 报备汇总
   * @apiDescription summary
   * @apiName  /admin/miniprogram/rota/summary
   * @apiGroup miniprogram
   * @apiSuccess {Number} taskCount 任务批次
   * @apiSuccess {Number} reportRecordCount 报备记录
   */
   async summaryAction() {
    try {
      const p = this.getParamOrPost();
      const searchObj = {
        'a.type': 1
      };
      // 报备记录
      // const reportRecordCount = await this.model('mini_rotagroup').alias('a').where(searchObj).count();

      const reportRecordCount = await this.model('mini_rotagroup').alias('a').field('c.cname,c.address,c.belong_town,b.*')
        .join('pt_mini_rota b on a.rota_gid=b.rota_gid and a.cid=b.cid')
        .join({
          table: 'mini_company',
          join: 'left',
          as: 'c',
          on: ['cid', 'id']
        }).where(searchObj).count();
        
      // 任务批次
      const searchObj2 = {task_type : 10};
      const taskCount = await this.model('report_config').where(searchObj2).count();
      const companyCount = await this.model('mini_company').where({isdel:0}).count()
      return this.success({reportRecordCount, taskCount, companyCount});
    } catch (e) {
      think.logger.info(e);
      return this.fail(-1, e.message);
    }
  }
  async timelistAction() {
    const p = this.getParamOrPost();
    const _page = p._page ? p._page : 1;
    const _limit = p._limit ? p._limit : 10;
    try {
      const searchObj = {task_type : 10};
      if(p.gtitle) searchObj.theme = ['like', `%${p.gtitle}%`];
      const res = await this.model('report_config').field('id, theme as gtitle, start_time as s_time, end_time as e_time, description as remark').where(searchObj).page(_page, _limit).countSelect();
      return this.success(res);
    } catch (e) {
      return this.fail(-1, e.message);
    }
  }

  async deltimeAction() {
    const p = this.getParamOrPost();
    try {
      if (!p.id) throw new Error('缺少id');
      const ishave = await this.model('mini_rotagroup').where({rota_gid: p.id}).select();
      if (!think.isEmpty(ishave)) throw new Error('该时段已经有人报备了，不允许删除，请联系管理员');
      const rd1 = await this.model('report_config').where({id: p.id}).delete();
      if (rd1 > 0) {
        await this.model('mini_rotadate').where({gid: p.id}).delete();
      }
      return this.success(rd1);
    } catch (e) {
      return this.fail(-1, e.message);
    }
  }
};
