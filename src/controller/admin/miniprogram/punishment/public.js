
const Base = require('../../../base.js');
const punishmentModel = think.model('admin/miniprogram/punishment/public');
module.exports = class extends Base {
  async __before() {
    await this.setModelBase(think.model('admin/miniprogram/punishment/public'));
  }
  async listAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await punishmentModel.getList(param);
      if (ret.status === -1) return this.fail(-1, ret.msg);
      return this.success(ret.data);
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err.message);
    }
  }
};
