const Base = require('../../base.js');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('mini/company'));
  }
  async deleteAction() {
    try {
      const p = this.getParamOrPost();
      if (think.isEmpty(p.id)) throw new Error('缺少id');
      const rd = await this.model('mini_company').where({id: p.id}).update({isdel: 1});
      return this.success(rd);
    } catch (e) {
      return this.fail(-1, e.message);
    }
  }
};
