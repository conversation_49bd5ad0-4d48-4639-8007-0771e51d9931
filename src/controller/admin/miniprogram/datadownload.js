const Base = require('../../base.js');
const Err = require('../../../common/errorCode');
const downloadModel = think.model('admin/miniprogram/datadownload');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(downloadModel);
  }
  async addAction() {
    try {
      let param = this.getParamOrPost();
      const adminInfo = await this.isLogin();
      if (adminInfo) {
        param.creator = adminInfo.uid;
        param.creator_name = adminInfo.nickname;
      }
      let groupidArr = [];
      if(param.hasOwnProperty('groupid')){
        groupidArr = param.groupid;
        delete param.groupid;
      }
      console.log(`==========param:${JSON.stringify(param)}`);
      const ret = await downloadModel.insertBase(param);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      for(let groupid of groupidArr){
        think.model('admin/filesave/file').claimFile(groupid);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
  /**
   * @api {post,get} /[controllerPath|controllerName]/edit 修改数据
   * @apiDescription 标准修改功能接口，支持基于list接口告知允许修改的字段列表动态构建表单的保存
   * @apiName /base/edit
   * @apiGroup controllerBase
   * @apiParam {String} id 需要修改的ID字段。
   * @apiParam {String} [fieldname] 需要提交的字段名，根据表单需要，有多少字段就提交多少个键值对。
   */
  async editAction() {
    try {
      let paramas = this.getParamOrPost();
      let groupidArr = [];
      if(paramas.hasOwnProperty('groupid')){
        groupidArr = paramas.groupid;
        delete paramas.groupid;
      }
      const res = await this.modelBase.updateBase(paramas, await this.isLogin());
      if (res.status === 0) {
        for(let groupid of groupidArr){
          think.model('admin/filesave/file').claimFile(groupid);
        }
        return this.success(res.data, res.msg);
      } else {
        return this.fail(-1, res.msg);
      }
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, err);
    }
  }
};
