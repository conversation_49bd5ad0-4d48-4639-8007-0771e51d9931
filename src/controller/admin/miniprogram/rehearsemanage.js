const Base = require('../../base.js');
const Err = require('../../../common/errorCode');
const rehearsemanageModel = think.model('admin/miniprogram/rehearsemanage');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(this.model('admin/miniprogram/rehearsemanage'));
  }
  async reportsAction() {
    try {
      const param = this.getParamOrPost();
      const ret = await rehearsemanageModel.reports(param);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
};
