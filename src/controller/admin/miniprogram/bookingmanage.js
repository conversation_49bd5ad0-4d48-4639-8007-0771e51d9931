const Base = require('../../base.js');
const Err = require('../../../common/errorCode');
const bookingModel = think.model('admin/miniprogram/bookingmanage');

module.exports = class extends Base {
  async __before() {
    await this.setModelBase(bookingModel);
  }
  async disposalAction() {
    try {
      const config = think.config('sysconfig');
      const param = this.getParamOrPost();
      const ret = await bookingModel.disposal(param, config);
      if (ret.status !== 0) {
        throw new Error(ret.msg);
      }
      return this.success();
    } catch (err) {
      think.logger.info(err);
      return this.fail(-1, Err.serverError);
    }
  }
};
