const Base = require('../base');
const moment = require('moment');
const validate = require(think.ROOT_PATH + '/src/common/validate');
module.exports = class extends Base {
  // async __before() {
  //   this.utils = this.controller('ext/emini/utils');
  // }
  constructor(tableName, config) {
    // 允许要访问的表名,默认为模块名
    // 可以在实例化之前修改表名
    tableName = 'punish_manage';
    super(tableName, config);
  }

    async getList(params) {
        try {
        
        const {_limit=10, _page=1,name='',status,pid,starttime,endtime} = params;

        let filter = ` 1=1 `;

        if(name){
            filter += ` AND d.o_name LIKE '%${name}%'  `
        }

        if (status) {
            filter += ` AND a.status=${status} `;
        }

        if(pid){
            filter += ` AND a.pid=${pid} `;
        }

        if(starttime&&endtime){
            filter += ` and a.record_time between ${starttime} and ${endtime} `
        }


        const res = await this.alias('a').join([
            'pt_building_group as c on c.id = a.pid',
            'pt_company as d on d.id = a.company_id'
        ]).order('a.record_time DESC').field('a.*,c.name as bname,d.o_name as cname').where(filter).page(_page, _limit).countSelect();

        return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }

    async addData(params, user, utils) {
        try {
        const obj = {
            pid: params.pid,
            company_id: params.company_id,
            record_time: params.record_time,
            status: params.status || 1,
            person: params.person,
            punish_detail: params.punish_detail,
            punish_type: params.punish_type,
            rule: params.rule,
            gropid: params.gropid,
            createtime:moment().unix(),
            updatetime:moment().unix()
        };
        const res = await this.add(obj);
        return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }

    async editData(params) {
        try {
            const obj = {
                pid: params.pid,
                company_id: params.company_id,
                record_time: params.record_time,
                status: params.status || 1,
                person: params.person,
                punish_detail: params.punish_detail,
                punish_type: params.punish_type,
                rule: params.rule,
                gropid: params.gropid,
                updatetime:moment().unix()
            };
            const res = await this.where({id: params.id}).update(obj);
            return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }

    async changestate(params) {
        try {
            let obj={
                status:params.type,
                updatetime:moment().unix()
            }
            const res = await this.where({id: params.id}).update(obj);
            return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }

    async companylist(params) {
        try {
            let filter = ` deleted=0 `
            if(params.name){
                filter += ` and o_name LIKE '%${params.name}%'  `
            }
            const res = await  think.model('company').field(`id,o_name as name`).where(filter).select();
            return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }

    async getbuildbycid(params) {
        try {
            const{cid} = params
            let filter = ` company_id = ${cid} `
            const res1 = await  think.model('map_building_company_rent').where(filter).select(); //入住单位
            let buildid = new Set()
            for (const item of res1) {
                buildid.add(item.building_group_id)
            }

            const res2 = await  think.model('map_manage_building_group').where(filter).select(); //管理单位
            for (const item of res2) {
                buildid.add(item.building_group_id)
            }

            const res3 = await  think.model('map_maintenance_building_group').where(filter).select(); //维保单位
            for (const item of res3) {
                buildid.add(item.building_group_id)
            }

            const res4 = await  think.model('map_service_building_group').where(filter).select();  //服务商
            for (const item of res4) {
                buildid.add(item.building_group_id)
            }

            let bid = buildid.size>0 ?  Array.from(buildid) : []
            let res = []
            if(bid.length>0){
                let filter1 = ` id in (${bid.join(",")}) `
                res = await  think.model('building_group').field(`id,name`).where(filter1).select();
            }
            return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }

    async  statisticsList(params) {
        try {
        
        const {_limit=10, _page=1,starttime,endtime,pid,ordertype,street_code,name,orderfiled} = params;
        let filter = ` 1=1 `;

        if(starttime&&endtime){
            filter += ` and a.record_time between ${starttime} and ${endtime} `
        }

        if(street_code){
            filter += ` AND c.street_code = '${street_code}' `
        }

        if(pid){
            filter += ` AND a.pid=${pid} `;
        }

        if(name){
            filter += ` and (c.name LIKE '%${name}%' or d.o_name  LIKE '%${name}%') `
        }

    

        let order = ` a.pid `

        if(orderfiled==1){
            order = ` counts `
        }

        if(ordertype==1){
            order += ` asc `
        }else if(ordertype==2){
            order += ` desc `
        }
        const res = await this.alias('a').join([
            'pt_building_group as c on c.id = a.pid',
            'pt_company as d on d.id = a.company_id'
        ]).order(order).field('a.pid,c.name as bname,c.street_code,GROUP_CONCAT( DISTINCT d.o_name) as company_list,sum(a.status=1 or a.status=3) as counts').where(filter).group(`a.pid`).page(_page, _limit).countSelect();

        let sql = `SELECT COUNT(*) as num FROM (
            SELECT a.pid FROM pt_punish_manage as a 
            LEFT JOIN pt_building_group as c on c.id = a.pid 
            LEFT JOIN pt_company as d on a.company_id = d.id  where (${filter})
            GROUP BY a.pid
            ) AS tmp`

        const allcount = await this.query(sql)
        res.count = allcount[0].num || 0
      
      
        return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }


};
