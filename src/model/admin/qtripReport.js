const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'qtrip_report';
    super(tableName, config);
    this.listFields = [
      {name: 'id', show: 1, add: 0, edit: 0},
      {name: 'month', show: 1, add: 1, require: 1, search: 1, like: 1},
      { name: 'creator', show: 1, add: 1, edit: 1 },
      { name: 'creator_name', show: 1, add: 1, edit: 1 },
      { name: 'deleted', show: 1, add: 1, edit: 1, search: 1 },
      { name: 'created_at', show: 1, add: 1, edit: 0 },
      { name: 'updated_at', show: 1, add: 1, edit: 1 },
      { name: 'report_number', show: 1, search: 1 },
      { name: 'status', add: 1, edit: 0, show: 1, search: 1 }
    ];
  }
};
