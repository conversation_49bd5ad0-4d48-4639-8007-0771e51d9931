const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'outfire_plan';
    super(tableName, config);
    this.setRelation(false);
    this.listFields = [
      {name: 'id', show: 1, add: 0, edit: 0},
      {name: 'name', show: 1, add: 1, edit: 1, search: 1, like: 1},
      {name: 'building_group_id', show: 1, add: 1, edit: 1},
      {name: 'key_part_id', show: 1, add: 1, edit: 1},
      {name: 'company_id', show: 1, add: 1, edit: 1},
      {name: 'company_type', show: 1, add: 1, edit: 1},
      {name: 'status', show: 1, add: 1, edit: 1},
      {name: 'department', show: 1, add: 1, edit: 1},
      {name: 'leader', show: 1, add: 1, edit: 1},
      {name: 'person', show: 1, add: 1, edit: 1},
      {name: 'description', show: 1, add: 1, edit: 1},
      {name: 'result', show: 1, add: 1, edit: 1},
      {name: 'plan_images', show: 1, add: 1, edit: 1},
      {name: 'record_id', show: 1, add: 1, edit: 1},
      {name: 'record_type', show: 1, add: 1, edit: 1},
      {name: 'record_name', show: 1, add: 1, edit: 1},
      {name: 'creator', show: 1, add: 1, edit: 1},
      {name: 'creator_name', show: 1, add: 1, edit: 1},
      {name: 'created_at', show: 1, add: 1, edit: 0},
      {name: 'updated_at', show: 1, add: 1, edit: 1}
    ];
  }
  get relation() {
    return {
      building_key_part: {
        type: think.Model.BELONG_TO,
        model: 'building_key_part',
        key: 'key_part_id',
        fKey: 'id',
        field: 'id,name'
      },
      company: {
        type: think.Model.BELONG_TO,
        model: 'company',
        key: 'company_id',
        fKey: 'id',
        field: 'id,o_name'
      }
    };
  }
  async getList (params) {
    try {
      this.setRelation(true);
      const {_page, _limit, name, bgid} = params;
      let filter;
      filter = `a.building_group_id = ${parseInt(bgid)}`;
      if (name) {
        filter = `a.name like '%${name}%'`;
      }
      const res = await this.alias('a').where(filter).page(_page, _limit).order('created_at DESC').countSelect();
      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }
  async getManage(params) {
    try {
      // const db = this.model('company');
      // const whereObj = {building_group_id: params.bgid};
      // db.alias('a');
      // db.join({
      //   table: 'map_manage_building_group',
      //   join: 'inner', // join 方式，有 left, right, inner 3 种方式
      //   as: 'b', // 表别名
      //   on: ['a.id', 'b.company_id'] // ON 条件
      // });
      // db.field('a.id as id, a.o_name as name');
      // // const res = await db.where(whereObj).order('a.createtime DESC').page(params._page, params._limit).countSelect();
      // const res = await db.where(whereObj).select();
      // return {status: 0, data: res};

      const buildingGroupId = parseInt(params.bgid) ? parseInt(params.bgid) : 0;

      const filter = `bc.building_group_id=${buildingGroupId} and u.company_type&2`;
      const list = await think.model('admin/mapManageScope').alias('bc').field('u.*').join({
        table: 'company', join: 'left', as: 'u', on: ['bc.company_id', 'u.id']
      }).where(filter).group('u.id').order('u.id DESC').select();
      return {status: 0, data: list};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  async getKeypart(params) {
    try {
      const db = this.model('building_key_part');
      const whereObj = {building_group_id: params.bgid};
      db.field('id, name');
      const res = await db.where(whereObj).select();
      return { status: 0, data: res };
    } catch (err) {
      think.logger.info(err);
      return {status: -1, msg: err.message};
    }
  }
};
