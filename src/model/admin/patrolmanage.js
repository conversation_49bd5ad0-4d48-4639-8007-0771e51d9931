const Base = require('../base');
const moment = require('moment');
const util = require('../../common/util');
module.exports = class extends Base {
  // async __before() {
  //   this.utils = this.controller('ext/emini/utils');
  // }
  constructor(tableName, config) {
    // 允许要访问的表名,默认为模块名
    // 可以在实例化之前修改表名
    tableName = 'patrol_point';
    super(tableName, config);
  }

  async getpointList(params) {
    try {

      const { name,code,_page=1,_limit=10} = params;
      let filter = {deleted:0}
      if (name) {
        filter['name'] = ['like',`%${name}%`];
      }
      if (code) {
        filter['code'] = ['like',`%${code}%`];
      }
      const res = await think.model('patrol_point').order(`code asc`).where(filter).page(_page, _limit).countSelect();
      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }


  async getpointcode(params) {
    try {
      let res = await  util.getcodeNumber('patrol','code')
      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }




  async addpatrolconfig(params) {
    try {
      const obj = {
        name: params.name,
        type: params.type,
        person: params.person,
        creattime: moment().unix()
      };
      const res = await think.model('patrol_config').add(obj);
      if(params.point.length>0){
        let addobj = []
          for (const i of params.point) {
            addobj.push({config_id:res,point_code:i})
          }
          await think.model('patrol_config_point').addMany(addobj)
      }
      return {status: 0, data: res};
    } catch (err) {
      return { status: -1, msg: err };
    }
  }

  async editpatrolconfig(params) {
    try {
      const obj = {
        id:params.id,
        name: params.name,
        type: params.type,
        person: params.person,
        updatetime:moment().unix()
      };
      const res = await think.model('patrol_config').update(obj);
      await think.model('patrol_config_point').where({config_id:params.id}).delete()
      if(params.point.length>0){
        let addobj = []
          for (const i of params.point) {
            addobj.push({config_id:params.id,point_code:i})
          }
          await think.model('patrol_config_point').addMany(addobj)
      }
      return {status: 0, data: res};

    } catch (err) {
      return { status: -1, msg: err };
    }
  }



  async patrolconfigList(params) {
    try {
  
        const { name,_page=1,_limit=10} = params;
        let filter =  `a.deleted=0 `
        if (name) {
          filter+=  `  and a.name LIKE '%${name}%' `  
        }
      
        const res  = await think.model('patrol_config').alias('a')
        .join([
            `pt_patrol_config_point as c on a.id = c.config_id`,
            `pt_patrol_point as d on d.code = c.point_code`,
        ]).field(`a.*,GROUP_CONCAT(d.name) as pointlist,GROUP_CONCAT(d.code) as pointcodelist`).
        order(`a.creattime desc`).where(filter).group(`a.id`).page(_page, _limit).countSelect();


        let sql = `SELECT COUNT(*) as num FROM (
            SELECT a.*,GROUP_CONCAT(d.name) as pointlist FROM pt_patrol_config as a 
            LEFT JOIN pt_patrol_config_point as c on a.id = c.config_id
            LEFT JOIN pt_patrol_point as d on d.code = c.point_code  where (${filter})
            GROUP BY a.id
            ) AS tmp`

        const allcount = await this.query(sql)
        res.count = allcount[0].num || 0

      return {status: 0, data: res};

    } catch (err) {
      return { status: -1, msg: err };
    }
  }


  async patrolrecord(params) {
    try {

      const { starttime,endtime,person,configid,type,_page=1,_limit=10} = params;
      let filter = `1=1`
      if (starttime&&endtime) {
        filter += `  and  create_time between ${starttime} and  ${endtime} `
        //filter['create_time'] = ['between', `${starttime},${endtime}`];
      }
      if (person) {
          filter+=  ` and FIND_IN_SET('${person}',person) `
      }
      if(configid){
          filter+= `  and config_id= ${configid}`
      }
      if(type){
          filter+= ` and type=${type} `
      }

      const res = await think.model('patrol_data').order(`create_time desc`).where(filter).page(_page, _limit).countSelect();
      let info ={
      }
      info['allcount'] = res.count
      let pset = new Set()
      let names = await think.model('patrol_data').field(`person`).group('person').where(` ${filter} and person <> ""  `).select()
      for (const i of names) {
          for (const j of i.person.split(',')) {
              pset.add(j)
          }
      }  
      info['pcount'] = pset.size
      let ss = await think.model('patrol_data').field(`count(distinct config_id) as taskcount,count(distinct point_code) as pointcount`).where(filter).select()
      info['taskcount'] =  ss[0].taskcount || 0
      info['pointcount'] =  ss[0].pointcount || 0
      res['total'] = info

      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }


  async patrolperson(params) {
    try {
      let res = await think.model('patrol_data').field(`person`).group('person').where('person <> "" ').select()
      let data = new Set()
      for (const i of res) {
         let name  =  i.person.split(',')
        for (const j of name) {
            data.add(j)
        }
      }
      let resdata = [...data]
      let ps = []
      for (const i of resdata) {
          ps.push({person:i})
      }
      return {status: 0, data: ps};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }



};
