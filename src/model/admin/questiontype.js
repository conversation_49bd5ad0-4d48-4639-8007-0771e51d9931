const Base = require('../base');
const moment = require('moment');
module.exports = class extends Base {
  // async __before() {
  //   this.utils = this.controller('ext/emini/utils');
  // }
  constructor(tableName, config) {
    // 允许要访问的表名,默认为模块名
    // 可以在实例化之前修改表名
    tableName = 'question_type';
    super(tableName, config);
  }

    async getList(params) {
        try {
        
            let res = await think.model('question_type').alias('a').join(
                `pt_question_bank b ON FIND_IN_SET(a.code,b.type_code) and b.status!=2`
            ).field(`a.*,count(b.id) as count`).group(`a.id`).order('a.code asc').select();

        return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }



    async addData(params,user) {
        try {
        
        const {code,name} = params;
        let  find  =  await this.where({code:code}).find()
        if(!think.isEmpty(find)){
            return {status: -1, msg: '分类ID不能重复！'};
        }
        let  obj  ={
            code:code,
            name:name,
            creator_id:user.uid,
            create_time:moment().unix()
        }
        let  res  =  await this.add(obj)
        return {status: 0, data: res};
        } catch (err) {
            const result = think.isError(err) ? err : new Error(err);
            return {status: -1, msg: result.message};
        }
    }


    async editData(params) {
        try {
        
        const {id,name} = params;
        let  res  =  await this.where({id:id}).update({name:name})
        return {status: 0, data: res};
        } catch (err) {
            const result = think.isError(err) ? err : new Error(err);
            return {status: -1, msg: result.message};
        }
    }


    async changestate(params) {
        try {
        
        const {id,type} = params;
        if(type==0 || type==1 ){
            await this.where({id:id}).update({status:type})
        }else  if(type==2){
           let ss=  await this.where({id:id}).find()
           if(!think.isEmpty(ss)){
               let res1= await think.model('question_bank').where(` FIND_IN_SET('${ss.code}',type_code) `).select()
               if(res1.length){
                return {status: -1, msg: '题目数量不为0'};
               }
               await this.where({id:id}).delete()
           }
        }
        
        return {status: 0, data: ''};
        } catch (err) {
            const result = think.isError(err) ? err : new Error(err);
            return {status: -1, msg: result.message};
        }
    }




};
