const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'qtrip_report_total';
    super(tableName, config);
    this.listFields = [
      {name: 'id', show: 1, add: 0, edit: 0},
      { name: 'creator', show: 1, add: 1, edit: 1 },
      { name: 'creator_name', show: 1, add: 1, edit: 1 },
      { name: 'deleted', show: 1, add: 1, edit: 1, search: 1 },
      { name: 'created_at', show: 1, add: 1, edit: 0 },
      { name: 'updated_at', show: 1, add: 1, edit: 1 },
      { name: 'qtrip_report_id', show: 1, add: 1, edit: 1 },
      { name: 'sum_gas_car_quantity', show: 1, add: 1, edit: 1 },
      { name: 'sum_derv_car_quantity', show: 1, add: 1, edit: 1 },
      { name: 'sum_fadian', show: 1, add: 1, edit: 1 },
      { name: 'sum_fire_alarm_quantity', show: 1, add: 1, edit: 1 },
      { name: 'sum_training_quantity', show: 1, add: 1, edit: 1 },
      { name: 'sum_other_quantity', show: 1, add: 1, edit: 1 },
      { name: 'sum_month_qtrip', show: 1, add: 1, edit: 1 },
      { name: 'sum_last_month_gas', show: 1, add: 1, edit: 1 },
      { name: 'sum_last_month_derv', show: 1, add: 1, edit: 1 },
      { name: 'sum_cur_get_gas', show: 1, add: 1, edit: 1 },
      { name: 'sum_cur_get_derv', show: 1, add: 1, edit: 1 },
      { name: 'sum_cur_qtrip_gas', show: 1, add: 1, edit: 1 },
      { name: 'sum_cur_qtrip_derv', show: 1, add: 1, edit: 1 },
      { name: 'sum_balance_gas', show: 1, add: 1, edit: 1 },
      { name: 'sum_balance_derv', show: 1, add: 1, edit: 1 },
      { name: 'leader', show: 1, add: 1, edit: 1 },
      { name: 'preparer', show: 1, add: 1, edit: 1 },
      { name: 'filling_date', show: 1, add: 1, edit: 1 }
    ];
  }

  get relation() {
    return {
      items: {
        type: think.Model.HAS_MANY,
        model: 'qtrip_report_total_item',
        key: 'id',
        fKey: 'report_total_id',
        field: '*'
      }
    };
  }
};
