const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    // 允许要访问的表名,默认为模块名
    // 可以在实例化之前修改表名
    tableName = 'project_group';
    super(tableName, config);
    this.listFields = [
      {name: 'id', show: 1, add: 0, edit: 0},
      {name: 'name', show: 1, add: 1, edit: 1, search: 1},
      {name: 'pid', show: 1, add: 1, edit: 1},
      {name: 'status', show: 1, add: 1, edit: 1},
      {name: 'description', show: 1, add: 1, edit: 1},
      {name: 'creator', show: 1, add: 1, edit: 1},
      {name: 'created_at', show: 1, add: 1, edit: 0},
      {name: 'updated_at', show: 1, add: 1, edit: 1}
    ];
  }

  // 添加管辖范围
  async addProjectGroup(params) {
    // 1.添加管辖范围组
    const obj = {
      pid: params.pid,
      status: params.status,
      name: params.name,
      description: params.desc,
      created_at: think.datetime(new Date()),
      creator: params.creator
    };
    const res = await this.add(obj);
    console.log(res);
    // 2.设置添加完组以后 设置组合项目的关系
    for (const i of params.project_id) {
      const obj2 = {
        pid: i,
        group_id: res,
        created_at: think.datetime(new Date())
      };
      await think.model('map_project_group').add(obj2);
    }
    return { status: 0, data: res };
  }

  //  修改管辖范围
  async updateProjectGroup(params) {
    try {
      // 1. 修改project_group 的修改时间 和 desc 字段 status字段
      const obj = {
        id: params.id,
        pid: params.pid,
        status: params.status,
        name: params.name,
        description: params.desc,
        updated_at: think.datetime(new Date()),
        creator: params.creator
      };
      const res = await this.where({id: params.id}).update(obj);
      // 2. 修改 map_project_group 表中的pid字段 （全部删除然后重新创建）
      // 根据 group_id 删除所有的 然后重新添加
      // await think.model('map_project_group').where(` group_id = ${params.id} `).delete();
      const projectIdArr = params.project_id;
      for (const i of projectIdArr) {
        const countInfo = await think.model('map_project_group').where(` group_id = ${params.id} and pid = ${i}`).count();
        if (countInfo === 0) {
          const obj2 = {
            pid: i,
            group_id: params.id,
            created_at: think.datetime(new Date()),
            updated_at: think.datetime(new Date())
          };
          await think.model('map_project_group').add(obj2);
        }
      }
      if (Array.isArray(projectIdArr)) {
        if (projectIdArr.length) {
          await think.model('map_project_group').where({group_id: params.id, pid: ['NOTIN', projectIdArr]}).delete();
        } else {
          await think.model('map_project_group').where({group_id: params.id}).delete();
        }
      }
      return { status: 0, data: res };
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  // 查询列表
  async list(param) {
    let filter = ` 1 = 1 `;
    const page = parseInt(param._page) ? parseInt(param._page) : 1;
    let pageSize = parseInt(param._limit) ? parseInt(param._limit) : 10;
    if (pageSize > 1000) {
      pageSize = 1000;
    }
    const offset = (page - 1) * pageSize;

    const name = param.name;
    const status = param.status;

    if (!think.isEmpty(name)) {
      filter = filter + ` and name like '%${name}%' `;
    }
    if (!think.isEmpty(status) && status >= 0) {
      filter = filter + ` and status = ${status} `;
    }
    const data = await this.where(filter).limit(offset, pageSize).select();
    const count = await this.where(filter).limit(offset, pageSize).count();
    return { data, count };
  }
};
