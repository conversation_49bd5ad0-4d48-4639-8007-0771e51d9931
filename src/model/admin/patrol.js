const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'patrol';
    super(tableName, config);
  }
  async getList (params) {
    try {
      const {_page, _limit, name, bgid} = params;
      let filter;
      filter = `a.pid = ${parseInt(bgid)}`;
      if (name) {
        filter = filter + ` and a.handler like '%${name}%'`;
      }
      const res = await this.alias('a').field('a.*, b.b_name').join({
        table: 'building',
        join: 'left',
        as: 'b',
        on: ['a.bid', 'b.id']
      }).where(filter).page(_page, _limit).order('created_at DESC').countSelect();
      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }
};
