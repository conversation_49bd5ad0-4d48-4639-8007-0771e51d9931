const Base = require('../base');
const moment = require('moment');
module.exports = class extends Base {
  // async __before() {
  //   this.utils = this.controller('ext/emini/utils');
  // }
  constructor(tableName, config) {
    // 允许要访问的表名,默认为模块名
    // 可以在实例化之前修改表名
    tableName = 'question_bank';
    super(tableName, config);
  }

  async getList(params) {
    try {
      const {type_code, system_id, scene, event_id, status, name, _page = 1, _limit = 10, key = ''} = params;
      let filter = ` status!=2 `;
      if (type_code) {
        filter += ` and  FIND_IN_SET('${type_code}',type_code) `;
      }
      if (system_id) {
        filter += ` and system_id = ${system_id} `;
      }
      if (scene) {
        filter += ` and scene = '${scene}' `;
      }

      if (event_id) {
        filter += ` and  FIND_IN_SET('${event_id}',event_id) `;
      }
      if (status) {
        filter += ` and status = ${status} `;
      }

      if (key) {
        filter += ` and project = '${key}' `;
      }

      if (name) {
        const a = '`desc`';
        filter += ` and (title  like '%${name}%'  or ${a}  like '%${name}%' ) `;
      }

      const res = await think.model('question_bank').where(filter).order('create_time desc').page(_page, _limit).countSelect();
      for (const item of res.data) {
        const filter = `bank_id = ${item.id} and status!=2`;
        const im = await think.model('question_bank_item').where(filter).select();
        item['item'] = im;
      }
      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  async addData(params, user) {
    try {
      const obj = {
        type_code: params.type_code,
        system_id: params.system_id,
        scene: params.scene,
        event_id: params.event_id,
        title: params.title,
        desc: params.desc,
        law_id: params.law_id,
        project: params.project,
        creator_id: user.uid,
        create_time: moment().unix(),
        risk_tip: params.risk_tip,
        help: params.help,
        is_blank: params.is_blank,
        blank_type: params.blank_type,
        type: params.type,
        question_values: params.question_values,
        question_default_value: params.question_default_value,
        except_value: params.except_value,
        bid: think.uuid('v1').replace(/-/g, ''),
        score:params.score
      };
      const bankid = await this.add(obj);

      if (params.item && params.item.length) {
        const banklist = params.item.map(i => {
          return {
            name: i.name,
            bank_id: bankid,
            result_type: i.result_type,
            comparator: i.comparator,
            compare_value: i.compare_value,
            itemid: think.uuid('v1').replace(/-/g, '')
          };
        });
        await this.model('question_bank_item').addMany(banklist);
      }

      return {status: 0, data: bankid};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  async editData(params) {
    try {
      const obj = {
        type_code: params.type_code,
        system_id: params.system_id,
        scene: params.scene,
        event_id: params.event_id,
        title: params.title,
        desc: params.desc,
        law_id: params.law_id,
        project: params.project,
        risk_tip: params.risk_tip,
        help: params.help,
        is_blank: params.is_blank,
        blank_type: params.blank_type,
        type: params.type,
        question_values: params.question_values,
        question_default_value: params.question_default_value,
        except_value: params.except_value,
        score:params.score
      };
      const bankid = await this.where({id: params.id}).update(obj);
      const filter = `bank_id =  ${params.id} and status!=2 `;
      const lists = await this.model('question_bank_item').where(filter).select();
      for (const im of params.item) {
        if (!im.id) {
          im['itemid'] = think.uuid('v1').replace(/-/g, '');
          im['bank_id'] = params.id;
          await this.model('question_bank_item').add(im);
          continue;
        }
        await this.model('question_bank_item').where({id: im.id}).update(im);
      }

      for (const im of lists) {
        const num = params.item.length>0 ? params.item.findIndex(i => i.id == im.id) : -1;
        if (num <= -1) {
          await this.model('question_bank_item').where({id: im.id}).update({status: 2});
          await this.model('inspec_template_item').where({bank_id: im.id, level: 3}).delete();
        }
      }

      return {status: 0, data: bankid};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  async scene(params) {
    try {
      const res = await this.field(`scene`).select();
      let arr = [];
      for (const item of res) {
        if (item.scene) {
          const s1 = item.scene.split(',') || [];
          arr = arr.concat(s1);
        }
      }
      const ss = arr.length>0 ? Array.from(new Set(arr)) : [];
      return {status: 0, data: ss};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  async changestate(params) {
    try {
      const {id, type, status} = params;
      let res = '';
      if (type == 1) {
        res = await this.where({id: id}).update({status: status});
        if (status == 2) {
          const ins = await this.model('inspec_template_item').where({bank_id: id, level: 2}).select();
          await this.model('inspec_template_item').where({bank_id: id, level: 2}).delete();
          const ids = ins.map(m => m.id).join(',');
          if (ids) {
            const filter = `  level =3 and parent_id IN (${ids})`;
            await this.model('inspec_template_item').where(filter).delete();
          }
        }
      } else if (type == 2) {
        res = await this.model('question_bank_item').where({id: id}).update({status: status});
        if (status == 2) {
          await this.model('inspec_template_item').where({bank_id: id, level: 3}).delete();
        }
      }

      return {status: 0, data: ''};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  async info(params) {
    try {
      const res = {
        question_type: [],
        question_bank: []
      };
      res.question_type = await think.model('question_type').field(`code,name`).select();

      const ss = await think.model('question_bank').alias('a').join([
        `pt_question_bank_item as b on a.id = b.bank_id`,
        `pt_devmodel_attribute as c on b.devmodel_attribute_id = c.id`
      ]).field(`a.*,b.*,c.attribute_id`).where(`a.status = 0 and b.status=0 `).select();

      const syslist = await think.model('system_type').field(`id,sid`).select();
      const sysobj = {};
      for (const a of syslist) {
        if (!sysobj[`${a.id}`]) {
          sysobj[`${a.id}`] = a.sid;
        }
      }

      const attrMap = {};
      const attrList = await think.model(`attribute`).alias('a').field('a.id,a.aid,a.name as aname,av.avid,av.code,av.name as vname').join({
        table: 'attribute_value', join: 'left', as: 'av', on: ['a.id', 'av.typeid']
      }).select();
      for (const a of attrList) {
        if (!attrMap[`${a.id}`]) {
          attrMap[`${a.id}`] = {id: a.aid, valueList: []};
        }
      }
      for (const a of attrList) {
        if (a.avid) {
          const valObj = {id: a.avid, name: a.vname};
          attrMap[`${a.id}`].valueList.push(valObj);
        } else {
          const valObj = {id: a.aid, name: a.aname};
          attrMap[`${a.id}`].valueList.push(valObj);
        }
      }

      const tree = [];
      const smap = {};
      for (const a of ss) {
        if (!smap[a.bid]) {
          const sysid = sysobj[`${a.system_id}`] || '';
          smap[a.bid] = {id: a.bid, name: a.title, type_code: a.type_code, system_id: sysid, scene: a.scene, event_id: a.event_id, desc: a.desc, itemlist: []};
        }
      }

      for (const a of ss) {
        if (a.itemid) {
          const value = a.attribute_id ? (attrMap[`${a.attribute_id}`].valueList || []) : [];
          const valObj = {id: a.itemid, name: a.name, type: a.result_type, valuelist: value};
          smap[a.bid].itemlist.push(valObj);
        }
      }

      for (const k in smap) {
        tree.push(smap[k]);
      }

      res.question_bank = tree;

      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }

  async getproject(params) {
    try {
      const data = [{'key': 'public', 'value': '公共题库'}];
      return {status: 0, data: data};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }
};
