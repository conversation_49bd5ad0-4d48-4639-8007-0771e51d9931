const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'qtrip_report_vehicle';
    super(tableName, config);
    this.listFields = [
      {name: 'id', show: 1, add: 0, edit: 0},
      { name: 'creator', show: 1, add: 1, edit: 1 },
      { name: 'creator_name', show: 1, add: 1, edit: 1 },
      { name: 'deleted', show: 1, add: 1, edit: 1, search: 1 },
      { name: 'created_at', show: 1, add: 1, edit: 0 },
      { name: 'updated_at', show: 1, add: 1, edit: 1 },
      { name: 'qtrip_report_id', show: 1, add: 1, edit: 1 },
      { name: 'sum_total_mileage', show: 1, edit: 1 },
      { name: 'sum_month_mileage', add: 1, edit: 0, show: 1 },
      { name: 'sum_fadian', add: 1, edit: 0, show: 1 },
      { name: 'sum_fire_alarm_quantity', add: 1, edit: 0, show: 1 },
      { name: 'sum_training_quantity', add: 1, edit: 0, show: 1 },
      { name: 'sum_other_quantity', add: 1, edit: 0, show: 1 },
      { name: 'sum_cur_qtrip_gas', add: 1, edit: 0, show: 1 },
      { name: 'sum_cur_qtrip_derv', add: 1, edit: 0, show: 1 },
      { name: 'sum_balance', add: 1, edit: 0, show: 1 },
      { name: 'sum_fuel_consumption', add: 1, edit: 0, show: 1 },
      { name: 'sum_last_month_gas', add: 1, edit: 0, show: 1 },
      { name: 'sum_last_month_derv', add: 1, edit: 0, show: 1 },
      { name: 'sum_cur_get_gas', add: 1, edit: 0, show: 1 },
      { name: 'sum_cur_get_derv', add: 1, edit: 0, show: 1 },
      { name: 'sum_cur_gas', add: 1, edit: 0, show: 1 },
      { name: 'sum_cur_derv', add: 1, edit: 0, show: 1 },
      { name: 'comment', add: 1, edit: 0, show: 1 },
      { name: 'leader', add: 1, edit: 0, show: 1 },
      { name: 'preparer', add: 1, edit: 0, show: 1 },
      { name: 'filling_date', add: 1, edit: 0, show: 1 }
    ];
  }

  get relation() {
    return {
      items: {
        type: think.Model.HAS_MANY,
        model: 'qtrip_report_vehicle_item',
        key: 'id',
        fKey: 'report_vehicle_id',
        field: '*'
      },
      payments: {
        type: think.Model.HAS_MANY,
        model: 'month_balance_payment',
        key: 'id',
        fKey: 'report_vehicle_id',
        field: '*'
      }
    };
  }
};
