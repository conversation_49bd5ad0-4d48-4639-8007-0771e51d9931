const Base = require('../base');
const moment = require('moment');
const _ = require('lodash');
const redis = require(think.ROOT_PATH + '/src/common/ioredis');
module.exports = class extends Base {

  async list() {
    try {
      let res = await this.model('wordbook').field('remark').where({ keyname: 'qualityscore', val: 1 }).find();
      res = res.remark;
      res = res.length && typeof res == 'string' ? JSON.parse(res) : {};
      return { status: 0, data: res };
    } catch (err) {
      console.log(err);
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async edit(params) {
    try {
      await this.model('wordbook').where({ keyname: 'qualityscore', val: 1 }).update({ remark: JSON.stringify(params) });
      return { status: 0, data: {} };
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  //仪表盘
  async getDashboard() {
    try {
      //物联网服务商数量  本月服务商数量
      const companyNum = await this.model('company').where({ company_type: ['EXP', '&1'], deleted: 0 }).count();
      const companyNum_cache = await redis.get(`qualityscore_sevicecompany`);

      //综合评分报告数量 存档状态报告数量
      const reportNum = await this.model('servie_company_qualityscore').where({ status: 2 }).count();

      //本月平均值 服务商分值/服务商数量
      let monthsTotalScore = await this.model('servie_company_qualityscore').field('sum(total_score) as total_score,count(id) as total_num').where(` score_time >= ${moment().startOf('months').unix()} and score_time <= ${moment().unix()} and status = 1 `).select();
      monthsTotalScore = monthsTotalScore.length ? monthsTotalScore[0] : {};
      const monAvg = parseInt(monthsTotalScore.total_score / monthsTotalScore.total_num) || 0;

      //本年平均值 总分 / 总报告数量
      let totalScore = await this.model('servie_company_qualityscore').field('sum(total_score) as total_score,count(id) as total_num').where(` score_time >= ${moment().startOf('years').unix()} and status = 2 `).select();
      totalScore = totalScore.length ? totalScore[0] : {};
      const yearsAvg = parseInt((totalScore.total_score + monthsTotalScore.total_score) / (totalScore.total_num + monthsTotalScore.total_num)) || 0;

      return {
        status: 0, data: {
          companyNum: companyNum,
          reportNum: reportNum,
          monAvg: monAvg,
          yearsAvg: yearsAvg,
          diff: companyNum_cache ? companyNum - companyNum_cache : 0
        }
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async qualityRank(params) {
    /** 2021 12 29 查询功能修改
     *  查询周期 半年往前推6个月 全年往前推12个月    
     *  统计方式 周期内服务商报告总分 / 周期内应有报告数量  例如 半年，则为6个月内报告总分 / 6
     *  */
    try {
      const { type } = params;
      let start, end, reportNum = 1;
      end = moment().unix();
      switch (type) {
        case '1': //本月
          start = moment().startOf('months').unix();
          break;
        case '2': //上月
          start = moment().subtract(1, 'months').startOf('months').unix();
          end = moment().subtract(1, 'months').endOf('months').unix();
          break;
        case '3': //半年
          // const currntMonth = new Date().getMonth();
          // if (currntMonth + 1 <= 6) {
          //   start = moment(moment().format('YYYY-01-01 00:00:00')).unix();
          //   end = moment(moment().format('YYYY-06-30 23:59:59')).unix();
          // } else {
          //   start = moment(moment().format('YYYY-07-01 00:00:00')).unix();
          //   end = moment(moment().format('YYYY-12-31 23:59:59')).unix();
          // }
          start = moment().subtract(5, 'months').startOf('months').unix();
          reportNum = 6;
          break;
        case '4': //全年
          // start = moment().startOf('years').unix();
          start = moment().subtract(11, 'months').startOf('months').unix();
          reportNum = 12;
          break;
      }

      let res = await this.model('servie_company_qualityscore').field('sum(total_score) as total_score, company_id, company_name, score_time ')
        .where(` score_time >= ${start} and score_time <= ${end} `).group('company_id').select();

      res.map(val => {
        val.total_score = Math.round(val.total_score / reportNum);
      })

      res = res.sort((a, b) => {
        return b.total_score - a.total_score;
      });

      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err);
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async dataComparison() {
    try {
      const start = moment().subtract(12, 'months').startOf('months').unix();
      const end = moment().unix();
      let res = await this.model('servie_company_qualityscore').where(` score_time >= ${start} and score_time <= ${end}`).order('score_time asc').select();
      let obj = {};
      let arr = [];
      // 拼接数组
      for (const val of res) {
        if (!obj[val.company_id]) obj[val.company_id] = { company_id: val.company_id, company_name: val.company_name, totalArr: {} };
        const key = moment(val.score_time * 1000).format(`YYYY-MM`);
        obj[val.company_id].totalArr[key] = val.total_score;
      }
      for (const item in obj) {
        arr.push(obj[item]);
      }
      return {
        status: 0, data: arr
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async rateArchives(params) {
    try {
      const { status, year, month, name, order, _page, _limit } = params;
      let where = ` status in (1,2) `;
      const time = `${year}-${month}-01 00:00:00`;
      let sort = 'id desc';
      if (status) where = ` status = ${status} `;
      if (name) where += ` and company_name like '%${name}%'  `;
      if (year && month) where += ` and score_time >= ${moment(moment().format(time)).unix()} and score_time <= ${moment(moment().format(time)).endOf('month').unix()}  `;
      if (order) sort = order;
      const res = await this.model('servie_company_qualityscore').where(where).order(sort).page(_page, _limit).countSelect();
      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async rateArchivesGov(params) {
    try {
      const { status, year, month, name, order, _page, _limit,company_id} = params;
      let where = ` status=2 `;
      let sort = 'score_time ASC';
      if(company_id&&year){
        let start = moment(`${year}`).startOf(`years`).unix();
        let end = moment(`${year}`).endOf(`years`).unix();
        where += ` and company_id = ${company_id}  and score_time >= ${start} and score_time <= ${end} `;
      }else{
        return {
          status: 0, data: []
        };
      }
      const res = await this.model('servie_company_qualityscore').field(`id,score_time,status,total_score as score`).where(where).order(sort).page(_page, _limit).countSelect();
      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async rateArchivesDetail(params) {
    try {
      let { id, time, status } = params;
      let res = await this.model('servie_company_qualityscore').field('total_score,detail,company_id').where({ id: id }).find();
      const company_id = res.company_id;
      const total_score = res.total_score;
      let sqlfilter = ` a.company_id = ${company_id} and b.deleted = 0 and b.accessstatus = 1 and c.deleted = 0 and c.b_name !='周边环境' `;
      const companyInfo = await this.model('company').field('o_name,legal_name,o_linkphone,o_license,reg_address,o_address,o_linkman').where({ id: company_id }).find();
      res = res && res.detail ? JSON.parse(res.detail) : {};
      res.companyData = Object.assign({}, companyInfo);
      res.companyData.total_score = total_score;

      // 已接入楼宇
      const buildingNums = await think.model('map_service_building_group').alias('a').join([{
        table: 'building_group',
        join: 'left',
        as: 'b',
        on: ['a.building_group_id', 'b.id']
      }, {
        table: 'building',
        join: 'left',
        as: 'c',
        on: ['b.id', 'c.building_group_id']
      }]).where(sqlfilter).select();
      res.companyData.buildnums = buildingNums.length;

      //管理建筑群
      // let stopTotal = 0;
      // const stopData = await think.model('admin/building_group').alias('pt_building_group').field('b.building_group_id, count(1) as count').join({
      //   table: 'map_service_building_group',
      //   as: 'b',
      //   join: 'left',
      //   on: ['pt_building_group.id', 'b.building_group_id']
      // }).where({ stop_status: 1, deleted: 0, accessstatus: 1, company_id: company_id }).group('building_group_id').select();
      // for (const i of stopData) {
      //   const data = await think.model('map_service_building_group').field('count(1) as count').where(` building_group_id = ${i.building_group_id} and company_id = ${company_id} `).select();
      //   for (const j of data) {
      //     if (i.count === j.count) {
      //       stopTotal += 1;
      //     }
      //   }
      // };
      // res.companyData.buildGroupnums = (buildingNums.length - stopTotal);
      const buildGroupnums = await this.model('map_service_building_group').alias('a').join({
        table: 'building_group',
        as: 'b',
        join: 'left',
        on: ['a.building_group_id', 'b.id']
      }).where(` a.company_id = ${company_id} and a.stop_status = 0 and b.accessstatus = 1 and b.deleted = 0 `).select();
      res.companyData.buildGroupnums = buildGroupnums.length;

      //受评月份
      let wdConfig = await this.model('wordbook').field('remark').where({ keyname: 'qualityscore', val: 1 }).find();
      wdConfig = JSON.parse(wdConfig.remark);
      const config_time = parseInt(wdConfig.d) * 86400 + parseInt(wdConfig.h) * 3600 + parseInt(wdConfig.m) * 60;
      res.companyData.score_time = status == 1 ? parseInt(time) + config_time : moment(parseInt(time) * 1000).subtract(1, 'month').startOf('month').unix() + config_time;
      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }


  async rateArchivesDetailGov(params) {
    try {
      
      let { id, time, status } = params;
      if(status!=2){
        return {
          status: -1, data: "只能查看已存档数据!"
        };
      }
      let res = await this.model('servie_company_qualityscore').field('total_score,detail,company_id').where({ id: id }).find();
      const company_id = res.company_id;
      const total_score = res.total_score;
      let sqlfilter = ` a.company_id = ${company_id} and b.deleted = 0 and b.accessstatus = 1 and c.deleted = 0 and c.b_name !='周边环境' `;
      const companyInfo = await this.model('company').field('o_name,legal_name,o_linkphone,o_license,reg_address,o_address,o_linkman').where({ id: company_id }).find();
      res = res && res.detail ? JSON.parse(res.detail) : {};
      res.companyData = Object.assign({}, companyInfo);
      res.companyData.total_score = total_score;

      // 已接入楼宇
      const buildingNums = await think.model('map_service_building_group').alias('a').join([{
        table: 'building_group',
        join: 'left',
        as: 'b',
        on: ['a.building_group_id', 'b.id']
      }, {
        table: 'building',
        join: 'left',
        as: 'c',
        on: ['b.id', 'c.building_group_id']
      }]).where(sqlfilter).select();
      res.companyData.buildnums = buildingNums.length;

      const buildGroupnums = await this.model('map_service_building_group').alias('a').join({
        table: 'building_group',
        as: 'b',
        join: 'left',
        on: ['a.building_group_id', 'b.id']
      }).where(` a.company_id = ${company_id} and a.stop_status = 0 and b.accessstatus = 1 and b.deleted = 0 `).select();
      res.companyData.buildGroupnums = buildGroupnums.length;

      //受评月份
      let wdConfig = await this.model('wordbook').field('remark').where({ keyname: 'qualityscore', val: 1 }).find();
      wdConfig = JSON.parse(wdConfig.remark);
      const config_time = parseInt(wdConfig.d) * 86400 + parseInt(wdConfig.h) * 3600 + parseInt(wdConfig.m) * 60;
      res.companyData.score_time = status == 1 ? parseInt(time) + config_time : moment(parseInt(time) * 1000).subtract(1, 'month').startOf('month').unix() + config_time;
      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async getBuildDashboard(params) {
    try {
      const res = await this.model('servie_building_qualityscore').field('total_score,building_group_id,building_group_name,status,score_time,security_code,uuid').order('total_score desc').where(`cycle=1`).select();

      let qrcode = []
      let uuid = undefined
      let configs = await think.model('grading_config').field('uuid').where({dashboardcheck:1}).find();
      if(!think.isEmpty(configs)){
        uuid = configs.uuid
        let unit = await think.model('grading_unit').where({ uuid: uuid }).select();
        let item = await think.model('grading_item').where({ disable: 0, deleted: 0, uuid: uuid }).select();
        //安全码配置
        const safeCode = unit.filter(val => {
          return val.name === '同步生成建筑(群)安全码';
        })
        const safeCodeItem = item.filter(val => { // 评分项
          return val.grading_unit_id === safeCode[0].id;
        })
        qrcode = safeCodeItem;
      }


      //实时评分排名
      const instantRes = res.filter(val => {
        if(uuid){
          return val.status == 1 && val.uuid==uuid;
        }
        return val.status == 1;
      })

      const green = instantRes.filter(val => {
        return val.security_code == 1;
      });
      const yellow = instantRes.filter(val => {
        return val.security_code == 2;
      });
      const red = instantRes.filter(val => {
        return val.security_code == 3;
      });

      //上月评分报告数
      const lastStart = moment().subtract(1, 'month').startOf('month').unix();
      const lastEnd = moment().subtract(1, 'month').endOf('month').unix();
      const lastMonth = res.filter(val => {
        return val.score_time >= lastStart && val.score_time <= lastEnd;
      })

      //今年评分报告数
      const start = moment().startOf('year').unix();
      const end = moment().endOf('year').unix();
      const year = res.filter(val => {
        return val.score_time >= start && val.score_time <= end;
      })

      //建筑群总数
      const total = await this.model('building_group').where({ deleted: 0 }).count();
      let joinTotal = await this.model('building_group').alias('a').join({
        table: 'map_service_building_group',
        left: 'join',
        as: 'b',
        on: ['a.id', 'b.building_group_id']
      }).field(`a.id`).where(` a.deleted=0 and a.accessstatus=1  and b.join_status = 1 and b.status = 2 and b.stop_status = 0 `).group('a.id').select();
      joinTotal = joinTotal.length;
      const notJoinTotal = await this.model('building_group').where({ accessstatus: 0, deleted: 0 }).count();
      // green = green.sort((a, b) => {
      //   return b.total_score - a.total_score;
      // })
      return {
        status: 0, data: {
          green: green, //绿码排行
          yellow: yellow, //黄码排行
          red: red, //红码排行
          lastMonth: lastMonth.length, //上月总数
          year: year.length, //今年总数
          month: instantRes.length, //本月总数
          total: res.length, //所有报告数
          buildTotal: total, //建筑群总数
          joinTotal: joinTotal, //已接入建筑群数
          notJoinTotal: notJoinTotal,//未接入建筑群数
          qrcode:qrcode
        }
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async getBuildDashboardold(params) {
    try {
      const res = await this.model('servie_building_qualityscore').field('total_score,building_group_id,building_group_name,status,score_time,security_code').order('total_score desc').where(`cycle=1`).select();

      //实时评分排名
      const instantRes = res.filter(val => {
        return val.status == 1;
      })

      const green = instantRes.filter(val => {
        return val.security_code == 1;
      });
      const yellow = instantRes.filter(val => {
        return val.security_code == 2;
      });
      const red = instantRes.filter(val => {
        return val.security_code == 3;
      });

      //上月评分报告数
      const lastStart = moment().subtract(1, 'month').startOf('month').unix();
      const lastEnd = moment().subtract(1, 'month').endOf('month').unix();
      const lastMonth = res.filter(val => {
        return val.score_time >= lastStart && val.score_time <= lastEnd;
      })

      //今年评分报告数
      const start = moment().startOf('year').unix();
      const end = moment().endOf('year').unix();
      const year = res.filter(val => {
        return val.score_time >= start && val.score_time <= end;
      })

      //建筑群总数
      const total = await this.model('building_group').where({ deleted: 0 }).count();
      // let joinTotal = await this.model('building_group').alias('a').join({
      //   table: 'map_service_building_group',
      //   left: 'join',
      //   as: 'b',
      //   on: ['a.id', 'b.building_group_id']
      // }).field(`a.id`).where(` a.deleted=0 and a.accessstatus=1  and b.join_status = 1 and b.status = 2 and b.stop_status = 0 `).group('a.id').select();
      // joinTotal = joinTotal.length;
     
     
      let pids = [9999999]
      const stopData = await think.model('admin/building_group').alias('pt_building_group').field('b.building_group_id, count(1) as count').join({
        table: 'map_service_building_group',
        as: 'b',
        join: 'left',
        on: ['pt_building_group.id', 'b.building_group_id']
      }).where(`stop_status = 1  and pt_building_group.deleted = 0`).group('building_group_id').select();
      for (const i of stopData) {
        const data = await think.model('map_service_building_group').field('count(1) as count, building_group_id').where(` building_group_id = ${i.building_group_id} `).select();
        for (const j of data) {
          if (i.count === j.count) {
            pids.push(j.building_group_id);
          }
        }
      }
   
      let filters = `accessstatus = 1 and id not in(${pids}) `
      let joinTotal =   await this.model('building_group').where(filters).count()

      const notJoinTotal = await this.model('building_group').where({ accessstatus: 0, deleted: 0 }).count();
      // green = green.sort((a, b) => {
      //   return b.total_score - a.total_score;
      // })
      return {
        status: 0, data: {
          green: green, //绿码排行
          yellow: yellow, //黄码排行
          red: red, //红码排行
          lastMonth: lastMonth.length, //上月总数
          year: year.length, //今年总数
          month: instantRes.length, //本月总数
          total: res.length, //所有报告数
          buildTotal: total, //建筑群总数
          joinTotal: joinTotal, //已接入建筑群数
          notJoinTotal: notJoinTotal//未接入建筑群数
        }
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async getBuildDetail(params) {
    try {
      const { id } = params;
      const info = await this.model('servie_building_qualityscore').where({ encryptid: params.id }).find();
      if (think.isEmpty(info)) return { status: -1, msg: "id错误" };
      const buildInfo = await this.model('building_group').where({ id: info.building_group_id }).find();
      if (think.isEmpty(buildInfo)) return { status: -1, msg: "建筑群错误" };

      const res1 = await think.model('map_maintenance_building_group').alias('a').join({
        table: 'company',
        join: 'left',
        as: 'b',
        on: ['a.company_id', 'b.id']
      }).field(`b.o_name`).where(` building_group_id = ${info.building_group_id} and a.status = 2 and b.company_type&4 `).select();
      const res2 = await think.model('map_service_building_group').alias('a').join({
        table: 'company',
        join: 'left',
        as: 'b',
        on: ['a.company_id', 'b.id']
      }).field(`b.o_name`).where(` building_group_id = ${info.building_group_id} and a.status = 2 and b.company_type&1 `).select();
      const res3 = await think.model('wordbook').where({ keyname: "systemname" }).select();
      const res4 = await think.model('wordbook').where({ keyname: "Main_management_Unit" }).select();

      const res5 = await think.model('map_manage_building_group').alias('a').join({
        table: 'company',
        join: 'left',
        as: 'b',
        on: ['a.company_id', 'b.id']
      }).field(`b.o_name`).where(` building_group_id = ${info.building_group_id} and a.status = 2 and b.company_type&2 `).select();

      const employeeData = await think.model('employee').field(`name,mobile`).where({ pid: info.building_group_id,position_type:1}).find();


      let tag = []
      if(buildInfo.building_tag){
        let  where = ` code in (${buildInfo.building_tag}) `
        tag = await think.model(`company_tag`).field(`name`).where(where).select();
      }
    

      return {
        status: 0, data: {
          building_group_name: info.building_group_name,  //建筑群名称
          score: info.total_score, //总分
          reportDate: info.score_time,// moment(info.score_time * 1000).format("YYYY-MM"), //受评时间
          finishtime: buildInfo.finishtime, //竣工时间
          coveredarea: buildInfo.coveredarea, //建筑面积
          zdarea: buildInfo.zdarea, //占地面积
          building_num: buildInfo.building_num, //建筑栋数
          address: buildInfo.address, //建筑地址
          detail: info.detail,  //评分数据详情
          maintenance: res1,
          service:res2,
          cycle:info.cycle,
          systemname:res3.length>0 ? res3[0].display : "",
          management:res4.length>0 ? res4[0].display : "",
          manage:res5,
          employee: think.isEmpty(employeeData) ? {name:null,mobile:null} :employeeData,
          tag:tag,
          security_code:info.security_code
        }
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  async generateReport(params) {
    try {
      const { uuid } = params;
      if (!uuid) return { status: -1, msg: '配置为空' };

      //配置
      const unit = await think.model('grading_unit').where({ uuid: uuid }).select();
      const item = await think.model('grading_item').where({ disable: 0, deleted: 0, uuid: uuid }).select();

      //安全码配置
      const safeCode = unit.filter(val => {
        return val.name === '同步生成建筑(群)安全码';
      })
      const safeCodeItem = item.filter(val => { // 评分项
        return val.grading_unit_id === safeCode[0].id;
      })


      //消防档案  
      let fireArchivesWeight = unit.filter(val => { //获取权重
        return val.name === '消防档案';
      })
      fireArchivesWeight = fireArchivesWeight.length ? fireArchivesWeight[0] : {};

      const itemsIndex1Obj = {};
      const weigthIndex1 = unit.filter((i) => {
        return i.name === '建筑(群)基本信息';
      });
      // 获取具体每项
      const itemsIndex1 = weigthIndex1.length > 0 ? item.filter((i) => { return i.grading_unit_id === weigthIndex1[0].id }) : [];
      for (const val of itemsIndex1) {
        itemsIndex1Obj[val.id] = val.weight;
      }

      const itemsIndex2Obj = {};
      const weigthIndex2 = unit.filter((i) => {
        return i.name === '重点消防部位备案';
      });
      const itemsIndex2 = weigthIndex2.length > 0 ? item.filter((i) => { return i.grading_unit_id === weigthIndex2[0].id }) : [];
      for (const val of itemsIndex2) {
        itemsIndex2Obj[val.id] = val.weight;
      }

      // // 获取具体每项
      const itemsIndex3Obj = {};
      const weigthIndex3 = unit.filter((i) => {
        return i.name === '消防设施设备备案';
      });
      const itemsIndex3 = weigthIndex3.length > 0 ? item.filter((i) => { return i.grading_unit_id === weigthIndex3[0].id }) : [];
      for (const val of itemsIndex3) {
        itemsIndex3Obj[val.id] = val.weight;
      }


      //消防物联网系统接入
      let accessRateWeight = unit.filter(val => { //获取权重
        return val.name === '消防物联网系统接入';
      })
      const accessRateItemObj = {};
      //消防物联网系统接入
      if (!think.isEmpty(accessRateWeight)) {
        accessRateWeight = accessRateWeight[0];
        const accessRateItem = item.filter(val => { // 评分项
          return val.grading_unit_id === accessRateWeight.id;
        })
        for (const val of accessRateItem) {
          accessRateItemObj[val.id] = val.weight;
        }
      }

      const eManagementItemObj = {};
      //设施设备管理
      let eManagementWeight = unit.filter(val => { //获取权重
        return val.name === '设施设备管理';
      })
      //设施设备管理
      if (!think.isEmpty(eManagementWeight)) {
        eManagementWeight = eManagementWeight[0];
        const eManagementItem = item.filter(val => { // 评分项
          return val.grading_unit_id === eManagementWeight.id;
        })
        for (const val of eManagementItem) {
          eManagementItemObj[val.id] = val.weight;
        }
      }

      //当前实时方案
      const infos = await this.model('servie_building_qualityscore').field('id,detail').where({ status: 1, uuid: uuid }).select();
      for (const val of infos) {
        let total_score = 0; //重新计算总分
        let accessRate_totalscore = 0;
        let fireScore = 0;
        let equipmentManagement_totalscore = 0;
        let detail = JSON.parse(val.detail);
        //消防物联网系统接入
        if (detail.accessRate) {

          detail.oldaccessRate_score = detail.accessRate_score;
          detail.accessRate_score = accessRateWeight.weight;
          for (let conf of detail.accessRate) {
            if (accessRateItemObj[conf.id]) {
              //记录上一次子权重
              conf.oldweight = conf.weight;
              //替换新权重
              conf.weight = accessRateItemObj[conf.id];
              //计算完子权重的分数
              conf.childWeightScore = await this.getFloat((conf.score * conf.weight), 2);
              //计算模块权重
              conf.weightScore = await this.getFloat((detail.accessRate_score * (conf.childWeightScore / 100)), 2);
              accessRate_totalscore += conf.weightScore;
              total_score += conf.weightScore;
            }
          }
        }

        //设施设备管理
        if (detail.equipmentManagement) {
          detail.oldequipmentManagement_score = detail.equipmentManagement_score;
          detail.equipmentManagement_score = eManagementWeight.weight;
          for (let conf of detail.equipmentManagement) {
            if (eManagementItemObj[conf.id]) {
              //记录上一次子权重
              conf.oldweight = conf.weight;
              //替换新权重
              conf.weight = eManagementItemObj[conf.id];
              //计算完子权重的分数
              conf.childWeightScore = await this.getFloat((conf.score * conf.weight), 2);
              //计算模块权重
              conf.weightScore = await this.getFloat((detail.equipmentManagement_score * (conf.childWeightScore / 100)), 2);
              equipmentManagement_totalscore += conf.weightScore;
              total_score += conf.weightScore;
            }
          }
        }

        //消防档案
        if (detail.fireArchives.data) {
          detail.oldfireArchives_score = detail.fireArchives_score;
          detail.fireArchives_score = fireArchivesWeight.weight;
          let total_pro = 0;
          for (let conf of detail.fireArchives.data) {
            conf.oldweight = conf.weight;
            //该模块获取的百分比
            conf.score = 0;
            let eachItem;
            switch (conf.name) {
              case "建筑(群)基本信息":
                conf.weight = weigthIndex1[0].weight;
                eachItem = itemsIndex1Obj;
                break;
              case "重点消防部位备案":
                conf.weight = weigthIndex2[0].weight;
                eachItem = itemsIndex2Obj;
                break;
              case "消防设施设备备案":
                conf.weight = weigthIndex3[0].weight;
                eachItem = itemsIndex3Obj;
                break;
            }

            //每一层的子模块
            for (let child_conf of conf.data) {
              if (eachItem[child_conf.id]) {
                //记录上一次子权重
                child_conf.oldweight = child_conf.weight;
                //替换新权重
                child_conf.weight = eachItem[child_conf.id];
                //计算完子权重的分数
                child_conf.childWeightScore = await this.getFloat((child_conf.score * child_conf.weight), 2);
                //计算模块权重
                child_conf.weightScore = await this.getFloat((conf.weight * (child_conf.childWeightScore / 100)), 2);
                conf.score += child_conf.weightScore;
              }
            }
            //该模块占比
            total_pro += conf.score;
          }
          //计算权重之后的实际分数
          fireScore = detail.fireArchives_score * total_pro;
          total_score += fireScore;
        }

        //更新
        const updateObj = {total_score: total_score, detail: JSON.stringify(detail)};
        if(!think.isEmpty(fireArchivesWeight)) updateObj.fireArchives = fireScore;
        if(!think.isEmpty(accessRateWeight)) updateObj.accessRate = accessRate_totalscore;
        if(!think.isEmpty(eManagementWeight)) updateObj.equipmentManagement = equipmentManagement_totalscore;

        //安全码
        for (const safecode of safeCodeItem) {
          safecode.code = parseInt(safecode.code);
          safecode.qualified_operator_value = parseInt(safecode.qualified_operator_value);
          if (safecode.code == 33 && (await this.compare(safecode.qualified_value, updateObj.total_score, safecode.qualified_operator_value))) {
            updateObj.security_code = 1; //绿
          } else if (safecode.code == 35 && (await this.compare(safecode.qualified_value, updateObj.total_score, safecode.qualified_operator_value))) {
            updateObj.security_code = 3; //红
          } else if (safecode.code == 34 && (await this.compare(safecode.qualified_value, updateObj.total_score, safecode.qualified_operator_value))
            && (await this.compare(safecode.unqualified_value, updateObj.total_score, parseInt(safecode.unqualified_operator_value)))) {
              updateObj.security_code = 2; //黄
          }
        }

        await this.model('servie_building_qualityscore').where({ id: val.id }).update(updateObj);
      }

      return {
        status: 0, data: {}
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }

  //保留小数 四舍五入
  async getFloat(num, n) {
    n = n ? parseInt(n) : 0;
    if (n <= 0) {
      return Math.round(num);
    }
    num = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); //四舍五入
    num = Number(num).toFixed(n); //补足位数
    return parseFloat(num);
  };

   // 运算
   async compare(symbolId, value, threshold) {
    switch (symbolId.toString()) {
      case '1':
        return value > threshold;
      case '2':
        return value < threshold;
      case '3':
        return value === threshold;
      case '4':
        return value >= threshold;
      case '5':
        return value <= threshold;
      case '6':
        return value !== threshold;
      default:
        return false;
    }
  }

  async getBuildreportList(params) {
    try {
      const { beforetime, endtime, region, street, state, code, _page = 1, _limit = 10, ordertype = 0, type = 2, buildname,configname,uuid,pid} = params;
      let filter = ` 1=1 `
      if (beforetime && endtime) {
        filter += ` and  a.score_time between ${beforetime} and ${endtime}  `
      }
      if (region) {
        filter += ` and a.region ='${region}' `
      }
      if (street) {
        filter += ` and a.street ='${street}' `
      }
      if (state) {
        filter += ` and a.status =${state} `
      }
      if (code) {
        filter += ` and a.security_code =${code} `
      }
      if (buildname) {
        filter += ` and a.building_group_name like '%${buildname}%' `
      }
      if(configname){
        filter += ` and b.name like '%${configname}%' `
      }
      if(uuid){
        filter += ` and a.uuid = '${uuid}' `
      }
      if(pid){
        filter += ` and a.building_group_id = ${pid} `
      }

      let order = `a.score_time`
      if (ordertype == 1) {
        order = `a.total_score`
      } else if (ordertype == 2) {
        order = `a.fireArchives`
      } else if (ordertype == 3) {
        order = `a.accessRate`
      } else if (ordertype == 4) {
        order = `a.equipmentManagement`
      }
      if (type == 1) {
        order += ` ASC `
      } else if (type == 2) {
        order += ` DESC `
      }

      order += `,a.building_group_id ASC`

      const res = await this.model('servie_building_qualityscore').alias('a').join(
        `pt_grading_config b on b.uuid=a.uuid`
      ).field(`a.id,a.building_group_id,a.building_group_name,a.region,a.street,a.total_score,a.security_code,a.fireArchives,a.accessRate,a.equipmentManagement,a.status,a.score_time,a.create_time,a.encryptid,a.rewardsPenalties,a.safeManage`).where(filter).order(order).page(_page, _limit).countSelect();
      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }


  async getBuildstatisticsList(params) {
    try {
      const { beforetime, endtime, region, street, state, code, _page = 1, _limit = 10, ordertype = 0, type , buildname,configname,uuid} = params;
      let filter = ` 1=1 `
      if (beforetime && endtime) {
        filter += ` and  (a.score_time between ${beforetime} and ${endtime}) `
      }
      if (region) {
        filter += ` and a.region ='${region}' `
      }
      if (street) {
        filter += ` and a.street ='${street}' `
      }
      if (state) {
        filter += ` and a.status =${state} `
      }
      // if (code) {
      //   filter += ` and a.security_code =${code} `
      // }
      if (buildname) {
        filter += ` and a.building_group_name like '%${buildname}%' `
      }
      // if(configname){
      //   filter += ` and b.name like '%${configname}%' `
      // }
      if(uuid){
        filter += ` and a.uuid = '${uuid}' `
      }

      let order = ` a.building_group_id asc`
      if (ordertype == 1) {
        order = `record_count`
      } else if (ordertype == 2) {
        order = `record_score`
      }else if (ordertype == 4) {
        order = `now_score`
      }
      if (type == 1) {
        order += ` ASC `
      } else if (type == 2) {
        order += ` DESC `
      }
      
    let  res =  {
    }
    let start = moment().startOf('years').unix()
    let end = moment().endOf('years').unix()
    let filter1 = ` a.uuid='${uuid}' and  a.score_time between ${start} and ${end} and cycle=1 `
    const taskNumPend = await this.model('servie_building_qualityscore').alias('a').field(`a.building_group_id,(sum(if(a.status!=1,a.total_score,0))/sum(a.status!=1)) as score`)
    .where(filter1).group(`a.building_group_id`).select();

    let taskNumPendobj={}
    taskNumPend.forEach(element => {
      taskNumPendobj[`${element.building_group_id}`] = element.score  ?  Math.round(Number(element.score))  : 0
    });

    let start1 = moment().startOf('month').unix()
    let end1 = moment().endOf('month').unix()
      
    let filter2 = ` a.uuid='${uuid}' and  a.cycle=1  and a.score_time between ${start1} and ${end1} `
    if(code){
      filter2 += ` and a.security_code = ${code}`
    }
    const nowscore = await this.model('servie_building_qualityscore').alias('a').field(`a.building_group_id, a.total_score, a.security_code`)
    .where(filter2).group(`a.building_group_id`).select();
    let nowlist={}
    const buildid = [9999999];
    nowscore.forEach(element => {
      buildid.push(element.building_group_id)
      nowlist[`${element.building_group_id}`] = {total_score: element.total_score,security_code: element.security_code}
    });

    if (code) {
      filter += ` and a.building_group_id in (${buildid})`
    }

    if(params.ordertype==3){
      let lists = await this.model('servie_building_qualityscore').alias('a').field(`a.id,a.building_group_id,a.building_group_name,a.region,a.street,sum(a.status!=1) as record_count, ROUND(sum(if(a.status!=1,a.total_score,0))/sum(a.status!=1)) as record_score`)
      .where(filter).group(`a.building_group_id`).select();
      lists.forEach(element => {
        element['year_score'] = taskNumPendobj[`${element.building_group_id}`] || 0
        element['now_score'] = !think.isEmpty(nowlist[`${element.building_group_id}`]) ? nowlist[`${element.building_group_id}`]['total_score'] : ''
        element['now_security_code'] = !think.isEmpty(nowlist[`${element.building_group_id}`]) ? nowlist[`${element.building_group_id}`]['security_code'] : ''
      });
      if(type==1){
        lists.sort((a, b) => {
          return a.year_score - b.year_score;
        });
      }else if(type==2){
        lists.sort((a, b) => {
          return b.year_score - a.year_score;
        });
      }
      const offset = (_page - 1) * _limit;
      let list = lists.slice(offset,_limit+offset)
      res['count'] =  lists.length
      res['data']  =  list

    }else if(params.ordertype==4){
      let lists = await this.model('servie_building_qualityscore').alias('a').field(`a.id,a.building_group_id,a.building_group_name,a.region,a.street,sum(a.status!=1) as record_count, ROUND(sum(if(a.status!=1,a.total_score,0))/sum(a.status!=1)) as record_score`)
      .where(filter).group(`a.building_group_id`).select();
      lists.forEach(element => {
        element['year_score'] = taskNumPendobj[`${element.building_group_id}`] || 0
        element['now_score'] = !think.isEmpty(nowlist[`${element.building_group_id}`]) ? nowlist[`${element.building_group_id}`]['total_score'] : ''
        element['now_security_code'] = !think.isEmpty(nowlist[`${element.building_group_id}`]) ? nowlist[`${element.building_group_id}`]['security_code'] : ''
      });

      if(type==1){
        lists.sort((a, b) => {
          return a.now_score - b.now_score;
        });
      }else if(type==2){
        lists.sort((a, b) => {
          return b.now_score - a.now_score;
        });
      }
      const offset = (_page - 1) * _limit;
      let list = lists.slice(offset,_limit+offset)
      res['count'] =  lists.length
      res['data']  =  list

    }else{

      res = await this.model('servie_building_qualityscore').alias('a').field(`a.id,a.building_group_id,a.building_group_name,a.region,a.street,sum(a.status!=1) as record_count,ROUND(sum(if(a.status!=1,a.total_score,0))/sum(a.status!=1)) as record_score`)
      .where(filter).order(order).group(`a.building_group_id`).page(_page, _limit).countSelect();
      let sql = `SELECT COUNT(*) as num FROM (
        SELECT a.building_group_id FROM pt_servie_building_qualityscore as a 
        where (${filter})
        GROUP BY a.building_group_id
        ) AS tmp`
      const allcount = await this.query(sql)
      res.count = allcount[0].num || 0

      res.data.forEach(element => {
        element['year_score'] = taskNumPendobj[`${element.building_group_id}`] || 0
        element['now_score'] = !think.isEmpty(nowlist[`${element.building_group_id}`]) ? nowlist[`${element.building_group_id}`]['total_score'] : ''
        element['now_security_code'] = !think.isEmpty(nowlist[`${element.building_group_id}`]) ? nowlist[`${element.building_group_id}`]['security_code'] : ''
      });
    }

    
      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }


  async getBuildyearList(params) {
    try {
      const { beforetime, endtime, region, street, state, code, _page = 1, _limit = 10, ordertype = 0, type = 1, buildname,uuid} = params;
      let filter = ` 1=1 and a.cycle=2`
      if (beforetime && endtime) {
        filter += ` and  a.score_time between ${beforetime} and ${endtime}  `
      }
      if (region) {
        filter += ` and a.region ='${region}' `
      }
      if (street) {
        filter += ` and a.street ='${street}' `
      }
      if (state) {
        filter += ` and a.status =${state} `
      }
      if (code) {
        filter += ` and c.security_code =${code} `
      }
      if (buildname) {
        filter += ` and a.building_group_name like '%${buildname}%' `
      }
      // if(configname){
      //   filter += ` and b.name like '%${configname}%' `
      // }
      if(uuid){
        filter += ` and a.uuid = '${uuid}' `
      }

      let order = `a.building_group_id`
      if (ordertype == 1) {
        order = `record_count`
      } else if (ordertype == 2) {
        order = `record_score`
      }else if (ordertype == 3) {
        order = `prev_score`
      }
      if (type == 1) {
        order += ` ASC `
      } else if (type == 2) {
        order += ` DESC `
      }

      let  st = moment().subtract(1, 'years').startOf('years').unix()
      let  et = moment().subtract(1, 'years').endOf('years').unix()
      let res = await this.model('servie_building_qualityscore').alias('a').join(
        `pt_servie_building_qualityscore as c ON a.building_group_id = c.building_group_id and c.uuid = '${uuid}' and c.score_time  between ${st} and ${et}  `
      ).field(`a.id,a.building_group_id,a.building_group_name,a.region,a.street,sum(a.status!=1) as record_count,ROUND(sum(if(a.status!=1,a.total_score,0))/sum(a.status!=1)) as record_score,c.total_score as prev_score,c.security_code as prev_security_code`)
      .where(filter).order(order).group(`a.building_group_id`).page(_page, _limit).countSelect();

      let sql = `SELECT COUNT(*) as num FROM (
        SELECT a.building_group_id FROM pt_servie_building_qualityscore as a 
        LEFT JOIN pt_servie_building_qualityscore as c ON a.building_group_id = c.building_group_id and c.uuid = '${uuid}' and c.score_time  between ${st} and ${et}
        where (${filter})
        GROUP BY a.building_group_id
        ) AS tmp`
      const allcount = await this.query(sql)
      res.count = allcount[0].num || 0

      return {
        status: 0, data: res
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }


  async getBuildQrcodeDetail(params) {
    try {
      const { id } = params;
      let filter = `building_group_id = ${id} and cycle=1 and (status=1 or status=3)`
      const info = await this.model('servie_building_qualityscore').where(filter).find();
      if (think.isEmpty(info)) return { status: -1, msg: "无数据" };
      return {
        status: 0, data: {
          building_group_name: info.building_group_name,  //建筑群名称
          score: info.total_score, //总分
          reportDate: info.score_time, //受评时间
          security_code:info.security_code,
          encryptid:info.encryptid
        }
      };
    } catch (err) {
      console.log(err)
      const result = think.isError(err) ? err : new Error(err);
      return { status: -1, msg: result.message };
    }
  }


};
