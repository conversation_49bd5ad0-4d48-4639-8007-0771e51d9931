const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    // 允许要访问的表名,默认为模块名
    // 可以在实例化之前修改表名
    tableName = 'planner';
    super(tableName, config);
    /**
     * 字段类型说明,1：显示，0：不显示
     * show：是否显示，add:是否显示添加，edit:是否显示编辑,search：是否搜索,like是否模糊搜索
     * between：是否区间搜索,unique:是否为一,tinyType：自定义类型
     * @tinyType varchar:字符串，int：整型,datetime:时间，date:日期，image:单图片上传，iamages:多图片上传
     * @tinyType file:上传，files:多文件上传,telephone:坐机电话，mobile:手机号码,email:电子邮箱,require:必须输入
     * as：表示从表，从表暂不支持search、like、between
     * addslot,editslot,searchslot
     * @type {*[]}
     */
    this.listFields = [
      {name: 'id'},
      {name: 'fireid', show: 1, add: 1, edit: 1, search: 1},
      {name: 'typeid', show: 1, add: 1, edit: 1},
      {name: 'name', show: 1, add: 1, edit: 1},
      {name: 'dutydes', show: 1, add: 1, edit: 1},
      {name: 'flage', show: 1, add: 1, edit: 1},
      {name: 'operdes', show: 1, add: 1, edit: 1}
    ];
  }
};
