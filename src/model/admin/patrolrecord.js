const Base = require('../base');
const moment = require('moment');
module.exports = class extends Base {
  // async __before() {
  //   this.utils = this.controller('ext/emini/utils');
  // }
  constructor(tableName, config) {
    // 允许要访问的表名,默认为模块名
    // 可以在实例化之前修改表名
    tableName = 'patrol_record';
    super(tableName, config);
  }

    async getList(params) {
        try {
        
        const {_limit=10, _page=1,name='',pid,starttime,endtime,street_code,orderfiled,ordertype=2} = params;

        let filter = ` 1=1 `;

        if(starttime&&endtime){
            filter += ` and a.patrol_time between ${starttime} and ${endtime} `
        }

        if(name){
            filter += ` AND (c.o_name LIKE '%${name}%') `
        }

        if(street_code){
            filter += ` AND b.street_code = '${street_code}' `
        }

        if(pid){
            filter += ` AND a.building_group_id=${pid} `;
        }

        let order = ` a.patrol_time `
   
        if(orderfiled==1){
            order = ` sounts `
        }else if(orderfiled==2){
            order = ` ocount `
        }else if(orderfiled==3){
            order = ` okcount `
        }else if(orderfiled==4){
            order = ` acount `
        }else if(orderfiled==5){
            order = ` hcount `
        }else if(orderfiled==6){
            order = ` rate `
        }

        if(ordertype==1){
            order += ` asc `
        }else{
            order += ` desc `
        }

        const res = await this.alias('a').join([
            'pt_building_group as b on b.id = a.building_group_id',
            'pt_company as c on c.id = a.company_id',
            'pt_patrol_detail as f on f.record_id = a.id'
        ]).order(order).field(`FROM_UNIXTIME(a.patrol_time,'%Y-%m-%d') as datetime,a.building_group_id,a.company_id,b.street_code,b.name as bname,c.o_name as cname,
           count(DISTINCT a.id) as allcount,sum(a.status=1) as ncount,count(DISTINCT a.id) as scount,count(f.id) as ocount,sum(f.resolve=0) as okcount,sum(f.resolve=1) as acount,0 hcount,0 rate`)
        .where(filter).group(`FROM_UNIXTIME(a.patrol_time,'%Y-%m-%d'),a.building_group_id,a.company_id`).page(_page, _limit).countSelect();

        let sql = `SELECT COUNT(*) as num FROM (
            SELECT FROM_UNIXTIME(patrol_time,'%Y-%m-%d') as datetime FROM pt_patrol_record as a 
            LEFT JOIN pt_building_group as b on a.building_group_id = b.id 
            LEFT JOIN pt_company as c on a.company_id = c.id  where (${filter})
            GROUP BY FROM_UNIXTIME(patrol_time,'%Y-%m-%d'), a.building_group_id,a.company_id
            ) AS tmp`

        const allcount = await this.query(sql)
        res.count = allcount[0].num || 0


        return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }


    async detail(params) {
        try {
        
        const {datetime='',pid,company_id} = params;

        let filter = ` 1=1 `;
        let starttime,endtime
        if(datetime){
            starttime = moment(`${datetime}`).startOf('day').unix()
            endtime = moment(`${datetime}`).endOf('day').unix()
        }
        if(starttime&&endtime){
            filter += ` and a.patrol_time between ${starttime} and ${endtime} `
        }

        if(pid){
            filter += ` and a.building_group_id = ${pid} `
        }

        if(company_id){
            filter += ` and a.company_id = ${company_id} `
        }
        let res = await this.alias('a').where(filter).select()
        for (const item of res) {
           item['detail'] = await think.model('patrol_detail').where({record_id:item.id}).select()
        }
        return {status: 0, data: res};
      
        } catch (err) {
            const result = think.isError(err) ? err : new Error(err);
            return {status: -1, msg: result.message};
        }
    }

    async trend(params) {
        try {
        
        const {datetime='',pid,company_id} = params;

        let filter = ` 1=1 `;
        let starttime,endtime
        if(datetime){
            starttime = moment(`${datetime}`).subtract(31, 'days').startOf('day').unix()
            endtime = moment(`${datetime}`).endOf('day').unix()
        }
        if(starttime&&endtime){
            filter += ` and a.patrol_time between ${starttime} and ${endtime} `
        }

        if(pid){
            filter += ` and a.building_group_id = ${pid} `
        }

        if(company_id){
            filter += ` and a.company_id = ${company_id} `
        }

        const res = await this.alias('a').order('a.patrol_time asc').field(`FROM_UNIXTIME(a.patrol_time,'%Y-%m-%d') as datetime,
           count(a.id) as allcount,sum(a.status=1) as ncount`)
        .where(filter).group(`FROM_UNIXTIME(a.patrol_time,'%Y-%m-%d'),a.building_group_id,a.company_id`).select();

        return {status: 0, data: res};
    
        } catch (err) {
            const result = think.isError(err) ? err : new Error(err);
            return {status: -1, msg: result.message};
        }
    }

    async  statisticsList(params) {
        try {
        
        const {_limit=10, _page=1,starttime,endtime,pid,street_code,name,orderfiled,ordertype} = params;
        let filter = ` 1=1 `

        if(starttime&&endtime){
            filter += ` and a.patrol_time between ${starttime} and ${endtime} `
        }

        if(street_code){
            filter += ` AND c.street_code = '${street_code}' `
        }

        if(pid){
            filter += ` AND a.building_group_id=${pid} `;
        }


        if(name){
            filter += ` and (c.name LIKE '%${name}%' or d.o_name  LIKE '%${name}%') `
        }

        let order = ` a.building_group_id `
   
        if(orderfiled==1){
            order = ` counts `
        }else if(orderfiled==2){
            order = ` scount `
        }else if(orderfiled==3){
            order = ` ocount `
        }else if(orderfiled==4){
            order = ` ncount `
        }else if(orderfiled==5){
            order = ` acount `
        }else if(orderfiled==6){
            order = ` hcount `
        }else if(orderfiled==7){
            order = ` rate `
        }

        if(ordertype==1){
            order += ` asc `
        }else if(ordertype==2){
            order += ` desc `
        }

        const res = await this.alias('a').join([
            'pt_building_group as c on c.id = a.building_group_id',
            'pt_company as d on d.id = a.company_id',
            'pt_patrol_detail as f on f.record_id = a.id'
        ]).order(order).field(`a.building_group_id,c.name as bname,c.street_code,GROUP_CONCAT(DISTINCT NULLIF(d.o_name,'')) as company_list,count(DISTINCT FROM_UNIXTIME(a.patrol_time,'%Y-%m-%d')) as counts,
        count(DISTINCT a.patrol_spot) as scount,count(f.id) as ocount,sum(f.resolve=0) as ncount,sum(f.resolve=1) as acount,0 hcount,0 rate`).where(filter).group(`a.building_group_id`).page(_page, _limit).countSelect();

        let sql = `SELECT COUNT(*) as num FROM (
            SELECT a.building_group_id FROM pt_patrol_record as a 
            LEFT JOIN pt_building_group as c on c.id = a.building_group_id 
            LEFT JOIN pt_company as d on a.company_id = d.id 
            LEFT JOIN pt_patrol_detail as f on f.record_id = a.id where (${filter})
            GROUP BY a.building_group_id
            ) AS tmp`

        const allcount = await this.query(sql)
        res.count = allcount[0].num || 0
      
      
        return {status: 0, data: res};
        } catch (err) {
        const result = think.isError(err) ? err : new Error(err);
        return {status: -1, msg: result.message};
        }
    }



};
