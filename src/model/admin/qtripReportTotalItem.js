const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'qtrip_report_total_item';
    super(tableName, config);
    this.listFields = [
      {name: 'id', show: 1, add: 0, edit: 0},
      { name: 'creator', show: 1, add: 1, edit: 1 },
      { name: 'creator_name', show: 1, add: 1, edit: 1 },
      { name: 'deleted', show: 1, add: 1, edit: 1, search: 1 },
      { name: 'created_at', show: 1, add: 1, edit: 0 },
      { name: 'updated_at', show: 1, add: 1, edit: 1 },
      { name: 'report_total_id', show: 1, add: 1, edit: 1 },
      { name: 'department', show: 1, search: 1 },
      { name: 'gas_car_quantity', add: 1, edit: 0, show: 1, search: 1 },
      { name: 'derv_car_quantity', show: 1, add: 1, edit: 1 },
      { name: 'fadian', show: 1, add: 1, edit: 1 },
      { name: 'fire_alarm_quantity', show: 1, add: 1, edit: 1 },
      { name: 'training_quantity', show: 1, add: 1, edit: 1 },
      { name: 'other_quantity', show: 1, add: 1, edit: 1 },
      { name: 'month_mileage', show: 1, add: 1, edit: 1 },
      { name: 'last_month_gas', show: 1, add: 1, edit: 1 },
      { name: 'last_month_derv', show: 1, add: 1, edit: 1 },
      { name: 'cur_get_gas', show: 1, add: 1, edit: 1 },
      { name: 'cur_get_derv', show: 1, add: 1, edit: 1 },
      { name: 'cur_qtrip_gas', show: 1, add: 1, edit: 1 },
      { name: 'cur_qtrip_derv', show: 1, add: 1, edit: 1 },
      { name: 'balance_gas', show: 1, add: 1, edit: 1 },
      { name: 'balance_derv', show: 1, add: 1, edit: 1 }
    ];
  }
};
