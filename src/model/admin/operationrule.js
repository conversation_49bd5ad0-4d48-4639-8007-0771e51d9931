const Base = require('../base');

module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'operation_rule';
    super(tableName, config);
  }


  async getlist(params) {
    try {
      const {_page, _limit,street_code,pid,starttime,endtime,name,firetype,orderfiled,ordertype=2} = params;
      let filter = `1=1`;
      if(starttime&&endtime){
        filter += ` and a.creat_time between ${starttime} and ${endtime} `
      }
      if(street_code){
        filter += ` and b.street_code='${street_code}' `
      }
      if(pid){
        filter += ` and a.building_group_id=${pid} `
      }
      if(firetype){
        filter += ` and a.firetype=${firetype} `
      }
      if(name){
        filter += ` and (c.o_name  LIKE '%${name}%') `
      }
      let order = ` a.creat_time `
    
      if(orderfiled==1){
          order = ` a.update_time `
      }
       
      if(ordertype==1){
          order += ` asc `
      }else{
          order += ` desc `
      }


      const res = await think.model('operation_rule').alias(`a`).join([
        `pt_building_group b on b.id=a.building_group_id`,
        `pt_company c on c.id=a.company_id`,
        `(select code,name from pt_area) as d on b.street_code = d.code `,
      ]).field(`a.*,b.name as bname,c.o_name as cname,d.name as street_name`).where(filter).order(order).page(_page, _limit).countSelect();
      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }


  
  async getlistside(params) {
    try {
      const {pid,company_id} = params;
      let filter = `1=1`;
      if(pid){
        filter += ` and building_group_id=${pid} `
      }
      if(company_id){
        filter += ` and company_id=${company_id} `
      }
      const res = await think.model('operation_rule').field(`creat_time,update_time,title`).where(filter).order('creat_time desc').select();
      return {status: 0, data: res};
    } catch (err) {
      const result = think.isError(err) ? err : new Error(err);
      return {status: -1, msg: result.message};
    }
  }


  async  statisticsList(params) {
    try {
    
    const {_limit=10, _page=1,starttime,endtime,pid,street_code,name,ordertype,orderfiled} = params;
    let filter = ` 1=1 `

    if(starttime&&endtime){
        filter += ` and a.creat_time between ${starttime} and ${endtime} `
    }

    if(street_code){
        filter += ` AND c.street_code = '${street_code}' `
    }

    if(pid){
        filter += ` AND a.building_group_id=${pid} `;
    }


    if(name){
        filter += ` and (c.name LIKE '%${name}%' or d.o_name  LIKE '%${name}%') `
    }

    let order = ` a.building_group_id `


    if(orderfiled==1){
      order = ` counts `
    }

    if(ordertype==1){
        order += ` asc `
    }else if(ordertype==2){
        order += ` desc `
    }

    const res = await this.alias('a').join([
        'pt_building_group as c on c.id = a.building_group_id',
        'pt_company as d on d.id = a.company_id'
    ]).order(order).field(`a.building_group_id,c.name as bname,c.street_code,GROUP_CONCAT(DISTINCT NULLIF(d.o_name,'')) as company_list,count(a.id) as counts`).where(filter).group(`a.building_group_id`).page(_page, _limit).countSelect();

    let sql = `SELECT COUNT(*) as num FROM (
        SELECT a.building_group_id FROM pt_operation_rule as a 
        LEFT JOIN pt_building_group as c on c.id = a.building_group_id 
        LEFT JOIN pt_company as d on a.company_id = d.id  where (${filter})
        GROUP BY a.building_group_id
        ) AS tmp`

    const allcount = await this.query(sql)
    res.count = allcount[0].num || 0
  
  
    return {status: 0, data: res};
    } catch (err) {
    const result = think.isError(err) ? err : new Error(err);
    return {status: -1, msg: result.message};
    }
}





};
