const Base = require('../base');
module.exports = class extends Base {
  constructor(tableName, config) {
    tableName = 'qtrip_report_vehicle_item';
    super(tableName, config);
    this.listFields = [
      {name: 'id', show: 1, add: 0, edit: 0},
      { name: 'creator', show: 1, add: 1, edit: 1 },
      { name: 'creator_name', show: 1, add: 1, edit: 1 },
      { name: 'deleted', show: 1, add: 1, edit: 1, search: 1 },
      { name: 'created_at', show: 1, add: 1, edit: 0 },
      { name: 'updated_at', show: 1, add: 1, edit: 1 },
      { name: 'report_vehicle_id', show: 1, add: 1, edit: 1 },
      { name: 'vehicle_model', show: 1, search: 1 },
      { name: 'vehicle_code', show: 1, search: 1 },
      { name: 'oil_type', add: 1, edit: 0, show: 1, search: 1 },
      { name: 'total_mileage', show: 1, add: 1, edit: 1 },
      { name: 'month_mileage', show: 1, add: 1, edit: 1 },
      { name: 'fadian', show: 1, add: 1, edit: 1 },
      { name: 'fire_alarm_quantity', show: 1, add: 1, edit: 1 },
      { name: 'training_quantity', show: 1, add: 1, edit: 1 },
      { name: 'other_quantity', show: 1, add: 1, edit: 1 },
      { name: 'month_qtrip', show: 1, add: 1, edit: 1 },
      { name: 'balance', show: 1, add: 1, edit: 1 },
      { name: 'fuel_consumption', show: 1, add: 1, edit: 1 }
    ];
  }
};
