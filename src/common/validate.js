const ERR = require('./errorCode');
module.exports = {
  checkSearchValidate(searchs, params, fieldTypeArry) {
    // 搜索不检查的字段类型
    const nocheck = ['phonenumber', 'mobile', 'email'];
    for (const search of searchs) {
      for (const field of fieldTypeArry) {
        if (search === field.name) {
          const rlt = this.checkCondition(field, params, nocheck);
          if (rlt !== 1) {
            return rlt;
          }
        }
      }
    }
    return 1;
  },
  checkEditValidate(params, schema, listFields) {
    const fieldTypeArry = []; // 字段类型
    for (const item of listFields) {
      let schemaObj = schema[item.name];
      if (!think.isEmpty(schemaObj)) {
        schemaObj = {
          name: schemaObj.name,
          type: schemaObj.type,
          tinyType: schemaObj.tinyType
        };
        fieldTypeArry.push(think.extend(schemaObj, item));
      }
    }
    for (const i in params) {
      for (const field of fieldTypeArry) {
        if (i === field.name) {
          const rlt = this.checkCondition(field, params);
          if (rlt !== 1) {
            return rlt;
          }
        }
      }
    }
    return 1;
  },
  checkCondition(resData, params, ischeck) {
    let checkValue = 1;
    if (resData) {
      switch (resData.tinyType) {
        case 'int':
          break;
        case 'date':
          checkValue = this.checkDate(params[resData.name]) ? 1 : ERR.validateErro.dateError + ':' + params[resData.name];
          if (checkValue !== 1) {
            return checkValue;
          }
          break;
        case 'datetime':
          checkValue = this.checkDate(params[resData.name]) ? 1 : ERR.validateErro.datetimeError + ':' + params[resData.name];
          if (checkValue !== 1) {
            return checkValue;
          }
          break;
        case 'mobile':
          if (ischeck && ischeck.indexOf('mobile') > -1) {
            return 1;
          } else {
            checkValue = this.checkMobile(params[resData.name]) ? 1 : ERR.validateErro.mobileError + ':' + params[resData.name];
            if (checkValue !== 1) {
              return checkValue;
            }
          }

          break;
        case 'telephone':
          if (ischeck && ischeck.indexOf('telephone') > -1) {
            return 1;
          } else {
            checkValue = this.checkTelephone(params[resData.name]) ? 1 : ERR.validateErro.telephoneError + ':' + params[resData.name];
            if (checkValue !== 1) {
              return checkValue;
            }
          }
          break;
        case 'email':
          if (ischeck && ischeck.indexOf('email') > -1) {
            return 1;
          } else {
            checkValue = this.checkEmail(params[resData.name]) ? 1 : ERR.validateErro.emailError + ':' + params[resData.name];
            if (checkValue !== 1) {
              return checkValue;
            }
          }
          break;
        case 'safevarchar':
          checkValue = this.checkVarchar(params[resData.name]) ? 1 : ERR.validateErro.varcharError + ':' + params[resData.name];
          if (checkValue !== 1) {
            return checkValue;
          }
          break;
      }
    }
    return checkValue;
  },
  // 判断是否是日期格式
  checkDate(date) {
    if (think.isEmpty(date)) {
      return true;
    }
    if (isNaN(date) && !isNaN(Date.parse(date))) {
      return true;
    } else {
      return false;
    }
  },
  // 手机号码验证
  checkMobile(phone) {
    if (think.isEmpty(phone)) {
      return false;
    }
    if (!(/^1[3456789]\d{9}$/.test(phone))) {
      return false;
    } else {
      return true;
    }
  },
  // 坐机号码验证
  checkTelephone(tel) {
    if (think.isEmpty(tel)) {
      return false;
    }
    const phone = /^0\d{2,3}-?\d{7,8}$/;
    return phone.test(tel);
  },
  // 邮箱验证
  checkEmail(mail) {
    if (think.isEmpty(mail)) {
      return false;
    }
    var szReg = new RegExp('^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$'); // 正则表达式
    if (szReg.test(mail)) {
      return true;
    } else {
      return false;
    }
  },
  // 验证字符串是否包含特殊符号
  checkVarchar(value) {
    if (think.isEmpty(value)) {
      return false;
    }
    const pattern = new RegExp("[`~!@#$^&*=|{}':;'\\[\\]<>《》?~！@#￥……&*——|{}【】‘’；：”“'\"]");
    if (!pattern.test(value)) {
      return true;
    } else {
      return false;
    }
  },
  dateTime() {
    return Math.round(new Date().getTime() / 1000);
  }
};
