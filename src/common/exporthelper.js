const path = require('path');
const { readFileSync } = require('fs');
const ejsExcel = require('ejsexcel');

exports.exportExcelAsync = async function(ctx, sourceFilePath, output, data) {
  const sourceFile = path.join(__dirname, '../../www/static/excel', sourceFilePath);
  const exlBuf = readFileSync(sourceFile);

  const renderReuslt = await ejsExcel.renderExcel(exlBuf, data);

  ctx.res.setHeader('Content-Type', 'application/vnd.openxmlformats');
  ctx.attachment(output);
  ctx.body = renderReuslt;
};
