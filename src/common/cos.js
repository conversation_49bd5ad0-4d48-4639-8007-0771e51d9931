const COS = require('cos-nodejs-sdk-v5')
const ObjectsToCsv = require('objects-to-csv');
const path = require('path');
const fs = require('fs')
const { promisify } = require('util');
module.exports = {
  getCOS: function () {
    return new COS({
      SecretId: think.config('sysconfig').tencentSecretId,
      SecretKey: think.config('sysconfig').tencentSecretKey
    })
  },

 async saveFile (data,header=[]) {
    try {
        const csv = new ObjectsToCsv(data);
        const uuid = think.uuid('v1').replace(/-/g, '');
        const tempFilePath = path.join(think.ROOT_PATH, "/tempfile");
        think.mkdir(tempFilePath);
        let fileName = `${uuid}.csv`
        let filePath = `${tempFilePath}/${fileName}`; 
        if(!think.isEmpty(header)){
            const headerStr = header.join(',') + "\n"           // 将表头转换为字符串格式
            await fs.writeFileSync(filePath, headerStr);       // 写入文件
        }
        let csvdata = await csv.toDisk(filePath,{append:true}); //保存文件
        return {filePath,fileName}
    } catch (error) {
        think.logger.info(error);
        return {}
    }
  },

  async putFileObject(filePath,fileName) {
    const ossPrefix = think.config('sysconfig').ossPrefix; //OSS前缀 主要用来区分环境
    const keyPrefix = think.config('sysconfig').ossTempFilePrefix; //临时文件前缀
    try {
      const cos = this.getCOS()
      const uploadRes = await cos.putObject({
        Bucket: think.config('sysconfig').shBucket, /* 填入您自己的存储桶，必须字段 */
        Region: think.config('sysconfig').cosRegion,  /* 存储桶所在地域，例如ap-beijing，必须字段 */
        Key: `${ossPrefix}/${keyPrefix}/${fileName}`,  /* 存储在桶里的对象键（例如1.jpg，a/b/test.txt），必须字段 */
        Body: fs.createReadStream(filePath),                /* 必须 */
        ContentType: 'text/csv',
        ContentLength: fs.statSync(filePath).size,
        ACL: 'public-read',
        onTaskReady: function (taskId) {               /* 非必须 */
          console.log(taskId);
        },
        onProgress: function (progressData) {           /* 非必须 */
          console.log(JSON.stringify(progressData));
        },
        onFileFinish: function (err, data, options) {   /* 非必须 */
          console.log(options.Key + '上传' + (err ? '失败' : '完成'));
        },
      });
      return {status: 0, data: uploadRes};
    } catch (err) {
      think.logger.info(err);
      return {status: -1, msg: err.code};
    }
  },

  async getObjectUrl(fileName) {
    const ossPrefix = think.config('sysconfig').ossPrefix; //OSS前缀 主要用来区分环境
    const keyPrefix = think.config('sysconfig').ossTempFilePrefix; //临时文件前缀
    try {
      const cos = this.getCOS()
      const uploadRes = await cos.getObjectUrl(
        {
            Bucket: think.config('sysconfig').shBucket, /* 填入您自己的存储桶，必须字段 */
            Region: think.config('sysconfig').cosRegion,  /* 存储桶所在地域，例如ap-beijing，必须字段 */
            Key: `${ossPrefix}/${keyPrefix}/${fileName}`,  /* 存储在桶里的对象键（例如1.jpg，a/b/test.txt），必须字段 */
            Sign: false /* 获取带签名的对象 URL */
        },
        function (err, data) {
          if (err) return console.log(err);
          /* url为对象访问 url */
          var url = data.Url;
          /* 复制 downloadUrl 的值到浏览器打开会自动触发下载 */
        
        }
      );
      return {status: 0, data: uploadRes};
    } catch (err) {
      think.logger.info(err);
      return {status: -1, msg: err.code};
    }
  },

  async deleteMultipleObject(fileList) {
    const ossPrefix = think.config('sysconfig').ossPrefix; //OSS前缀 主要用来区分环境
    const keyPrefix = think.config('sysconfig').ossTempFilePrefix; //临时文件前缀
    const fileObjects = fileList.map((file)=>{
       return {Key: `${ossPrefix}/${keyPrefix}/${file}`}
    })
    try {
        const cos = this.getCOS()
        cos.deleteMultipleObject({
        Bucket: think.config('sysconfig').shBucket, /* 填入您自己的存储桶，必须字段 */
        Region: think.config('sysconfig').cosRegion,  /* 存储桶所在地域，例如ap-beijing，必须字段 */
        Objects: fileObjects
    }, function(err, data) {
        think.logger.info(err || data);
    });
    } catch (err) {
      think.logger.info(err);
    }
  },


  async deleteFileList(fileList){
    const tempFilePath = path.join(think.ROOT_PATH, "/tempfile");
    const fileObjects = fileList.map((file)=>{
        return `${tempFilePath}/${file}`
    })
      // 遍历文件列表并删除每个文件
    fileObjects.forEach(file => {
        fs.unlink(file, err => {
        if (err) throw err;
        console.log(`已删除文件: ${file}`);
        });
    });
  },


    async saveDataFile(data,ctx,header=[]){
        try { 
            let {filePath,fileName} =  await this.saveFile(data,header)
            let  fileUrl = `${ctx.origin}/tempfile/${fileName}`
            if(!think.isIP(ctx.hostname)){
                let resdata = await this.putFileObject(filePath,fileName)
                if(resdata.status==0){
                    fileUrl = `https://${resdata.data.Location}`
                }
            }
            return fileUrl
        } catch (error) {
            think.logger.info(error)    
            return ""
        }
    },

    async delteDataFile(){

        const readdir = promisify(fs.readdir);
        const stat = promisify(fs.stat);
        const folderPath = path.join(think.ROOT_PATH, "/tempfile");
        think.mkdir(folderPath);
        const files = await readdir(folderPath);
        const filteredFiles = await Promise.all(
            files.map(async (file) => {
                const filePath = path.join(folderPath, file);
                const fileStats = await stat(filePath);
                const fileAge = Date.now() - fileStats.birthtimeMs;
                const hoursSinceCreation = fileAge / (1000 * 60 * 60);
                if (hoursSinceCreation > 24) {
                    return file;
                }
            })
        );
        let validFiles = filteredFiles.filter((file) => file);
        if(!think.isEmpty(validFiles)){
            this.deleteMultipleObject(validFiles)
            this.deleteFileList(validFiles)
        }
    }

}
