const {think} = require('thinkjs');
const {publicError, validateErro} = require('./errorCode');
const ERR = require('./errorCode');
module.exports = {
  checkSearchValidate(searchs, params, fieldTypeArry) {
    // 搜索不检查的字段类型
    const nocheck = ['phonenumber', 'mobile', 'email'];

    for (const search of searchs) {
      for (const field of fieldTypeArry) {
        if (search === field.name) {
          const rlt = this.checkCondition(field, params, nocheck);
          if (rlt !== 1) {
            return rlt;
          }
        }
      }
    }
    return 1;
  },
  // 检查添加和编辑表单
  checkEditValidate(params, schema, listFields, defaultTable) {
    const fieldTypeArry = []; // 字段类型
    let fieldTableName
    for (const item of listFields) {
      let field
      // 兼容两种配置格式
      if (think.isString(item)) {
        item = {
          field: item
        }
      }
      // 从数据库中取出表结构
      fieldTableName = item.table || defaultTable
      let schemaObj = schema[fieldTableName][item.field];
      if (!think.isEmpty(schemaObj)) {
        schemaObj = {
          field: schemaObj.name,
          type: schemaObj.type,
          tinyType: schemaObj.tinyType
        };
        fieldTypeArry.push(think.extend(schemaObj, item));
      }
    }
    // 取出每一个需要保存的字段，拿去检查值
    let foundValue
    for (const field of fieldTypeArry) {
      foundValue = false
      for (const i in params) {
        if (i === field.field) {
          foundValue = true
          const rlt = this.checkCondition(field, params);
          if (rlt !== 1) {
            return rlt;
          }
        }
      }
      // 如果在提交的参数中没有找到，并且是必填的，检查不通过
      if (!foundValue && field.required) {
        return validateErro.requiredError + ':' + field.field
      }
    }
    return 1;
  },
  checkCondition(resData, params, ischeck) {
    // 值的类型检查是否通过，1通过
    // 不通过就返回一个描述字符串，发回前端
    let checkValue = 1;
    if (resData) {
      const dataType = resData.dataType || resData.tinyType
      const field = resData.field
      // 判断是否必填字段
      if (resData.required) {
        if (params[field] === undefined || think.isEmpty(params[field])) {
          return ERR.validateErro.requiredError + ':' + field
        }
      }
      // 判断长度
      if (resData.maxLength || resData.minLength) {
        let rlt = this.checkLength(params[field], resData.maxLength, resData.minLength)
        if (!rlt) {
          return ERR.validateErro.lengthError + ':' + `${resData.minLength}<${field}<${resData.maxLength}`
        }
      }
      switch (dataType) {
        case 'int':
        case 'isint':
          const num = parseInt(params[field]);
          if (think.isNumber(num) || num === 0) {
            checkValue = 1;
          } else {
            checkValue = ERR.validateErro.intError + ':' + params[field];
          }
          break;
        case 'date':
          checkValue = this.checkDate(params[field]) ? 1 : ERR.validateErro.dateError + ':' + params[field];

          break;
        case 'datetime':
          checkValue = this.checkDate(params[field]) ? 1 : ERR.validateErro.datetimeError + ':' + params[field];

          break;
        case 'mobile':
          if (ischeck && ischeck.indexOf('mobile') > -1) {

          } else {
            checkValue = this.checkMobile(params[field]) ? 1 : ERR.validateErro.mobileError + ':' + params[field];

          }
          break;
        case 'telephone':
          if (ischeck && ischeck.indexOf('telephone') > -1) {

          } else {
            checkValue = this.checkTelephone(params[field]) ? 1 : ERR.validateErro.telephoneError + ':' + params[field];

          }
          break;
        case 'email':
          if (ischeck && ischeck.indexOf('email') > -1) {
          } else {
            checkValue = this.checkEmail(params[field]) ? 1 : ERR.validateErro.emailError + ':' + params[field];

          }
          break;
        case 'varchar':
          checkValue = this.checkVarchar(params[field]) ? 1 : ERR.validateErro.varcharError + ':' + params[field];
          break;
        // 默认不允许危险字符过
        default:
          checkValue = this.checkSQLHack(params[field]) ? 1 : ERR.validateErro.varcharError + ':' + params[field];
          break;
      }
    }
    return checkValue;
  },
  // 判断是否是日期格式
  checkDate(date) {
    // 允许不填，不允许乱填
    if (think.isEmpty(date)) {
      return true;
    }
    if (isNaN(date) && !isNaN(Date.parse(date))) {
      return true;
    } else {
      return false;
    }
  },
  // 手机号码验证
  checkMobile(phone) {
    if (think.isEmpty(phone)) {
      return true;
    }
    if (!(/^1[3456789]\d{9}$/.test(phone))) {
      return false;
    } else {
      return true;
    }
  },
  // 坐机号码验证
  checkTelephone(tel) {
    if (think.isEmpty(tel)) {
      return true;
    }
    const phone = /^0\d{2,3}-?\d{7,8}$/;
    return phone.test(tel);
  },
  // 邮箱验证
  checkEmail(mail) {
    if (think.isEmpty(mail)) {
      return true;
    }
    var szReg = new RegExp('^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$'); // 正则表达式
    if (szReg.test(mail)) {
      return true;
    } else {
      return false;
    }
  },
  // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  isCardNo(card) {
    if (think.isEmpty(card)) {
      return true;
    }
    var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!reg.test(card)) {
      return false;
    }
    return true;
  },
  // 验证字符串是否包含特殊符号
  checkVarchar(value) {
    if (think.isEmpty(value)) {
      return true;
    }
    const pattern = new RegExp("[`~!@#$^&*()=|{}':;\\[\\]<>《》?~！@#￥……&*（）——|{}【】‘’；：”“'。，、？\"]");
    if (!pattern.test(value)) {
      return true;
    } else {
      return false;
    }
  },
  // 检查sql注入字符
  checkSQLHack(value) {
    if (think.isEmpty(value)) {
      return true;
    }
    value = value.toString()
    const pattern = new RegExp("[*(){}';\\[\\]]");
    if (!pattern.test(value)) {
      return true;
    } else {
      return false;
    }
  },
  dateTime() {
    return Math.round(new Date().getTime() / 1000);
  },
  // 检查长度
  checkLength(value, max, min) {
    let checkValue = false
    if (think.isEmpty(value)) {
      return checkValue
    }
    if (max && min) {
      if (value.length >= min && value.length <= max) {
        checkValue = true
      }
    } else if (max) {
      if (value.length <= max) {
        checkValue = true
      }
    } else if (min) {
      if (value.length >= min) {
        checkValue = true
      }
    }
    return checkValue
  }
};
