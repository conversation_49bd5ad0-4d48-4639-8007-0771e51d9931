
const Redis = require('ioredis');
const config = require('../config.js');

const redisConfig = {
  host: config.redis.host,
  port: config.redis.port,
  password: config.redis.password,
  db: config.redis.db
};

const redis = new Redis(redisConfig);

redis.on('connect', () => {
  console.log('redis connected...', redisConfig);
});

redis.on('error', (err) => {
  console.log('ioredis error...', err);
});

module.exports = redis;
