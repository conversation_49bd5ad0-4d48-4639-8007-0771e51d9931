'use strict';
const amqp = require('amqplib');
// const config = require('../config');

const mqConn = think.config('mq').conn;

function sleep(miliSeconds = 0) {
  return new Promise((resolve) => {
    setTimeout(resolve, miliSeconds);
  });
}


class Rabbitmq {
  constructor() {
    this.lv1_queue_alarm_cache = 'lv1_alarm_cache';
  this.ch = null;
  }
  async connect(onConsume = null) {
    const self = this;
    try {
      console.log(mqConn);
      // console.log('connect to rabbitmq...', config.mq);
      const conn = await amqp.connect(mqConn);
      this.ch = await conn.createChannel();
      conn.on('error', async function(err) {
        console.log('rabbitmq channel err ------', err);
        await self.reconnect(onConsume);
      });
      conn.on('close', async function() {
        console.log('rabbitmq channel close ------');
        await self.reconnect(onConsume);
      });
      const queueOption = {durable: true};
      await this.ch.assertQueue(this.lv1_queue_alarm_cache, queueOption);
      if (onConsume) {
        // console.log('callback------', onConsume);
        // console.log(typeof onConsume);
        await onConsume();
      }
    } catch (err) {
      console.log(new Date() + 'connect err----', err);
      await self.reconnect(onConsume);
    }
  }

  async sendToQueue (queue, message, option = null) {
    let opt = {persistent: true};
    if (option) {
      opt = Object.assign(option, {persistent: true});
    }

    await this.ch.sendToQueue(queue, Buffer.from(JSON.stringify(message)), opt);
  }
  async sendToExchange (ex, routekey, message) {
    await this.ch.publish(ex, routekey, Buffer.from(JSON.stringify(message)));
  }

  async reconnect(onConsume) {
    console.log(new Date() + 'reconnect err----');
    await sleep(1000);
    await this.connect(onConsume);
  }

  async work() {}

  async consume (queue) {
    await this.ch.prefetch(100);
    await this.ch.consume(queue, this.work);
  }

  async ack(message) {
    await this.ch.ack(message);
  }

  async nack(message) {
    await this.ch.nack(message);
  }
}

module.exports = Rabbitmq;
