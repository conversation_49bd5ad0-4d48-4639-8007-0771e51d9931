module.exports = {
  serverError: '请联系管理员',
  loginError: {
    noUsername: '用户不存在',
    errorPassword: '密码错误',
    pleaseLogin: '请登陆',
    checkParams: '请检查参数',
    userisHave: '该用户已存在',
    regPassError: '两次密码不一致',
    regFail: '注册失败',
    regSuccess: '注册成功',
    noLogin: '你已被禁止登陆',
    errorCode: '手机验证码错误',
    noJurisdiction: '当前用户暂无对应权限'
  },
  publicError: {
    addError: '请设置允许插入的字段',
    addFieldError: '插入存在不合法的字段',
    modifyFieldError: '修改存在不合法字段',
    lackIdError: '修改时缺少关键字段ID',
    delError: '删除失败,没有条件字段',
    checkIdError: '存在不合法的ID',
    uploadError: '上传失败',
    uploadFileError: '上传文件错误',
    editError: '编辑失败',
    delExendError: '删除失败',
    importError: '导入失败',
    exportError: '导出失败',
    addExendError: '添加失败',
    delMostError: '批量删除失败',
    repeatError: '存在相同数据',
    selectError: '查询失败',
    parametersError: '参数有误',
    dataEmptyError: '数据为空',
    jwtError: 'token验证失败,请重新登陆',
    mustFieldError: '有必填选项未填写',
    authorityError: '权限错误',
    droplistSearchValueEmpty: '下拉列表搜索val和display必须选填一个值',
    droplistSelfFieldEmpty: '下拉列表搜索必须提供自己的字段名',
    droplistIsEmpty: '下拉列表为空',
    notFindInfo: '未找到相关信息',
    numError: '数量超过限制',
    sizeError: '大小超过限制',
    rolehaveuserError: '该角色下存在用户',
    rolehaveSubroleError: '该角色存在子角色',
    buildIsEmpty: '建筑为空',
    mustFiledIsEmpty: '缺少必填字段',
    groupidError: '角色id不正确',
    delAdminError: '不允许操作管理员'
  },
  publicSuccess: {
    editSucc: '编辑成功',
    delSucc: '删除成功',
    importSucc: '导入成功',
    exportSucc: '导出成功',
    controlSucc: '操作成功',
    addSucc: '添加成功',
    delMostSucc: '批量删除成功',
    uploadSucc: '上传成功'
  },
  excelUpErro: {
    upFieldError: '上传的数据存在不允许的字段',
    checkDataError: '请检查上传数据',
    exceedingLimitError: '超过限制数量',
    dataNameError: '数据重名'
  },
  validateErro: {
    intError: '为空或整型格式错误',
    dateError: '为空或日期格式错误',
    datetimeError: '为空或时间格式错误',
    mobileError: '为空或手机格式错误',
    telephoneError: '为空或坐机格式错误',
    emailError: '为空或邮箱格式错误',
    varcharError: '为空或包含非法字符'
  }

}
;
