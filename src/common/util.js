const _ = require('underscore');
const redis = require('./ioredis');
const moment = require('moment');
const crypto = require('crypto');
const util = {
  getClientIp(ctx) {
    const strpos = ctx.ip.indexOf('.');
    const ip = [2, 3, 4].includes(strpos) ? ctx.ip : ctx.ip.substring(7);
    return ip;
  },
  format(date, formatStr) {
    date = new Date(date);
    const preFun = (val) => {
      if (val < 10) {
        return '0' + val;
      }
      return val;
    };
    const year = date.getFullYear();
    const month = preFun(date.getMonth()+1);
    const day = preFun(date.getDate());
    const hour = preFun(date.getHours());
    const minute = preFun(date.getMinutes());
    const second = preFun(date.getSeconds());
    if (formatStr === 'Y-m-d') {
      return year + '-' + month + '-' + day;
    }
    if (formatStr === 'ymdHis') {
      const y = String(year).substr(2);
      return `${y}${month}${day}${hour}${minute}${second}`;
    }
    if (formatStr === 'Y/m/d H:i:s') {
      return year + '/' + month + '/' + day + ' ' + hour + ':' + minute + ':' + second;
    }
    if (formatStr === 'Y-m-d H:i:s') {
      return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
    }
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
  },
  //获取某年的某天是第几周
  getWeekNumber(now) {
    // 判断年份是否为润年
    const isLeapYear = (year) => {
      return (year % 400 == 0) || (year % 4 == 0 && year % 100 != 0);
    };
    // 获取某一年份的某一月份的天数
    const getMonthDays = (year, month) => {
      return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month] || (isLeapYear(year) ? 29 : 28);
    };
    var year = now.getFullYear(),
      month = now.getMonth(),
      days = now.getDate();
    //那一天是那一年中的第多少天
    for (var i = 0; i < month; i++) {
      days += getMonthDays(year, i);
    }

    //那一年第一天是星期几
    var yearFirstDay = new Date(year, 0, 1).getDay() || 7;

    var week = null;
    if (yearFirstDay == 1) {
      week = Math.ceil(days / yearFirstDay);
    } else {
      days -= (7 - yearFirstDay + 1);
      week = Math.ceil(days / 7) + 1;
    }

    return week;
  },
  getRandom: function (min, max) {
    max = max + 1;
    const ret = parseInt(min + Math.random() * (max - min));
    return ret;
  },

  randomStr: function (range) {
    if (!range) {
      range = 16;
    }
    return crypto.randomBytes(range).toString('hex');
  },
  getTaskCode() {
    const now = new Date();
    const time = now.getTime();
    const date = this.format(now, 'ymdHis');
    const second = String(time).substr(10, 3);
    const rand = this.randomStr(2);
    return `${date}${second}${rand}`;
  },

  getSign(appSecret, signParam) {
    const keys = _.keys(signParam);
    // console.log("keys", keys);
    const list = _.sortBy(keys);
    console.log('list', list);
    let str = appSecret;
    _.each(list, function (v, k) {
      if (typeof signParam[v] === 'object') {
        signParam[v] = JSON.stringify(signParam[v]);
      }
      str += v + signParam[v];
    });
    str += appSecret;
    console.log('str', str);
    const ret = crypto.createHash('md5').update(str).digest('hex');
    return ret.toUpperCase();
  },
  // 获取当前月的天数
  getDate(year, month) {
    var d = new Date(year, month, 0);
    return d.getDate();
  },
  getBuildingIndex() {
    return 1;
  },
  async getOrderNumber(pre, type) {
    const date = moment().format('yyyyMMDD');
    let serialNumber = await redis.incr(`key:${pre}:${type}:${date}`);
    serialNumber = padding(serialNumber, 4);
    return `${pre}${type}${date}${serialNumber}`;
  },
  async getcodeNumber(pre, type) {
    let serialNumber = await redis.incr(`key:${pre}:${type}`);
    serialNumber = padding(serialNumber, 4);
    return `1000${serialNumber}`;
  }
};

function padding(num, length) {
  for (let len = (num + '').length; len < length; len = num.length) {
    num = '0' + num;
  }
  return num;
}

module.exports = util;
