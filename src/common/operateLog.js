
const serviceOperateList = [
  {path: '/frontend/service/create', group: 'certification', group_name: '服务商认证', operate: '添加了认证信息', field: ''},
  {path: '/frontend/service/update', group: 'certification', group_name: '服务商认证', operate: '修改了认证信息', field: ''},
  {path: '/frontend/buildinggroup/create', group: 'building', group_name: '建筑信息', operate: '创建了建筑群', field: 'name'},
  {path: '/frontend/buildinggroup/update', group: 'building', group_name: '建筑信息', operate: '修改了建筑群', field: 'name'},
  {path: '/frontend/employee/add', group: 'employee', group_name: '人员备案', operate: '添加了人员备案', field: 'name'},
  {path: '/frontend/employee/edit', group: 'employee', group_name: '人员备案', operate: '修改了人员备案', field: 'name'},
  {path: '/frontend/employee/delete', group: 'employee', group_name: '人员备案', operate: '删除了人员备案', field: 'name'}
];

const manageOperateList = [
  {path: '/admin/firehouse/add', group: 'firehouse', group_name: '消防站', operate: '添加了消防站', field: 'name,code,type,captain_name,captain_mobile,duty_telephone,address,area_scope'},
  {path: '/admin/firehouse/edit', group: 'firehouse', group_name: '消防站', operate: '修改了消防站', field: 'name,captain_name,area_scope,captain_mobile,duty_telephone'},
  {path: '/admin/firehouse/delete', group: 'firehouse', group_name: '消防站', operate: '删除了消防站', field: 'id'},
  {path: '/admin/buildinggroup/delete', group: 'buildinggroup', group_name: '建筑群', operate: '删除了建筑群', field: 'id'},
  {path: '/admin/buildinggroup/create', group: 'buildinggroup', group_name: '建筑群', operate: '添加了建筑群', field: 'name,superviselevel,regulatorylevel,usenature,address,building_num'},
  {path: '/admin/buildinggroup/edit', group: 'buildinggroup', group_name: '建筑群', operate: '修改了建筑群', field: 'name,building_num,have_ctrlroom,usetime,has_firelift,has_refuge,has_auto_fire,coveredarea,zdarea,livepeoplenum,buildingequity,address'},
  {path: '/admin/building/update', group: 'building', group_name: '建筑', operate: '修改了建筑', field: 'b_name,b_area,b_zd_area,b_height,up_floor,b_zc_area,up_floor_area,under_floor,under_floor_area,b_sort,b_strture,b_strture1,fire_rate,fire_danger,mostworker,use_type,use_kind,have_fireproof,refuge_number,refuge_place,refuge_area,lift_count,lift_place,have_ctrlroom,ctrlroom_place'},
  {path: '/admin/building/create', group: 'building', group_name: '建筑', operate: '添加建筑', field: 'b_name,up_floor,under_floor'},
  {path: '/admin/building/delete', group: 'building', group_name: '建筑', operate: '删除了建筑', field: 'id'},
  {path: '/admin/buildinggroup/updateCompanyScope', group: 'buildinggroup', group_name: '建筑群', operate: '建筑群设置入住单位', field: 'name'},
  {path: '/admin/unitlist/edit', group: 'unitlist', group_name: '单位', operate: '修改了入住单位', field: 'name,email,o_address,o_linkman,o_linkphone,o_name,reg_address,setup_date,legal_name,legal_phone,legal_idcard,business_start,business_end,register_status,o_nature'},
  {path: '/admin/employee/add', group: 'employee', group_name: '人员', operate: '添加了人员', field: 'name,o_name,education,position_type,identity_card_number,entry_date,certificate_type,certificate_number'},
  {path: '/admin/employee/edit', group: 'employee', group_name: '人员', operate: '修改了人员', field: 'name,o_name,education,position_type,identity_card_number,entry_date,certificate_type,certificate_number'},
  {path: '/admin/employee/delete', group: 'employee', group_name: '人员', operate: '删除了人员', field: 'id'},
  {path: '/admin/buildingkeypart/add', group: 'buildingkeypart', group_name: '重点部位', operate: '添加了重点部位', field: 'name,b_name,floor_id,danger_source,owner'},
  {path: '/admin/buildingkeypart/edit', group: 'buildingkeypart', group_name: '重点部位', operate: '修改了重点部位', field: 'name,b_name,floor_id,danger_source,owner'},
  {path: '/admin/buildingkeypart/delete', group: 'buildingkeypart', group_name: '重点部位', operate: '删除了重点部位', field: 'id'},
  {path: '/admin/securityinstitution/add', group: 'securityinstitution', group_name: '消防安全制度', operate: '添加了消防安全制度', field: 'name,type,company_id'},
  {path: '/admin/securityinstitution/edit', group: 'securityinstitution', group_name: '消防安全制度', operate: '修改了消防安全制度', field: 'name,name,type,company_id'},
  {path: '/admin/securityinstitution/delete', group: 'securityinstitution', group_name: '消防安全制度', operate: '删除了消防安全制度', field: 'id'},
  {path: '/admin/securitytraining/add', group: 'securityinstitution', group_name: '消防安全培训记录', operate: '添加了消防安全培训记录', field: 'name,type,company_id,created_at,target'},
  {path: '/admin/securitytraining/edit', group: 'securityinstitution', group_name: '消防安全培训记录', operate: '修改了消防安全制度', field: 'participator,num,content'},
  {path: '/admin/securitytraining/delete', group: 'securityinstitution', group_name: '消防安全培训记录', operate: '删除了消防安全制度', field: 'id'},
  {path: '/admin/outfireplan/add', group: 'securityinstitution', group_name: '灭火和疏散预案', operate: '添加了灭火和疏散预案', field: 'name,company_id,key_part_id,created_at'},
  {path: '/admin/outfireplan/edit', group: 'securityinstitution', group_name: '灭火和疏散预案', operate: '修改了灭火和疏散预案', field: 'name,company_id,key_part_id,created_at,record_name,department,leader,person,description,result'},
  {path: '/admin/outfireplan/delete', group: 'securityinstitution', group_name: '灭火和疏散预案', operate: '删除了灭火和疏散预案', field: 'id'},
  {path: '/admin/project/material/material/add', group: 'material', group_name: '物资列表', operate: '添加了物资', field: 'devsystem_name,material_type,bid,fid,brand,type,specifications,devnumber,manufacturing_date,install_date,warranty_date,maintenance_cycle,remarks'},
  {path: '/admin/project/material/material/edit', group: 'material', group_name: '物资列表', operate: '修改了物资', field: 'devsystem_name,material_type,bid,fid,brand,type,specifications,devnumber,manufacturing_date,install_date,warranty_date,maintenance_cycle,remarks'},
  {path: '/admin/service/enableAccount', group: 'service', group_name: '服务商', operate: '停用了账号', field: 'value'},
  {path: '/admin/service/accessAccount', group: 'service', group_name: '服务商', operate: '停止接入', field: 'value'},
  {path: '/admin/unitlist/delete', group: 'unitlist', group_name: '建筑管理单位', operate: '删除了建筑管理单位', field: 'id'},
  {path: '/admin/unitlist/add', group: 'unitlist', group_name: '建筑管理单位', operate: '添加了建筑管理单位', field: 'o_name,o_license,o_type,o_class,o_linkman,o_linkphone,o_address'},
  {path: '/admin/unitlist/create', group: 'unitlist', group_name: '维保单位', operate: '添加了维保单位', field: 'o_name,o_license,certificate_type,level,certificate_number,certificate_start,o_linkman,certificate_end,o_linkphone,business_start,business_end,o_address'},
  {path: '/admin/maintenancecontract/add', group: 'maintenancecontract', group_name: '维保单位', operate: '添加了服务单位', field: 'company_name,contract_sign_date,contract_start_date,contract_end_date,contract_number'},
  {path: '/admin/maintenancecontract/edit', group: 'maintenancecontract', group_name: '维保单位', operate: '修改了服务单位', field: 'company_name,contract_sign_date,contract_start_date,contract_end_date,contract_number'},
  {path: '/admin/maintenancecontract/delete', group: 'maintenancecontract', group_name: '维保单位', operate: '删除了服务单位', field: 'id'},
  {path: '/admin/firehydrant/add', group: 'firehydrant', group_name: '消火栓', operate: '添加了消火栓', field: 'num,type,address,contact,contactnum,remarks'},
  {path: '/admin/firehydrant/edit', group: 'firehydrant', group_name: '消火栓', operate: '修改了消火栓', field: 'type,address,contact,contactnum,remarks'},
  {path: '/admin/firehydrant/delete', group: 'firehydrant', group_name: '消火栓', operate: '删除了消火栓', field: 'id'},
  {path: '/admin/firehouse/add', group: 'firehouse', group_name: '消防站', operate: '添加了消防站', field: 'name,code,type,captain_name,captain_mobile,duty_telephone,address'},
  {path: '/admin/firehouse/edit', group: 'firehouse', group_name: '消防站', operate: '修改了消防站', field: 'captain_name,captain_mobile,duty_telephone,area_scope'},
  {path: '/admin/firehouse/delete', group: 'firehouse', group_name: '消防站', operate: '删除了消防站', field: 'id'},
  {path: '/admin/firehouseequipment/add', group: 'firehouseequipment', group_name: '设备信息', operate: '添加了设备信息', field: 'name,code,number,purchase_time,check_cycle'},
  {path: '/admin/firehouseequipment/edit', group: 'firehouseequipment', group_name: '设备信息', operate: '修改了设备信息', field: 'name,code,number,purchase_time,check_cycle'},
  {path: '/admin/firehouseequipment/delete', group: 'firehouseequipment', group_name: '设备信息', operate: '删除了设备信息', field: 'id'},
  {path: '/admin/firehouseduty/add', group: 'firehouseduty', group_name: '责任制度', operate: '添加了责任制度', field: 'name,type,content'},
  {path: '/admin/firehouseduty/edit', group: 'firehouseduty', group_name: '责任制度', operate: '修改了责任制度', field: 'name,type,content'},
  {path: '/admin/firehouseduty/delete', group: 'firehouseduty', group_name: '责任制度', operate: '删除了责任制度', field: 'id'},
  {path: '/admin/street/add', group: 'street', group_name: '街道信息', operate: '添加了街道信息', field: 'workaddress,streetoverview'},
  {path: '/admin/street/edit', group: 'street', group_name: '街道信息', operate: '修改了街道信息', field: 'workaddress,streetoverview'},
  {path: '/admin/street/delete', group: 'street', group_name: '街道信息', operate: '删除了街道信息', field: 'id'},
  {path: '/admin/supervision/level/add', group: 'supervision', group_name: '监管级别', operate: '添加了监管级别', field: 'regulatory_level'},
  {path: '/admin/supervision/level/edit', group: 'supervision', group_name: '监管级别', operate: '编辑了监管级别', field: 'name'},
  {path: '/admin/supervision/equipment/add', group: 'supervision', group_name: '监管级别', operate: '添加了设施设备监管配置', field: 'system_id,pro_type_id,pro_model_id,value'},
  {path: '/admin/supervision/equipment/edit', group: 'supervision', group_name: '监管级别', operate: '修改了设施设备监管配置', field: 'system_id,pro_type_id,pro_model_id,value'},
  {path: '/admin/supervision/equipment/delete', group: 'supervision', group_name: '监管级别', operate: '删除了设施设备监管配置', field: 'id'},
  {path: '/admin/supervision/firesafety/add', group: 'supervision', group_name: '监管级别', operate: '添加了消防安全管理监管配置', field: 'management_sports,management_events,remark,value'},
  {path: '/admin/supervision/firesafety/edit', group: 'supervision', group_name: '监管级别', operate: '修改了消防安全管理监管配置', field: 'management_sports,management_events,remark,value'},
  {path: '/admin/supervision/firesafety/delete', group: 'supervision', group_name: '监管级别', operate: '删除了消防安全管理监管配置', field: 'id'},
  {path: '/admin/supervision/level/delete', group: 'supervision', group_name: '监管级别', operate: '删除了监管级别', field: 'id'},
  {path: '/admin/supervision/regulations/add', group: 'supervision', group_name: '监管级别', operate: '添加了监管条例', field: 'supervise_regulations_list,code,content,remarks'},
  {path: '/admin/supervision/regulations/edit', group: 'supervision', group_name: '监管级别', operate: '编辑了监管条例', field: 'supervise_regulations_list,code,content,remarks'},
  {path: '/admin/supervision/regulations/delete', group: 'supervision', group_name: '监管级别', operate: '删除了监管条例', field: 'id'},
  {path: '/admin/supervision/strategy/add', group: 'supervision', group_name: '督察规则', operate: '添加了督察规则', field: 'name'},
  {path: '/admin/supervision/strategy/edit', group: 'supervision', group_name: '督察规则', operate: '编辑了督察规则', field: 'name,code,content'},
  {path: '/admin/supervision/strategy/delete', group: 'supervision', group_name: '督察规则', operate: '删除了督察规则', field: 'name'},
  {path: '/admin/supervision/settingconfig/edit', group: 'supervision', group_name: '超期时长', operate: '修改了超期时长', field: 'val'},
  {path: '/admin/supervision/artificial/actionImg', group: 'supervision', group_name: '人工督察单', operate: '处理了人工督查单', field: 'check_record_img,rectification_notice_img,forensic_img'},
  {path: '/admin/supervision/artificial/edit', group: 'supervision', group_name: '人工督察单', operate: '分派了人工督查单', field: 'name'},
  {path: '/admin/activity/create', group: 'activity', group_name: '重大活动预案', operate: '添加了重大活动预案', field: 'title,address,manoeuvre_time,execute_time'},
  {path: '/admin/activity/delete', group: 'activity', group_name: '重大活动预案', operate: '删除了重大活动预案', field: 'id'},
  {path: '/admin/activity/updateOrg', group: 'activity', group_name: '重大活动预案', operate: '修改了组织分工', field: 'desc,username'},
  {path: '/admin/activity/createOrg', group: 'activity', group_name: '重大活动预案', operate: '添加了资源配置', field: 'activity_id,type'},
  {path: '/admin/activity/createOrg', group: 'activity', group_name: '重大活动预案', operate: '删除了资源配置', field: 'id'},
  {path: '/admin/activity/createEvent', group: 'activity', group_name: '重大活动预案', operate: '添加了异常事件', field: 'activity_id,harm,response,type'},
  {path: '/admin/activity/delEvent', group: 'activity', group_name: '重大活动预案', operate: '删除了异常事件', field: 'id'},
  {path: '/admin/activityperson/delete', group: 'activityperson', group_name: '组员配置', operate: '删除了组织', field: 'id'},
  {path: '/admin/service/check', group: 'service', group_name: '单位认证审核', operate: '审核了单位认证', field: 'operate,id,opinion'},
  {path: '/admin/buildinggroup/check', group: 'buildinggroup', group_name: '建筑群认证审核', operate: '审核了接入建筑群', field: 'building_group_id,status,opinion'},
  {path: '/admin/announcement/add', group: 'announcement', group_name: '公示通知管理', operate: '添加了公示通知', field: 'title,sort,is_publish,desc,content'},
  {path: '/admin/announcement/edit', group: 'announcement', group_name: '公示通知管理', operate: '编辑了公示通知', field: 'title,sort,is_publish,desc,content'},
  {path: '/admin/announcement/delete', group: 'announcement', group_name: '公示通知管理', operate: '删除了公示通知', field: 'id'},
  {path: '/admin/messagereply/add', group: 'messagereply', group_name: '群防群治管理', operate: '回复了留言', field: 'content'},
  {path: '/admin/project/repair/workorder/add', group: 'workorder', group_name: '任务工单', operate: '新建了任务工单', field: 'title,content,building_group_id,buildingname,floor,name'},
  {path: '/admin/project/material/materialcategory/add', group: 'materialcategory', group_name: '物资类型', operate: '添加了物资类型', field: 'typename,content,ifdisplay'},
  {path: '/admin/project/material/materialcategory/edit', group: 'materialcategory', group_name: '物资类型', operate: '编辑了物资类型', field: 'typename,content,ifdisplay'},
  {path: '/admin/system/systemtype/add', group: 'systemtype', group_name: '系统类型', operate: '添加了系统类型', field: 'typename,remark,state'},
  {path: '/admin/system/systemtype/edit', group: 'systemtype', group_name: '系统类型', operate: '编辑了系统类型', field: 'typename,remark,state'},
  {path: '/admin/firesystemPosition/add', group: 'firesystemPosition', group_name: '设备类型', operate: '添加了设备类型', field: 'name,system_id,type,status'},
  {path: '/admin/firesystemPosition/edit', group: 'firesystemPosition', group_name: '设备类型', operate: '编辑了设备类型', field: 'name,system_id,type,status'},
  {path: '/admin/equipment/setmodel/addmodel', group: 'setmodel', group_name: '设备模型', operate: '新增了设备模型', field: 'name,system_id,type_id,ispro_alarm_rule,isline,devtype,state'},
  {path: '/admin/equipment/setmodel/editmodel', group: 'setmodel', group_name: '设备模型', operate: '编辑了设备模型', field: 'name,system_id,type_id,ispro_alarm_rule,isline,devtype,state'},
  {path: '/admin/equipment/setmodel/editattributes', group: 'setmodel', group_name: '模型属性', operate: '编辑了模型属性', field: 'name,attribute_id,sortnum,floatdata,type,display'},
  {path: '/admin/equipment/setmodel/addattributes', group: 'setmodel', group_name: '模型属性', operate: '添加了模型属性', field: 'name,attribute_id,sortnum,floatdata,type,display'},
  {path: '/admin/equipment/setmodel/editalarm', group: 'setmodel', group_name: '报警策略', operate: '更新了报警策略', field: 'alarm_state'},
  {path: '/admin/equipment/setmodel/addalarm', group: 'setmodel', group_name: '报警策略', operate: '添加了报警策略', field: 'alarm_state,groupid,typeid,continued_time,alarm_level_id,symbol_id,alarm_value,msgset,isrecall,retime,recallmsg,notice_state,alarmcontent'},
  {path: '/admin/equipment/setmodel/deletealarm', group: 'setmodel', group_name: '报警策略', operate: '删除了报警策略', field: 'id'},
  {path: '/admin/equipment/attribute/add', group: 'attribute', group_name: '属性集合', operate: '添加了属性', field: 'name,code,icon,type'},
  {path: '/admin/equipment/attribute/edit', group: 'attribute', group_name: '属性集合', operate: '编辑了属性', field: 'name,code,icon,type'}
];

const serviceOperateMap = {};
const manageOperateMap = {};
for (const v of serviceOperateList) {
  serviceOperateMap[v.path] = v;
}
for (const v of manageOperateList) {
  manageOperateMap[v.path] = v;
}

module.exports = {
  service: serviceOperateMap,
  manage: manageOperateMap
};
