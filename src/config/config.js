// default config

const config = require('../config.js');

module.exports = {
  port: 8360,
  workers: 1, // server workers num, if value is 0 then get cpus num
  nableAgent: false, // enable agent worker
  errno<PERSON><PERSON>: 'code', // errno field
  errmsgField: 'msg', // errmsg field
  defaultErrno: 1000, // default errno
  validateDefaultErrno: 1001, // validate default errno
  onUnhandledRejection: err => think.logger.error(err), // unhandledRejection handle
  onUncaughtException: err => think.logger.error(err), // uncaughtException handle
  stickyCluster: true,
  processKillTimeout: 10 * 1000, // process kill timeout, default is 10s
  jsonpCallbackField: 'callback', // jsonp callback field
  jsonContentType: 'application/json', // json content type
  server_env: config.server_env,
  sh_119_net: 'https://shapi.119.net',
  hp_119_net: 'http://hp.119.net',
  // sh_119_net: 'http://api.119.net',
  // mq队列
  lv1_alarm_cache: 'lv1_alarm_cache',
  jwt: {
    secret: 'bg_stoken_secret',
    cookie: 'jwt-token',
    expire: 86400 * 1000 * 1000 // cache使用,毫秒
  },
  // 可匿名访问的接口
  anonymous: [
    '/verification/index',
    '/sms/index',
    'crontab/cloa',
    '/websocket/websocket/open',
    '/websocket/websocket/close'
  ],
  hpnwSqlServer: {
    config: {
      host: '************',
      port: 8813,
      username: 'hpzd',
      password: 'hpzd',
      db: "HJSL",
    }
  },
};
