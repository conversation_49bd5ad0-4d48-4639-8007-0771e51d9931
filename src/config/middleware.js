const path = require('path');
const isDev = think.env === 'development';
const cors = require('@koa/cors');
const jwt = require('koa-jwt');

module.exports = [
  {
    handle: 'meta',
    options: {
      logRequest: true,
      sendResponseTime: true
    }
  },
  {
    handle: 'resource',
    enable: isDev,
    options: {
      root: path.join(think.ROOT_PATH, ''),
      publicPath: /^\/(upload|favicon\.ico)/
    }
  },
  {
    handle: 'trace',
    enable: !think.isCli,
    options: {
      debug: isDev
    }
  },
  {
    handle: 'payload',
    options: {
      multiples: true,
      keepExtensions: true,
      limit: '5mb'
    }
  },
  {
    handle: cors,
    options: {
      credentials: true
    }
  },
  {
    handle: jwt,
    // match(ctx) {
    // return !/^/index/login/.test(ctx.path);
    // },
    options: {
      cookie: think.config('jwt')['cookie'],
      secret: think.config('jwt')['secret'],
      passthrough: true
    }
  },
  {
    handle: 'router',
    options: {
      optimizeHomepageRouter: false
    }
  },
  {
    handle: 'backdated',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'admin') return true;
      return false;
    }
  },
  {
    handle: 'frontend',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'frontend') return true;
      return false;
    }
  },
  {
    handle: 'api',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'fireapi') return true;
      return false;
    }
  },
  {
    handle: 'platform',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'platform') return true;
      return false;
    }
  },
  {
    handle: 'application',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'application') return true;
      return false;
    }
  },
  {
    handle: 'bookingmanage',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/');
      if (ctrlDir[0] === 'miniprogram' && ctrlDir[1] === 'bookingmanage') return true;
      return false;
    }
  },
  {
    handle: 'miniprogram',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'miniprogram') return true;
      return false;
    }
  },
  {
    handle: 'weixin',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'weixin') return true;
      return false;
    }
  },
  {
    handle: 'ext',
    match: ctx => {
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'ext') return true;
      return false;
    }
  },
  {
    handle: 'v2',
    match: ctx => {
      // console.log('ctx.controller ->', ctx, ctx.controller, ctx.action);
      const patch = ctx.controller;
      const ctrlDir = patch.split('/')[0];
      if (ctrlDir === 'v2') return true;
      return false;
    }
  },
  'logic',
  'controller'
];
