const list = [
  {
    // cron: '*/1 * * * *',
    interval: '30m',
    handle: 'crontab/cloa',
    type: 'one',
    enable: false // 关闭当前定时器，默认true
  },
  {
    cron: '*/50 * * * * *',
    handle: 'crontab/shield',
    type: 'one',
    enable: false // 关闭当前定时器，默认true
  },
  {
    cron: '0 0 * * *',
    // interval: '30m',
    immediate: false,
    handle: 'common/crontab/buildingReport',
    enable: false
  },
  {
    interval: '60m',
    immediate: false,
    handle: 'common/crontab/serviceReport',
    enable: false
  },
  {
    interval: '24h',
    immediate: false,
    handle: 'common/crontab/updateCarInfo',
    enable: false
  },
  {
    cron: '10 0 * * *', //每天凌晨10分执行
    handle: 'common/crontab/qualityscore',
    enable: false
  },
  {
    cron: '0 10 1 * *', //每月1号同步
    immediate: false,
    handle: 'common/crontab/wxgropUpdate',
    enable: false
  },
  {
    cron: '0 0/6 * * *',  // 0点开始，每6小时执行一次
    immediate: false, // 是否立即启动
    handle: 'common/crontab/equipmentDayReport',
    enable: false
  },
  {
    cron: '5 0 1 * *', //每月1号同步 0点5分执行
    immediate: false,  // 是否立即启动
    handle: 'common/crontab/equipmentMonthReport',
    enable: false
  },
  {
    cron: '0 0/4 * * *',  // 0点开始，每4小时执行一次
    immediate: false, // 是否立即启动
    handle: 'common/crontab/deleteTempFile',
    enable: false
  },
  {
    cron: '10 3 * * *', 
    immediate: false, 
    handle: 'common/crontab/fireDataSync',
    enable: false
  }
  // {
  //   cron: '0 0 1 1 * ?',
  //   handle: 'crontab/lastMonthReport',
  //   type: 'one',
  //   enable: true // 上月月报定时器
  // },
  // {
  //   cron: '20 0 * * *', // 每天凌晨20分执行
  //   handle: 'crontab/monthReport',
  //   type: 'one',
  //   enable: true // 当月月报定时器
  // }
  // {
  //   interval: '15s',
  //   immediate: false,
  //   handle: 'common/crontab/getFrontData'
  // }
  // {
  //   cron: '0 3 * * *',
  //   immediate: true,
  //   handle: 'crontab/requestLog'
  // }
];

module.exports = process.env.DISABLE_CRONTAB ? [] : list;