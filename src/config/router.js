module.exports = [
  ['/wx', 'common/message/receive', 'get,post'],

  // 自定义分页请求路由

  ['/v2/mobile/community', 'v2/community/mobileList', 'get'],
  ['/v2/community', 'v2/community/list', 'get'],
  ['/v2/community', 'v2/community/post', 'post'],
  ['/v2/community/:id', 'v2/community/get', 'get'],
  ['/v2/community/:id', 'v2/community/put', 'put'],
  ['/v2/community/:id', 'v2/community/delete', 'delete'],
  ['/v2/mobile/community/:id', 'v2/community/getMobile', 'get'],
  ['/v2/mobile/communityRisk/:id', 'v2/community/getMobileRisk', 'get'],

  // 组织
  ['/v2/organization', 'v2/organization/list', 'get'],
  ['/v2/organization', 'v2/organization/post', 'post'],
  ['/v2/organization/:id', 'v2/organization/get', 'get'],
  ['/v2/organization/:id', 'v2/organization/put', 'put'],
  ['/v2/organization/:id', 'v2/organization/delete', 'delete'],

  // 建筑
  ['/v2/building', 'v2/building/list', 'get'],
  ['/v2/building', 'v2/building/post', 'post'],
  ['/v2/building/:id', 'v2/building/get', 'get'],
  ['/v2/building/:id', 'v2/building/put', 'put'],
  ['/v2/building/:id', 'v2/building/delete', 'delete'],
  ['/v2/buildingFloor', 'v2/building/listFloor', 'get'],

  // 重点部位
  ['/v2/key_location', 'v2/key_location/list', 'get'],
  ['/v2/key_location', 'v2/key_location/post', 'post'],
  ['/v2/key_location/:id', 'v2/key_location/get', 'get'],
  ['/v2/key_location/:id', 'v2/key_location/put', 'put'],
  ['/v2/key_location/:id', 'v2/key_location/delete', 'delete'],

  // 消防组织
  ['/v2/fire_organization', 'v2/fire_organization/list', 'get'],
  ['/v2/fire_organization', 'v2/fire_organization/post', 'post'],
  ['/v2/fire_organization/:id', 'v2/fire_organization/get', 'get'],
  ['/v2/fire_organization/:id', 'v2/fire_organization/put', 'put'],
  ['/v2/fire_organization/:id', 'v2/fire_organization/delete', 'delete'],

  // 人员
  ['/v2/resident/import-excel', 'v2/resident/importExcel', 'post'],
  ['/v2/resident', 'v2/resident/list', 'get'],
  ['/v2/resident', 'v2/resident/post', 'post'],
  ['/v2/resident/:id', 'v2/resident/get', 'get'],
  ['/v2/resident/:id', 'v2/resident/put', 'put'],
  ['/v2/resident/:id', 'v2/resident/delete', 'delete'],

  ['/v2/drawing-catalog', 'v2/drawing_catalog/list', 'get'],

  // 警情
  ['/v2/real_time_police', 'v2/real_time_police/get', 'get'],

  // 基层执勤动态 - 消防站执勤的车辆所执勤的记录
  ['/v2/fire_station_vehicle_duty_record', 'v2/fire_station_vehicle_duty_record/list', 'get'],
  ['/v2/fire_station_vehicle_duty_record/:id', 'v2/fire_station_vehicle_duty_record/get', 'get'],
  ['/v2/fire_station_vehicle_duty_record', 'v2/fire_station_vehicle_duty_record/post', 'post'],
  ['/v2/fire_station_vehicle_duty_record/:id', 'v2/fire_station_vehicle_duty_record/put', 'put'],
  ['/v2/fire_station_vehicle_duty_record/:id', 'v2/fire_station_vehicle_duty_record/delete', 'delete'],
  ['/v2/get_fire_station_vehicle_duty_record_group', 'v2/fire_station_vehicle_duty_record/getGroup', 'get'],
];
