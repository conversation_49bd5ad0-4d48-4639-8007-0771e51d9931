const config = require('../../config.js');
const amqp = require('amqplib');
const RabbitSrrvice = require(think.ROOT_PATH + '/src/service/websocket');
const srrvice = new RabbitSrrvice();
class rabbitMq {
  constructor() {
    this.cfg = think.config('mq');
    this.cfgs = think.config('mqs');
    this.ch = '';
    this.chs = '';
    this.init();
  }
  work() {
    // 启动时写入队列的工作 index.js页面 zfq
  }

  async init() {
    const self = this;
    const conn = await amqp.connect(self.cfg.conn);

    let conns = null;
    if (config.server_env === 'huangpu') {
      conns = await amqp.connect(self.cfg.conn);
    } else {
      conns = await amqp.connect(self.cfgs.conn);
    }
    conn.on('error', async function (err) {
      const excludeErr = [503, 406]; // 这里声明的错误不重连
      if (excludeErr.indexOf(err.code) > -1) return;
      think.logger.error('[mq] connection error ', err);
      think.ch = '';
      self.ch = '';
      await sleep(1000); // 休眠1000毫秒
      this.init();
    });

    conns.on('error', async function (err) {
      const excludeErr = [503, 406]; // 这里声明的错误不重连
      if (excludeErr.indexOf(err.code) > -1) return;
      think.logger.error('[mq] connection error lv2 ', err);
      think.chs = '';
      self.chs = '';
      await sleep(1000); // 休眠1000毫秒
      this.init();
    });

    const ch = await conn.createChannel();
    const chs = await conns.createChannel();
    think.ch = ch;
    think.chs = chs;
    self.ch = ch;
    self.chs = chs;
    self.ch.prefetch(this.cfg.prefetchCount); // 当有x个任务未完成时，不再接受新任务
    self.chs.prefetch(this.cfg.prefetchCount); // 当有x个任务未完成时，不再接受新任务

    const queue = self.ch.assertQueue('', { exclusive: true });
    await self.ch.bindQueue(queue.queue, 'lv1_basic_event', 'lv1_basic_event.real_alarm');
    self.ch.consume(queue.queue, async (msg) => {
      self.ch.ack(msg);
      try {
        if (msg.content.toString()) {
          const data = JSON.parse(msg.content.toString());
          await srrvice.realAlarmAction(data);
        }
      } catch (e) {
        think.logger.error(e);
      }
    }).then();

    return true;
  }

  subscribe(queueName, receiveCallBack) {
    if (think.isEmpty(this.ch)) return;
    const self = this;
    self.ch.assertQueue(queueName).then((ok) => {
      self.ch.consume(queueName, (msg) => {
        if (msg !== null) {
          self.ch.ack(msg);
          receiveCallBack(msg.content.toString());
        }
      });
    });
  }
  async publish(queueName, msg, startnum = 0) {
    if (think.isEmpty(this.ch)) return;
    const self = this;
    // 如果不传queueName,只传obj就会使用默认的queunName:alarm_cache
    if (typeof queueName === 'object') {
      msg = queueName;
      queueName = 'alarm_cache';
    }
    try {
      self.ch.assertQueue(queueName).then((ok) => {
        if (typeof msg === 'object') {
          msg = JSON.stringify(msg);
        }
        self.ch.sendToQueue(queueName, Buffer.from(msg));
      });
    } catch (error) {
      if (startnum === 0) {
        const state = await this.init();
        if (state) startnum++; this.publish(queueName, msg, startnum);
      }
    }
  }
  async publishexcel(queueName, msg, startnum = 0) {
    if (think.isEmpty(this.ch)) return;
    const self = this;
    // 如果不传queueName,只传obj就会使用默认的queunName:alarm_cache
    if (typeof queueName === 'object') {
      msg = queueName;
      queueName = 'alarm_cache';
    }

    try {
      self.ch.assertExchange(queueName, 'fanout', { durable: true }).then((ok) => {
        if (typeof msg === 'object') {
          msg = JSON.stringify(msg);
        }
        self.ch.publish(queueName, '', Buffer.from(msg));
      });
    } catch (error) {
      if (startnum === 0) {
        const state = await this.init();
        if (state) startnum++; this.publishexcel(queueName, msg, startnum);
      }
    }
  }

  // 疑似火警通知二级
  async sendTopublish(ex, routeKey, msg, option, startnum = 0) {
    console.log('msg---', msg);
    if (think.isEmpty(this.chs)) return;
    const self = this;

    if (typeof msg === 'object') {
      msg = JSON.stringify(msg);
    }

    try {
      self.chs.publish(ex, routeKey, Buffer.from(msg), option);
    } catch (error) {
      if (startnum === 0) {
        const state = await self.init();
        if (state) startnum++; self.sendTopublish(ex, routeKey, msg, option = {}, startnum);
      }
    }
  }

  async sendTopublishs(ex, routeKey, msg, option, startnum = 0) {
    if (think.isEmpty(this.ch)) return;
    const self = this;
    if (typeof msg === 'object') {
      msg = JSON.stringify(msg);
    }
    try {
      self.ch.publish(ex, routeKey, Buffer.from(msg), option);
    } catch (error) {
      if (startnum === 0) {
        const state = await self.init();
        if (state) startnum++; self.sendTopublishs(ex, routeKey, msg, option = {}, startnum);
      }
    }
  }
}
module.exports = rabbitMq;

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
