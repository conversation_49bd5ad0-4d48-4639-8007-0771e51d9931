const view = require('think-view');
const model = require('think-model');
const cache = require('think-cache');
const mongo = require('think-mongo');
const session = require('think-session');
const fetch = require('think-fetch');
const queue = require('./rabbitmq/index');
const websocket = require('think-websocket');

module.exports = [
  view, // make application support view
  model(think.app),
  mongo(think.app),
  websocket(think.app),
  cache,
  session,
  fetch,
  queue
];
