const config = require('../config/config');
// 时间戳
global.getnow = function () {
  let time = Date.now().toString();
  time = time.substring(0, 10);
  return time;
};
// 生成6位的随机数
global.MathRand = function () {
  var Num = '';
  for (var i = 0; i < 6; i++) {
    Num += Math.floor(Math.random() * 10);
  }
  return Num;
};
/**
 * ip转数字
 * @param ip
 * @returns {number}
 * @private
 */
global._ip2int = function (ip) {
  let num = 0;
  ip = ip.split('.');
  num = Number(ip[0]) * 256 * 256 * 256 + Number(ip[1]) * 256 * 256 + Number(ip[2]) * 256 + Number(ip[3]);
  num = num >>> 0;
  return num;
};

/**
 * 数字转ip
 * @param num
 * @returns {string|*}
 * @private
 */
global._int2iP = function (num) {
  const tt = [];
  tt[0] = (num >>> 24) >>> 0;
  tt[1] = ((num << 8) >>> 24) >>> 0;
  tt[2] = (num << 16) >>> 24;
  tt[3] = (num << 24) >>> 24;
  const str = String(tt[0]) + '.' + String(tt[1]) + '.' + String(tt[2]) + '.' + String(tt[3]);
  return str;
};

/**
 * 数组去重
 * @param arr
 * @returns {Array}
 */
global.unique = function (arr) {
  return Array.from(new Set(arr));
};
/**
 * 实现无限层级树形数据结构
 * @param arr
 * @returns {Array}
 */
global.toTree = function (data) {
  // 删除 所有 children,以防止多次调用
  data.forEach(function (item) {
    delete item.children;
  });
  // 将数据存储为 以 id 为 KEY 的 map 索引数据列
  const map = {};
  data.forEach(function (item) {
    map[item.id] = item;
  });
  const val = [];
  data.forEach(function (item) {
    // 以当前遍历项，的pid,去map对象中找到索引的id
    var parent = map[item.pid];
    // 如果找到索引，那么说明此项不在顶级当中,那么需要把此项添加到，他对应的父级中
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else if (item.pid === 0) {
      // 如果没有在map中找到对应的索引ID,那么直接把 当前的item添加到 val结果集中，作为顶级
      val.push(item);
    }
  });
  return val;
};

global.otherToTree = function (data, type) {
  // 删除 所有 children,以防止多次调用
  data.forEach(function (item) {
    delete item.children;
  });
  // 将数据存储为 以 id 为 KEY 的 map 索引数据列
  const map = {};
  data.forEach(function (item) {
    map[item.id] = item;
  });
  const val = [];
  data.forEach(function (item) {
    // 以当前遍历项，的pid,去map对象中找到索引的id
    var parent = map[item[type]];
    // 如果找到索引，那么说明此项不在顶级当中,那么需要把此项添加到，他对应的父级中
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      // 如果没有在map中找到对应的索引ID,那么直接把 当前的item添加到 val结果集中，作为顶级
      val.push(item);
    }
  });
  return val;
};
/**
 * SQL防注入数组去重
 * @param str
 * 去除危险字符
 * @returns str
 */
global.sqlAntiHack = function (str) {
  if (str && think.isString(str)) {
    str = str.replace(/;/g, '；');
    str = str.replace(/'/g, '’');
    str = str.replace(/\*/g, '');
    str = str.replace(/\(/g, '（');
    str = str.replace(/\)/g, '）');
    str = str.replace(/\[/g, '【');
    str = str.replace(/\]/g, '】');
    str = str.replace(/\{/g, '｛');
    str = str.replace(/\}/g, '｝');
    str = str.replace(/-/g, '');
  }
  return str;
}
global.scheduleJob = {};

global.scheduleTask = {};


global.streetbyenv = function () {

  let streetCode = '310106000000';
  if (config.server_env === 'jingan') {
    streetCode = '310106000000';
  } else if (config.server_env === 'huangpu'|| config.server_env === 'jiaoyuju' || config.server_env === 'tengyu') {
    streetCode = '310101000000';
  } else if (config.server_env === 'songjiang') {
    streetCode = '310117000000';
  } else if (config.server_env === 'jiading') {
    streetCode = '310114000000';
  } else {
    streetCode = '310106000000';
  }
  return streetCode

};
