ALTER TABLE `pt_supervise_sheet_artificial`
MODIFY COLUMN `company_id` int(11) NOT NULL DEFAULT 0 COMMENT '单位id' AFTER `id`,
MODIFY COLUMN `alarm_strategy_id` int(11) DEFAULT 0 COMMENT '报警策略id' AFTER `company_id`,
MODIFY COLUMN `recordcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '电子督察单检查记录编号' AFTER `alarm_strategy_id`,
MODIFY COLUMN `overview` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '电子督察单概述' AFTER `recordcode`,
MODIFY COLUMN `status` tinyint(4) DEFAULT 0 COMMENT '任务状态：0待处理，1已完成' AFTER `overview`,
MODIFY COLUMN `dealing_id` int(11) DEFAULT 0 COMMENT '处理人id' AFTER `status`,
MODIFY COLUMN `dealing_people` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '处理人' AFTER `dealing_id`,
MODIFY COLUMN `assist_id` int(11) DEFAULT 0 COMMENT '协助人id' AFTER `dealing_people`,
MODIFY COLUMN `assist_people` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '协助人' AFTER `assist_id`,
MODIFY COLUMN `check_record_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '签字检查记录单' AFTER `assist_people`,
MODIFY COLUMN `rectification_notice_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '责令整改通知书' AFTER `check_record_img`,
MODIFY COLUMN `forensic_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '取证图片' AFTER `rectification_notice_img`,
MODIFY COLUMN `created_at` datetime(0) DEFAULT NULL COMMENT '创建时间' AFTER `forensic_img`,
MODIFY COLUMN `updated_at` datetime(0) DEFAULT NULL COMMENT '修改时间' AFTER `created_at`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`id`, `company_id`) USING BTREE;