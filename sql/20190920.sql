ALTER TABLE `pt_alarm_fault`
ADD COLUMN `created_at` datetime(0) DEFAULT NULL COMMENT '创建时间' AFTER `isalone`;

ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `created_at` datetime(0) DEFAULT NULL COMMENT '创建时间' AFTER `isalone`;

ALTER TABLE `pt_tendency`
MODIFY COLUMN `fire_num` int(11) DEFAULT NULL COMMENT '火警数' AFTER `pid`,
MODIFY COLUMN `fire_total` int(11) DEFAULT NULL COMMENT '火警总数' AFTER `fire_num`,
MODIFY COLUMN `fire_handled_num` int(11) DEFAULT NULL COMMENT '火警已处理' AFTER `fire_total`,
MODIFY COLUMN `broken_num` int(11) DEFAULT NULL COMMENT '故障数' AFTER `fire_reset_num`,
MODIFY COLUMN `broken_total` int(11) DEFAULT NULL COMMENT '故障总数' AFTER `broken_num`,
MODIFY COLUMN `broken_handled_num` int(11) DEFAULT NULL COMMENT '故障已处理' AFTER `broken_total`,
MODIFY COLUMN `danger_num` int(11) DEFAULT NULL COMMENT '隐患数' AFTER `broken_handled_num`,
MODIFY COLUMN `danger_total` int(11) DEFAULT NULL COMMENT '隐患总数' AFTER `danger_num`,
MODIFY COLUMN `danger_handled_num` int(11) DEFAULT NULL COMMENT '隐患已处理' AFTER `danger_total`,
MODIFY COLUMN `shield_num` int(11) DEFAULT NULL COMMENT '屏蔽数' AFTER `danger_handled_num`,
MODIFY COLUMN `shield_total` int(11) DEFAULT NULL COMMENT '屏蔽总数' AFTER `shield_num`,
MODIFY COLUMN `shield_handled_num` int(11) DEFAULT NULL COMMENT '屏蔽已处理' AFTER `shield_total`;