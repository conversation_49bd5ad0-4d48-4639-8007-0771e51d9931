-- btndown：表格内操作按钮
-- btnup：页面头部操作按钮
-- api:普通的调用接口

-- 首页
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页街道接口', 'api', '/admin/street/showLng');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页今日情况接口', 'api', '/admin/statistics/today');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页报警接口', 'api', '/admin/alarmTrend/alarmRatio');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页接口', 'api', '/admin/alarmTrend/tendency');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页接口', 'api', '/admin/statistics/todoEvent');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页接口', 'api', '/admin/street/showLng');
-- 坐标
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('获取地图坐标', 'api', '/admin/map/getMapPoint');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('获取单位坐标', 'api', '/admin/map/companyLnglat');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('获取建筑坐标', 'api', '/admin/map/getBuildingPoint');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('获取消火栓坐标', 'api', '/admin/map/firehydrantLnglat');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('获取微型消防站坐标', 'api', '/admin/map/firehouseLnglat');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('获取区坐标', 'api', '/admin/map/districtTotal');
-- 地图
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('地图接口', 'api', '/admin/map/statistics');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('数据字典接口', 'api', '/common/wordbook/wordbooksearch');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('区code', 'api', '/admin/user/areacode');
-- 建筑群信息
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑列表', 'api', '/admin/buildinggroup/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('指挥调度', 'api', '/admin/supervision/artificial/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('系统类型', 'api', '/admin/system/systemtype/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('判断建筑群是否存在', 'api', '/admin/buildinggroup/exist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('添加多个地址', 'api', '/admin/buildinggroup/createBuildingAddress');

-- 统计报表
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑统计-物联网接入建筑群统计', 'api', '/admin/statistics/buildingGroupJoinStatus');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑统计-报警建筑群统计', 'api', '/admin/statistics/buildingGroupAlarm');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑统计-建筑管理单位履责统计', 'api', '/admin/statistics/manageAccountStatics');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑统计-物联消防系统接入统计', 'api', '/admin/statistics/buildingGroupFireSystem');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('物联网服务商-物联网服务商接入建筑群统计', 'api', '/admin/statistics/serviceBuildingGroup');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('历史报警趋势-火灾报警主机报警趋势', 'api', '/admin/statistics/fireAlarm');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('历史报警趋势-各消防系统报警趋势（除火灾报警主机）', 'api', '/admin/statistics/otherAlarm');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('督查单接收失败日志-电子督查单列表', 'api', '/admin/statistics/supervisionfail');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('督查单接收失败日志-人工督查单列表', 'api', '/admin/statistics/humansupervisionfail');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设施设备接入-列表', 'api', '/admin/statistics/equipmentAccess');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警数据和督查单数据-列表', 'api', '/admin/statistics/alarmData');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重点单位总览-列表', 'api', '/admin/statistics/tongjibystreet');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重点单位列表-列表', 'api', '/admin/statistics/tongjibystreet');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重点单位列表-街道下拉', 'api', '/admin/statistics/getStreet');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重点单位列表-重点单位下拉', 'api', '/admin/statistics/importCompany');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重点单位列表-重点单位列表', 'api', '/admin/statistics/allImportCompany');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('已企业认证单位列表-列表', 'api', '/admin/statistics/enterpriseCertification');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('已建筑备案单位列表-列表', 'api', '/admin/statistics/buildingRecord');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件类型统计-报警事件', 'api', '/admin/statistics/alarmEventStatistice');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件类型统计-电子督查单和人工督查单', 'api', '/admin/statistics/supervisionStatistice');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件类型统计-报警事件类型分类统计', 'api', '/admin/statistics/alarmEventTypeStatistice');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('督查结果统计-列表', 'api', '/admin/statistics/supervisionResult');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础事件统计-列表', 'api', '/admin/statistics/basicEventTypeStatistice');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('按问题分类统计-列表', 'api', '/admin/statistics/alarmEventTypeStatistice');
-- 监管中心
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件类型', 'api', '/admin/basicEvent/eventType');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警事件-列表', 'api', '/admin/basicEvent/basicEventList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('离线设备-按建筑群统计列表', 'api', '/admin/statistics/bgofflinelist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('离线设备-按服务商统计列表', 'api', '/admin/statistics/serviceofflinelist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('电子督查单-事件下拉', 'api', '/admin/eventTpl/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('电子督查单-类型统计', 'api', '/admin/supervision/inspector/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('电子督查单和现场检查单-列表', 'api', '/admin/supervision/inspector/alarmmsg');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人工督查单-精准火警列表', 'api', '/admin/basicEvent/accurateFire');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('仪表盘-头部统计', 'api', '/admin/buildinggroup/dashboardStatistics');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('仪表盘-当前存在的问题', 'api', '/admin/buildinggroup/buildingProblem');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('仪表盘-等级统计', 'api', '/admin/buildinggroup/riskLevelByPid');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('仪表盘', 'api', '/admin/buildinggroup/problemByPid');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('仪表盘', 'api', '/admin/buildinggroup/eventTypeByPid');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-建筑信息', 'api', '/admin/buildinggroup/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-建筑群地址', 'api', '/admin/buildinggroup/findByAddressByPid');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息', 'api', '/common/area/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑信息-建筑树结构', 'api', '/admin/buildinggroup/getTree');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑信息-建筑信息', 'api', '/admin/building/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑信息-建筑群查重', 'api', '/admin/building/findBuild');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('入驻单位-列表', 'api', '/admin/buildinggroup/getRentCompanyList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('入驻单位-编辑入驻范围', 'api', '/admin/buildinggroup/updateCompanyScope');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('入驻单位-新增', 'api', '/admin/buildinggroup/updateCompanyScope');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('入驻单位-编辑列表', 'api', '/admin/unitlist/edit');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人员信息-列表', 'api', '/admin/buildinggroup/getEmployeeList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人员信息-管理单位列表', 'api', '/admin/buildinggroup/getManageList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('消防重点部位-列表', 'api', '/admin/buildinggroup/getKeyPointList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('消防安全制度-列表', 'api', '/admin/buildinggroup/getInstitutionList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('消防安全培训记录-列表', 'api', '/admin/buildinggroup/getTrainingList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('维保记录-列表', 'api', '/admin/buildinggroup/getMaintenanceRecordList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('维修记录-列表', 'api', '/admin/buildinggroup/getMaintenanceRecordList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('维保单位-列表', 'api', '/admin/buildinggroup/maintenanceContractList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑管理单位-列表', 'api', '/admin/buildinggroup/getBuildmanageunitList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('物联服务商-列表', 'api', '/admin/buildinggroup/getServiceproviderList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('房间信息-列表', 'api', '/admin/buildinggroup/contralRoom');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('房间信息-设备列表', 'api', '/admin/buildinggroup/roomDevice');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('防火分区-设备列表', 'api', '/admin/buildinggroup/firePrevention');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时监测-卡片列表', 'api', '/admin/project/detection/system/systemcard');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时监测-服务商', 'api', '/admin/buildinggroup/buildingGroupSevice');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时监测-模型列表', 'api', '/admin/project/detection/system/modelist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时监测-报警主机卡片列表', 'api', '/admin/project/detection/system/alarmhost');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时监测-报警主机点位列表', 'api', '/admin/project/firesystem/alarmlist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时监测-卡片列表（报警主机以外）', 'api', '/admin/project/detection/system/monitorcard');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设备列表-列表', 'api', '/admin/project/projectdev/allprodev');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设备列表-获取项目下楼层', 'api', '/admin/project/floor/projectallfloor');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设备列表-获取项目下建筑', 'api', '/admin/building/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警规则-模型规则列表', 'api', '/admin/project/projectdevalert/modelist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警规则-项目规则列表', 'api', '/admin/project/projectdevalert/projectlist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警规则-待配置规则列表', 'api', '/admin/project/projectdevalert/pendinglist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警规则-多设备组合报警', 'api', '/admin/equipment/setmodel/groupList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警规则-多设备组合报警组规则列表', 'api', '/common/attribute/findattvalue');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警规则-多设备组合报警组-管理组规则-列表', 'api', '/admin/project/projectdevalert/findoneproject');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('报警规则-多设备组合报警组-组合规则-列表', 'api', '/admin/equipment/setmodel/groupElList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设备登记-列表', 'api', '/admin/deviceEnter/getDeviceEnterRecords');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('测试计划-列表', 'api', '/admin/testplan/listData');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('质量报告-列表', 'api', '/admin/buildinggroup/buildReport');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('通知成员-列表', 'api', '/admin/project/member/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('物质列表-列表', 'api', '/admin/project/material/material/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-列表', 'api', '/admin/service/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-基本信息-详情', 'api', '/admin/service/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-接入建筑群-列表', 'api', '/admin/service/getBuildingGroupList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-接入建筑群-查看接入范围', 'api', '/admin/buildinggroup/getRentCompanyLis');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-审核信息-详情', 'api', '/admin/service/getCheckLog');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-数据对接报告-列表', 'api', '/admin/service/getProcesslist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-电子督查单日-列表', 'api', '/admin/service/taskLog');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-人工督查单日-列表', 'api', '/admin/service/superviseLog');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-接入系统管理-列表', 'api', '/admin/system/systemtype/getAllSystem');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-接入授权报告-列表', 'api', '/admin/system/systemtype/accessreportlist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-操作日志-列表', 'api', '/admin/system/systemtype/operationLog');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('服务商-开发信息-详情', 'api', '/admin/user/userinfo');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑管理单位-列表', 'api', '/admin/unitlist/getManageList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑管理单位-管理建筑-列表', 'api', '/admin/unitlist/getBuildingList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑管理单位-基本信息-详情', 'api', '/admin/unitlist/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('建筑管理单位-人员信息-列表', 'api', '/admin/unitlist/getCompanyEmployee');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('维保单位-服务单位-列表', 'api', '/admin/maintenance/serviceList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('维保单位-人员信息-列表', 'api', '/admin/maintenance/getEmployeeList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('维保单位-审核信息-列表', 'api', '/admin/maintenance/getCheckLog');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('消火栓信息-列表', 'api', '/admin/firehydrant/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('消火栓信息-基础信息-详情', 'api', '/admin/firehydrant/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('微型消防站-列表', 'api', '/admin/firehouse/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('微型消防站-基础信息-详情', 'api', '/admin/firehouse/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('微型消防站-设备信息-列表', 'api', '/admin/firehouseequipment/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('微型消防站-责任制度-列表', 'api', '/admin/firehouseduty/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('街道信息-列表', 'api', '/admin/street/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('街道信息-基础信息-详情', 'api', '/admin/street/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('街道信息-统计信息-详情', 'api', '/admin/map/statistics');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('街道信息-社区微型消防站-列表', 'api', '/admin/firehouse/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('入驻单位-列表', 'api', '/admin/unitlist/getProductList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('单位信息-重点单位列表-列表', 'api', '/admin/companyTagCategoryRelation/getCompanyList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('获取监督', 'api', '/admin/buildinggroup/getSupervise');
-- 获取菜单
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('头部菜单', 'api', '/admin/menu/top');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('左侧菜单', 'api', '/admin/menu/left');
-- 基础信息统计
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息统计数量接口', 'api', '/admin/buildinggroup/count');
-- 基类接口
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基类接口', 'api', '/admin/wordbook/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑信息-添加建筑群', 'btnup', 399, '/admin/building/create', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑群信息-添加建筑群', 'btnup', 387,'/admin/buildinggroup/create', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑群信息-编辑建筑群', 'btnup', 387,'/admin/buildinggroup/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑群信息-删除建筑群', 'btnup', 387,'/admin/buildinggroup/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑信息-添加楼层', 'btnup', 399, '/admin/floor/addFloor', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑信息-删除建筑', 'btnup', 399, '/admin/building/update', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑信息-编辑建筑', 'btnup', 399, '/admin/building/update', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('人员信息-添加', 'btnup', 404, '/admin/employee/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防重点部位-添加', 'btnup', 405, '/admin/buildingkeypart/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防安全制度-添加', 'btnup', 406, '/admin/securityinstitution/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防安全培训记录-添加', 'btnup', 408, '/admin/securitytraining/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-添加', 'btnup', 593, '/admin/buildinggroup/addManageBuilding', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-多设备组合报警组规则列表-添加', 'btnup', 496, '/admin/equipment/setmodel/addGroupEl', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-待配置规则列表-调整-添加', 'btnup', 496, '/admin/project/projectdevalert/addmodelalarmNew', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备登记-登记', 'btnup', 647, '/admin/deviceEnter/saveDeviceEnterRecords', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物质列表-新增', 'btnup', 540, '/admin/project/material/material/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('服务商-数据对接报告-接口允许接入', 'btnup', 563, '/admin/service/passDocking', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('服务商-根据requestid查询日志-查询', 'btnup', 586, '/admin/buildinggroup/searchRecordByRequestIdAndCompany', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('服务商-接入授权报告-新增', 'btnup', 589, '/admin/system/systemtype/addServiceModel', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-新增', 'btnup', 374, '/admin/unitlist/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-人员信息-新增', 'btnup', 423, '/admin/employee/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('维保单位-新增', 'btnup', 227, '/admin/unitlist/create', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('维保单位-服务单位-新增', 'btnup', 424, '/admin/maintenancecontract/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消火栓信息-新增', 'btnup', 230, '/admin/firehydrant/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-新增', 'btnup', 386, '/admin/firehouse/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-设备信息-新增', 'btnup', 441, '/admin/firehouseequipment/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-责任制度-新增', 'btnup', 442, '/admin/firehouseduty/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('街道信息-新增', 'btnup', 308, '/admin/street/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('街道信息-联系人-新增', 'btnup', 435, '/admin/employee/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('单位信息-重点单位列表-新增', 'btnup', 678, '/admin/company/addImportComapny', 1);




INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('人员信息-编辑', 'btndown', 404, '/admin/employee/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('人员信息-删除', 'btndown', 404, '/admin/employee/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防重点部位-编辑', 'btndown', 405, '/admin/buildingkeypart/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防重点部位-删除', 'btndown', 405, '/admin/buildingkeypart/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防安全制度-编辑', 'btndown', 406, '/admin/securityinstitution/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防安全制度-删除', 'btndown', 406, '/admin/securityinstitution/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防安全培训记录-编辑', 'btndown', 408, '/admin/securitytraining/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消防安全培训记录-删除', 'btndown', 408, '/admin/securitytraining/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('维保单位-解除', 'btndown', 227, '/admin/maintenancecontract/relieve', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-编辑', 'btndown', 374, '/admin/buildinggroup/updateCompanyScope', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-解除', 'btndown', 374, '/admin/buildinggroup/relieve', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物联服务商-解除', 'btndown', 374, '/admin/buildinggroup/relieve', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-多设备组合报警-组规则列表-编辑', 'btndown', 496, '/admin/equipment/setmodel/editGroupEl', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-多设备组合报警-组规则列表-删除', 'btndown', 496, '/admin/equipment/setmodel/delGroupEl', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-多设备组合报警组规则-编辑', 'btndown', 496, '/admin/equipment/setmodel/editGroup', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-多设备组合报警组规则-编辑', 'btndown', 496, '/admin/project/projectdevalert/updatemodelalarmNew', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-待配置规则列表-调整-编辑', 'btndown', 496, '/admin/project/projectdevalert/updatemodelalarmNew', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('报警规则-待配置规则列表-调整-删除', 'btndown', 496, '/admin/project/projectdevalert/deletemodelalarmNew', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('测试计划-新增', 'btndown', 648, '/admin/testplan/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('测试计划-编辑', 'btndown', 648, '/admin/testplan/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('测试计划-删除', 'btndown', 648, '/admin/testplan/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物质列表-编辑', 'btndown', 540, '/admin/project/material/material/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('基础信息-服务商-编辑', 'btndown', 224, '/admin/unitlist/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('基础信息-服务商-启用账号', 'btndown', 224, '/admin/service/enableAccount', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('基础信息-服务商-允许接入', 'btndown', 224, '/admin/service/accessAccount', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('服务商-接入系统管理-允许接入', 'btndown', 588, '/admin/system/systemtype/allowAccess', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('服务商-接入系统管理-停止接入', 'btndown', 588, '/admin/system/systemtype/stopAccess', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-备注', 'btndown', 374, '/admin/unitlist/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-删除', 'btndown', 374, '/admin/unitlist/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-管理建筑-删除','btndown', 420,  '/admin/buildinggroup/delCompanyScope', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-人员信息-编辑','btndown', 404,  '/admin/employee/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('建筑管理单位-人员信息-删除','btndown', 404,  '/admin/employee/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('维保单位-删除', 'btndown', 227, '/admin/unitlist/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('维保单位-服务单位-编辑', 'btndown', 424, '/admin/maintenancecontract/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('维保单位-服务单位-删除', 'btndown', 424, '/admin/maintenancecontract/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消火栓信息-编辑', 'btndown', 230, '/admin/firehydrant/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('消火栓信息-删除', 'btndown', 230, '/admin/firehydrant/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-编辑', 'btndown', 386, '/admin/firehouse/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-删除', 'btndown', 386, '/admin/firehouse/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-设备信息-编辑', 'btndown', 441, '/admin/firehouseequipment/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-设备信息-删除', 'btndown', 441, '/admin/firehouseequipment/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-责任制度-编辑', 'btndown', 442, '/admin/firehouseduty/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('微型消防站-责任制度-删除', 'btndown', 442, '/admin/firehouseduty/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('街道信息-编辑', 'btndown', 308, '/admin/street/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('街道信息-删除', 'btndown', 308, '/admin/street/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('街道信息-联系人-编辑', 'btndown', 435, '/admin/employee/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('街道信息-联系人-删除', 'btndown', 435, '/admin/employee/delete', 1);








-- 会议系统管理
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('会议管理-会议列表', 'api', '/ext/emanage/meeting/meeting/meetingListPc');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('会议管理-获取公司部门信息', 'api', '/ext/emanage/utils/getDepartMent');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('会议管理-查看签到记录', 'btndown', 663, '/ext/emanage/meeting/meeting/meetingSignInList', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('历史会议报表-历史会议列表', 'api', '/ext/emanage/meeting/meeting/meetingHistoryTotal');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人员参会统计表-人员参会列表查询', 'api', '/ext/emanage/meeting/meeting/mbs');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('人员参会统计表-查询人员参会详情', 'btndown', 674, '/ext/emanage/meeting/meeting/mbsinfo', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('部门会议统计-列表', 'api', '/ext/emanage/meeting/udepartment/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('会议室管理-列表', 'api', '/ext/emanage/meeting/room/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('会议室管理-新增', 'btnup', 667, '/ext/emanage/meeting/meeting/room/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('会议室管理-编辑', 'btndown', 667, '/ext/emanage/meeting/room/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('会议室管理-删除', 'btndown', 667, '/ext/emanage/meeting/room/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('审批人管理-列表', 'api', '/ext/emanage/meeting/approval/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('审批人管理-获取公司部门信息', 'api', '/ext/emanage/utils/getDepartMent');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('审批人管理-新增', 'btnup', 682, '/ext/emanage/meeting/approval/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('审批人管理-编辑', 'btndown', 682, '/ext/emanage/meeting/approval/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('审批人管理-删除', 'btndown', 682, '/ext/emanage/meeting/approval/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('承办部门-列表', 'api', '/ext/emanage/meeting/udepartment/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('承办部门-新增', 'btnup', 683, '/ext/emanage/meeting/udepartment/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('承办部门-编辑', 'btndown', 683, '/ext/emanage/meeting/udepartment/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('承办部门-删除', 'btndown', 683, '/ext/emanage/meeting/udepartment/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('审批开关配置-获取开关状态', 'api', '/ext/emanage/meeting/sysconfigHandle/sysSearch');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('审批开关配置-编辑开关状态', 'btnup', 684, '/ext/emanage/meeting/sysconfigHandle/sysEdit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('不打卡白名单-列表', 'api', '/ext/emanage/meeting/whiteListManage/whiteList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('不打卡白名单-新增', 'btnup', 687, '/ext/emanage/meeting/whiteListManage/whiteListAdd', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('不打卡白名单-删除', 'btndown', 687, '/ext/emanage/meeting/whiteListManage/whiteListDelete', 1);

-- 预约管理
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('预约管理-列表', 'api', '/admin/miniprogram/bookingmanage/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('预约管理-回复', 'btndown', 565, '/admin/miniprogram/bookingmanage/disposal', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('咨询管理-列表', 'api', '/admin/miniprogram/consultingmanage/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('咨询管理-处理咨询', 'btndown', 566, '/admin/miniprogram/consultingmanage/report', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('演练管理-列表', 'api', '/admin/miniprogram/rehearsemanage/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('演练管理-处理', 'btndown', 567, '/admin/miniprogram/rehearsemanage/report', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('资料下载-列表', 'api', '/admin/miniprogram/datadownload/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('资料下载-添加', 'btnup', 568, '/admin/miniprogram/datadownload/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('资料下载-编辑', 'btndown', 568, '/admin/miniprogram/datadownload/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('场馆信息-列表', 'api', '/admin/miniprogram/venue/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('场馆信息-添加', 'btnup', 570, '/admin/miniprogram/venue/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('场馆信息-编辑', 'btndown', 570, '/admin/miniprogram/venue/edit', 1);

-- 值班管理
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('值班报备-街道列表', 'api', '/miniprogram/rota/street');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('值班报备（未报备）-列表', 'api', '/miniprogram/rota/unsign');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('值班报备（未报备）-添加', 'btnup', 642, '/miniprogram/company/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('值班报备（未报备）-编辑', 'btndown', 642, '/miniprogram/company/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('值班报备（未报备）-删除', 'btndown', 642, '/miniprogram/company/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('值班报备记录-列表', 'api', '/miniprogram/rota/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('值班街道-列表', 'api', '/miniprogram/rota/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('值班报备（未复工）-列表', 'api', '/miniprogram/rota/undolist');

-- 信访系统
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('接收人管理-列表', 'api', '/ext/emanage/petition/receiverManage/receiverList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('接收人管理-查询部门人员列表', 'api', '/ext/emanage/utils/getDepartMentUser');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('接收人管理-添加', 'btnup', 677, '/ext/emanage/petition/receiverManage/receiverAdd', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('接收人管理-删除', 'btndown', 677, '/ext/emanage/petition/receiverManage/receiverDelete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('信访统计-意见/建议数据', 'api', '/ext/emanage/petition/total/partTotal');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('信访统计-月度数据', 'api', '/ext/emanage/petition/total/monthlyRatio');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('处罚公示-列表', 'api', '/miniprogram/punishment/public/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('处罚公示-新增', 'btnup', 688, '/miniprogram/punishment/public/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('处罚公示-编辑', 'btndown', 688, '/miniprogram/punishment/public/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('处罚公示-删除', 'btndown', 688, '/miniprogram/punishment/public/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('处罚公示-导入', 'btnup', 688, '/admin/excel/punishmentimport', 1);

-- 集中化采购
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('采购列表-列表', 'api', '/ext/emanage/logistics/procure/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('采购列表-新增', 'btnup', 694, '/miniprogram/punishment/public/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('采购列表-编辑', 'btndown', 694, '/ext/emanage/logistics/procure/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('采购列表-采购汇总', 'btnup', 694, '/ext/emanage/logistics/procure/procureMerge', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('汇总采购-列表', 'api', '/ext/emanage/logistics/procure/procureMergeList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('汇总采购-编辑', 'btndown', 695, '/ext/emanage/logistics/procure/mergeProcureEdit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('分发单-列表', 'api', '/ext/emanage/logistics/procure/procureSplitList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('采购设置-状态查询', 'api', '/ext/emanage/logistics/procure/getBuySetting');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('采购设置-保存设置', 'btnup', 720, '/ext/emanage/logistics/procure/saveBuySetting', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('物品列表-列表', 'api', '/ext/emanage/logistics/articalManage/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物品列表-新增', 'btnup', 698, '/ext/emanage/logistics/articalManage/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物品列表-编辑', 'btndown', 698, '/ext/emanage/logistics/articalManage/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('物品分类-列表', 'api', '/ext/emanage/logistics/articalType/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物品分类-新增', 'btnup', 699, '/ext/emanage/logistics/articalType/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物品分类-编辑', 'btndown', 699, '/ext/emanage/logistics/articalType/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('白名单设置-列表', 'api', '/ext/emanage/logistics/whiteListManage/whiteList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('白名单设置-新增', 'btnup', 700, '/ext/emanage/logistics/whiteListManage/whiteListAdd', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('白名单设置-删除', 'btndown', 700, '/ext/emanage/logistics/whiteListManage/whiteListDelete', 1);

-- 水电煤
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('单位设置-列表', 'api', '/ext/emanage/logistics/companyManage/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('单位设置-新增', 'btnup', 703, '/ext/emanage/logistics/companyManage/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('单位设置-编辑', 'btndown', 703, '/ext/emanage/logistics/companyManage/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('单位设置-删除', 'btndown', 703, '/ext/emanage/logistics/companyManage/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('费用录入-列表', 'api', '/ext/emanage/logistics/costEntry/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('费用录入-新增', 'btnup', 704, '/ext/emanage/logistics/costEntry/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('费用录入-导入', 'btnup', 704, '/ext/emanage/logistics/costEntry/import', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('费用录入-编辑', 'btndown', 704, '/ext/emanage/logistics/costEntry/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计报表-数据查询', 'api', '/ext/emanage/logistics/costTotal/costTypeYear');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计报表-数据查询', 'api', '/ext/emanage/logistics/costType/deepSearch');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计报表-数据查询', 'api', '/ext/emanage/logistics/costTotal/costExpensesProportion');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计报表-数据查询', 'api', '/ext/emanage/logistics/costTotal/costDepartYear');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计报表-数据查询', 'api', '/ext/emanage/logistics/costTotal/costYOY');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('费用类型管理-列表', 'api', '/ext/emanage/logistics/costType/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('费用类型管理-新增', 'btnup', 702, '/ext/emanage/logistics/costType/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('费用类型管理-编辑', 'btndown', 702,'/ext/emanage/logistics/costType/edit', 1);

-- 菜品评价 
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('菜品列表-列表', 'api', '/ext/emanage/logistics/dish/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('菜品列表-新增', 'btnup', 709, '/ext/emanage/logistics/dish/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('菜品列表-编辑', 'btndown', 709, '/ext/emanage/logistics/dish/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('菜品列表-获取菜品种类', 'api', 'ext/emanage/logistics/dishType/deepSearch');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('菜品列表-获取评价', 'btndown', 709, '/ext/emanage/logistics/evalution/dishEvalution', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('菜品分类-列表', 'api', '/ext/emanage/logistics/dishType/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('菜品分类-新增', 'btnup', 710, '/ext/emanage/logistics/dishType/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('菜品分类-编辑', 'btndown', 710, '/ext/emanage/logistics/dishType/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('评价记录-列表', 'api', '/ext/emanage/logistics/evalution/evalutionList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('评价记录-详情', 'btndown', 711, '/ext/emanage/logistics/evalution/evalutionDetail', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('白名单-列表', 'api', '/ext/emanage/logistics/recipeWhiteListManage/whiteList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('白名单-新增', 'btnup', 725, '/ext/emanage/logistics/recipeWhiteListManage/whiteListAdd', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('白名单-删除', 'btndown', 725, '/ext/emanage/logistics/recipeWhiteListManage/whiteListDelete', 1);

-- 客饭申请
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('客饭记录-列表', 'api', '/ext/emanage/logistics/hakkaRiceApplyRecord/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('客饭记录-客饭类型查询', 'btnup', 723, '/ext/emanage/logistics/articalType/deepSearch', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('审批人管理-列表', 'api', '/ext/emanage/logistics/hakkaRiceApplyApproval/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('审批人管理-新增', 'btnup', 724, '/ext/emanage/logistics/hakkaRiceApplyApproval/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('审批人管理-编辑', 'btndown', 724, '/ext/emanage/logistics/hakkaRiceApplyApproval/update', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('审批人管理-删除', 'btndown', 724, '/ext/emanage/logistics/hakkaRiceApplyApproval/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('就餐上报管理-记录', 'api', '/ext/emanage/logistics/eatrecord/record');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('就餐上报管理-统计', 'api', '/ext/emanage/logistics/eatrecord/statisticsRecord');

-- 法规分类
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('法规分类-列表', 'api', '/admin/supervision/regulationClassification/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('法规分类-添加', 'btnup', 610, '/admin/supervision/regulationClassification/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('法规分类-编辑', 'btndown', 610, '/admin/supervision/regulationClassification/edit', 1);
-- 法规库
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('法规库-列表', 'api', '/admin/law/list');

-- 人工督查策略管理
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人工督查策略管理-列表', 'api', '/admin/supervision/sheetStrategy/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人工督查策略管理-策略列表', 'api', '/admin/supervision/equipment/getSystem');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人工督查策略管理-事件类型列表', 'api', '/admin/eventTpl/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人工督查策略管理-法规列表', 'api', '/admin/supervision/regulationClassification/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('人工督查策略管理-工作簿', 'api', '/admin/wordbook/list');

-- 下边3个已配置
-- INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('人工督查策略管理-新增', 'btnup', '/admin/supervision/sheetStrategy/add', 1);
-- INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('人工督查策略管理-编辑', 'btndown', '/admin/supervision/sheetStrategy/edit', 1);
-- INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('人工督查策略管理-删除', 'btndown', '/admin/supervision/sheetStrategy/delete', 1);

-- 监管级别管理
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('监管级别管理-列表', 'api', '/admin/supervision/level/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('监管级别管理-设施设备监管配置列表', 'api', '/admin/supervision/equipment/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('监管级别管理-消防安全管理监管配置列表', 'api', '/admin/supervision/firesafety/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('监管级别管理-验证设施设备监管配置', 'api', '/admin/supervision/equipment/exist');

INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-设施设备监管配置新增', 'btnup', 542, '/admin/supervision/equipment/add', 1);

INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-消防安全管理监管配置新增', 'btnup', 542, '/admin/supervision/firesafety/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-新增', 'btnup', 542, '/admin/supervision/level/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-编辑', 'btndown', 542, '/admin/supervision/equipment/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理--设施设备监管配置编辑', 'btndown', 542, '/admin/supervision/level/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-消防安全管理监管配置编辑', 'btndown', 542, '/admin/supervision/firesafety/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-删除', 'btndown', 542, '/admin/supervision/level/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-设施设备监管配置删除', 'btndown', 542, '/admin/supervision/equipment/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管级别管理-消防安全管理监管配置删除', 'btndown', 542, '/admin/supervision/firesafety/delete', 1);

-- 监管条例管理
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('监管条例管理-列表', 'api', '/admin/supervision/regulations/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管条例管理-添加', 'btnup', 543, '/admin/supervision/regulations/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管条例管理-编辑', 'btndown', 543, '/admin/supervision/regulations/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管条例管理-删除', 'btndown', 543, '/admin/supervision/regulations/delete', 1);

-- 督查规则
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('监管条例管理-列表', 'api', '/admin/supervision/strategy/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管条例管理-添加', 'btnup', 544, '/admin/supervision/strategy/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管条例管理-编辑', 'btndown', 544, '/admin/supervision/strategy/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('监管条例管理-删除', 'btndown', 544, '/admin/supervision/strategy/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('监管条例管理-规则配置列表', 'api', '/admin/supervision/regulationClassification');

-- 执法统计
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('执法统计-列表', 'api', '/admin/enforce/employee/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('执法统计-执法记录详情', 'api', '/admin/enforce/record/list');

-- 重大活动预案
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-列表', 'api', '/admin/activity/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-获取地点信息', 'api', '/admin/user/areacode');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-添加', 'btnup', 236, '/admin/activity/create', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-删除', 'btndown', 236, '/admin/activity/delete', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-组织分工信息', 'api', '/admin/activity/getOrgList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-组员配置', 'api', '/admin/activityperson/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-更新组织分工信息', 'btnup', 236, '/admin/activity/update', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-更新组织分工配置信息', 'btnup', 236, '/admin/activity/updateOrg', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-显示区域列表', 'api', '/common/area/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-获取筛选分类类型', 'api', '/common/wordbook/wordbooksearch');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-资源配置添加资源', 'btnup', 236, '/admin/activity/createOrg', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-资源配置删除资源', 'btndown', 236, '/admin/activity/delOrg', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-获取异常事件类型', 'api', '/admin/activity/getEventList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-添加异常事件', 'btnup', 236, '/admin/activity/createEvent', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-删除异常事件', 'btndown', 236, '/admin/activity/delEvent', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('重大活动预案-获取预案信息', 'api', '/admin/activity/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('重大活动预案-发送预案报告', 'btnup', 236, '/admin/activity/notice', 1);

-- 认证审核
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('认证审核-列表', 'api', '/admin/unitlist/getCheckList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('认证审核-审核', 'btnup', 543, '/admin/service/check', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('认证审核-服务商审核短信通知', 'api', '/admin/activity/serviceSNotice');

-- 接入审核
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('接入审核-列表', 'api', '/admin/buildinggroup/getCheckList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('接入审核-获取建筑群列表', 'api', '/admin/buildinggroup/info');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('接入审核-获取建筑对应树', 'api', '/admin/buildinggroup/getTree');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('接入审核-审核', 'btnup', 376, '/admin/buildinggroup/check', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('接入审核-审核通过', 'btnup', 376, '/admin/buildinggroup/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('接入审核-服务商审核短信通知', 'api', '/admin/activity/serviceSNotice');

-- 实时统计
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时统计-天气信息', 'api', '/admin/statistics/getWeather');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时统计-今日救援统计', 'api', '/admin/alarmTrend/today');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时统计-出警趋势', 'api', '/admin/alarmTrend/tendency');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时统计-出警占比', 'api', '/admin/alarmTrend/alarmRatio');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时统计-中队出警排行', 'api', '/admin/alarmTrend/policeRank');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('实时统计-街道出警次数分析', 'api', '/admin/alarmTrend/streetPolice');

-- 统计信息
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计信息-出警对象类型统计（同比）', 'api', '/admin/alarmInformationRecord/alarmTypeStatistics');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计信息-出警对象类型统计（环比）', 'api', '/admin/alarmInformationRecord/alarmTypeChainRatio');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计信息-按出警时段统计', 'api', '/admin/alarmInformationRecord/alarmTimeStatistics');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计信息-按出警时段统计饼图', 'api', '/admin/alarmInformationRecord/alarmATimePieStatistic');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('统计信息-按行政区域统计火警数据对比', 'api', '/admin/alarmTrend/regionAlarm');

-- 出警记录
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('出警记录-列表', 'api', '/admin/alarmInformationRecord/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('出警记录-查看撤回、驳回记录', 'btndown', 576, '/admin/alarmInformationRecord/watchDetail', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('出警记录-查询派出的车辆', 'btndown', 576, '/admin/dispatchingVehicles/findDispatchingVehicles', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('出警记录-查询人员情况', 'btndown', 576, '/admin/rescueWorkers/findRescueWorkers', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('出警记录-驳回记录', 'btndown', 576, '/admin/alarmInformationRecord/rejectInfo', 1);

-- 公示通知
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('公示通知-列表', 'api', '/admin/announcement/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('公示通知-获取公示通知详情信息', 'btndown', 377, '/admin/announcement/info', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('公示通知-编辑公示通知', 'btndown', 377, '/admin/announcement/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('公示通知-添加公示通知', 'btnup', 377, '/admin/announcement/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('公示通知-删除公示通知', 'btnup', 377, '/admin/announcement/delete', 1);

-- 群防群治管理 
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('群防群治管理-列表', 'api', '/admin/message/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('群防群治管理-查看回复', 'btndown', 380, '/admin/messagereply/list', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('群防群治管理-回复', 'btndown', 380, '/admin/messagereply/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('群防群治管理-获取建筑信息', 'api', '/admin/buildinggroup/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('群防群治管理-获取建筑信息', 'api', '/admin/project/building/buildingQueryAll');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('群防群治管理-获取建筑信息', 'api', '/admin/project/building/getOrderList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('群防群治管理-新建任务工单', 'btndown', 380, '/admin/project/repair/workorder/add', 1);

-- 物资类型
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('物资类型-列表', 'api', '/admin/project/material/materialcategory/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物资类型-添加类型', 'btnup', 539, '/admin/project/material/materialcategory/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('物资类型-编辑类型', 'btndown', 539, '/admin/project/material/materialcategory/edit', 1);

-- 系统类型
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('系统类型-列表', 'api', '/admin/system/systemtype/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('系统类型-新增', 'btnup', 479, '/admin/system/systemtype/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('系统类型-列表', 'btndown', 479, '/admin/system/systemtype/edit', 1);

-- 设备类型
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设备类型-列表', 'api', '/admin/firesystemPosition/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备类型-新增', 'btnup', 527, '/admin/firesystemPosition/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备类型-列表', 'btndown', 527, '/admin/firesystemPosition/edit', 1);

-- 设备模型
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设备模型-列表', 'api', '/admin/equipment/setmodel/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-模型属性', 'btnup', 486, '/admin/equipment/setmodel/findattrs', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-查询设备模型属性名称', 'btnup', 486, '/admin/equipment/setmodel/attributes', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-模型组合规则列表', 'btnup', 486, '/admin/equipment/setmodel/groupListModel', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-查询报警策略', 'btnup', 486, '/admin/equipment/setmodel/findalarm', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-新增', 'btnup', 486, '/admin/equipment/setmodel/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-列表', 'btndown', 486, '/admin/equipment/setmodel/editmodel', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-新增组合', 'btnup', 486, '/admin/equipment/setmodel/addGroup', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-编辑组合', 'btndown', 486, '/admin/equipment/setmodel/editGroup', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-模型组合规则下的策略', 'btnup', 486, '/admin/equipment/setmodel/groupElList', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-组合规则下添加策略', 'btnup', 486, '/admin/equipment/setmodel/addGroupEl', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-组合规则下编辑策略', 'btndown', 486, '/admin/equipment/setmodel/editGroupEl', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-更新报警策略', 'btndown', 486, '/admin/equipment/setmodel/editalarm', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('设备模型-新增事件报警策略', 'btnup', 486, 'admin/equipment/setmodel/addalarm', 1);

-- 属性集合 
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('属性集合-列表', 'api', '/admin/equipment/attribute/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-新增', 'btnup', 497, '/admin/equipment/attribute/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-新增', 'btnup', 497, '/admin/mapAttributeUnit/adds', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-详情', 'btndown', 497, '/admin/mapAttributeUnit/getInfo', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-编辑', 'btndown', 497, '/admin/equipment/attribute/edit', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-编辑', 'btndown', 497, '/admin/mapAttributeUnit/edits', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-属性值列表', 'btndown', 497, '/admin/equipment/attributevalue/list', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-新增属性值', 'btnup', 497, '/admin/equipment/attributevalue/add', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('属性集合-编辑属性值', 'btndown', 497, '/admin/equipment/attributevalue/edit', 1);

-- 事件类型
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件类型-列表', 'api', '/admin/eventCategory/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件类型-分类名称种类', 'api', '/admin/eventCategory/searchParent');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件类型-通过分类名称获取id', 'api', '/admin/eventCategory/likeEventName');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('事件类型-添加事件名称', 'btnup', 614, '/admin/eventCategory/addEventClass', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('事件类型-编辑事件名称', 'btndown', 614, '/admin/eventCategory/edit', 1);

-- 事件管理
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件管理-列表', 'api', '/admin/eventTpl/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('事件管理-事件管理名称验证', 'api', '/admin/eventTpl/exist');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('事件管理-新增事件', 'btnup', 615, '/admin/eventTpl/addEventTpl', 1);
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `pid`, `api`, `status`) VALUES ('事件管理-编辑事件', 'btndown', 615, '/admin/eventTpl/editEventTpl', 1);

-- 建筑群内日抄表
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('日抄表设备tabs数据', 'api', '/admin/project/dailyreport/devTabsData');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('系统类型select', 'api', '/admin/system/systemtype/daliyselect');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('系统下的设备select', 'api', '/admin/system/systemtype/dailyDevSelect');



-- 建筑群内日抄表
INSERT INTO `ja_net119`.`pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('日抄表设备tabs数据', 'api', '/admin/project/dailyreport/devTabsData');
INSERT INTO `ja_net119`.`pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('系统类型select', 'api', '/admin/system/systemtype/daliyselect');
INSERT INTO `ja_net119`.`pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('系统下的设备select', 'api', '/admin/system/systemtype/dailyDevSelect');



-- btndown：表格内操作按钮
-- btnup：页面头部操作按钮
-- api:普通的调用接口


-- googoo
-- -- 首页
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页', 'api', '/admin/replyDuration/replyAvg');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页', 'api', '/admin/statistics/getWeather');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('首页', 'api', '/admin/statistics/headerTotal');

-- replyAvg
-- -- 基础信息
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-服务商信息', 'api', '/admin/service/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-建筑管理单位', 'api', '/admin/unitlist/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-维保单位', 'api', '/admin/maintenance/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-消火栓信息', 'api', '/admin/firehydrant/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-微型消防站', 'api', '/admin/firehouse/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-街道信息', 'api', '/admin/street/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-入驻单位', 'api', '/admin/unitlist/count');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-维保单位', 'api', '/admin/maintenance/getList');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-单位信息', 'api', '/admin/companyTagCategoryRelation/getCompanyTag');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('基础信息-单位信息', 'api', '/admin/buildinggroup/access');

-- -- 设置
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('通知模板', 'api', '/admin/system/systemtype/select');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('计量单位', 'api', '/admin/unit/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('属性值', 'api', '/admin/value/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('系统设置', 'api', '/admin/supervision/settingconfig/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设置-通知模板', 'api', '/admin/equipment/alarmrole/list');
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('设置-管辖范围组管理', 'api', '/admin/user/areacodes');

-- admin/rolemanager/droplist?selffield=status&val=&display=display
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('用户管理', 'api', '/admin/rolemanager/droplist');

-- admin/rolemanager/droplist?selffield=status&val=&display=display
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('角色管理', 'api', '/admin/rolemanager/droplist');


INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('管辖范围组管理', 'api', '/admin/projectgroup/list');
-- -- 个人资料
INSERT INTO `pt_auth_rule` (`desc_name`, `type`, `api`) VALUES ('个人资料', 'api', '/admin/user/findUser');

