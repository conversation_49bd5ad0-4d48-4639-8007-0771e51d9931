ALTER TABLE `pt_task`
ADD COLUMN `reason_status` int(11) COMMENT '1.误报 2.保修 3.其他' AFTER `reason`;

ALTER TABLE `pt_task`
ADD COLUMN `handler` varchar(100) COMMENT '处理人' AFTER `reason_status`;

ALTER TABLE `pt_task`
ADD COLUMN `relieve_time` datetime(0) COMMENT '解除屏蔽时间' AFTER `reason_status`;

ALTER TABLE `pt_task`
MODIFY COLUMN `reason_status` tinyint(4) DEFAULT NULL COMMENT '1.误报 2.保修 3.其他' AFTER `reason`;

ALTER TABLE `pt_task`
MODIFY COLUMN `repair_id` varchar(32) DEFAULT NULL COMMENT '维修工单自增ID' AFTER `position_id`;