ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `handler` varchar(100) COMMENT '处理人' AFTER `larum_level_name`;

ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `reason` varchar(255) COMMENT '记录原因' AFTER `larum_level_name`;

ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `reason_image` varchar(255) COMMENT '记录照片' AFTER `larum_level_name`;

ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `reason_status` tinyint(4) COMMENT '1.误报 2.报修 3.其他 4.屏蔽' AFTER `reason_image`;

ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `relieve_time` datetime(0) COMMENT '解除屏蔽时间' AFTER `reason_status`;


ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `loop_number` varchar(40) COMMENT '回路编号（部件区号）' AFTER `attr_type`;


ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `repair_id` varchar(32) COMMENT '维修工单自增ID' AFTER `handler`;

ALTER TABLE `pt_alarm_manage_fire`
ADD COLUMN `reason_time` datetime(0) COMMENT '修改时间' AFTER `reason_image`;

ALTER TABLE `pt_task`
ADD COLUMN `overview` text NULL COMMENT '电子督察单概述' AFTER `reason_image`;

ALTER TABLE `pt_task`
ADD COLUMN `regulations_id` varchar(50) NULL COMMENT '监管条例id' AFTER `overview`;