
const config = require('../src/configs.js');
const Knex = require('knex');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const moment = require('moment');

let mysql;
let mongo;

// 修改报警事件已经填写了记录原因，电子督查单还未关闭
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const res = await mysql.raw(` select * from pt_basic_event where status = 5 `)
    const ret = await mysql.raw(` select * from pt_task where type = 5 `)
    console.log(res[0].length);
    for (const i of res[0]) {
      console.log('开始删除报警事件了');
      i.reason = '修复数据关闭';
      i.reason_time = moment().unix();
      await mongo.collection(`pt_basic_event_log_202104`).insert(i);
      await mysql('pt_basic_event').where({id: i.id}).delete();
    }
    for (const i of ret[0]) {
      console.log('开始删除电子督查单');
      i.reason = '修复数据关闭';
      i.reason_time = moment().format('YYYY-MM-DD HH:mm:ss');
      await mongo.collection(`pt_task_log`).insert(i);
      await mysql('pt_task').where({id: i.id}).delete();
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
