
const config = require('../src/configs.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const moment = require('moment');
moment.locale('zh');
const Knex = require('knex');

let mongo;
let mysql;

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function main() {
  try {
    mongo = await getMongo();
    mysql = await getConn();
    const basicEvent = await mysql.raw(` select * from pt_basic_event where project_id = 570 and event_type = 10 and status = 0 `);
    for (const i of basicEvent[0]) {
      console.log(i.id);
      const sql = ` UPDATE pt_basic_event set handler = '王诚', reason_time = 1609141221, status = 2 where id = ${i.id} `;
      const res = await mysql.raw(sql);
      console.log('11成功了', res[0].affectedRows);
    }
    const taskData = await mysql.raw(` select * from pt_task where projectid = 570 and event_type = 10 and type = 1 `);
    for (const i of taskData[0]) {
      const sql = ` UPDATE pt_task set handler = '王诚', reason_time = '${moment().format('YYYY-MM-DD HH:mm:ss')}' where id = ${i.id} `;
      const res = await mysql.raw(sql);
      console.log('task成功了', res[0].affectedRows);
      await mongo.collection(`pt_task_log`).insert(i);
      await mysql('pt_task').where({id: i.id}).delete();
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
