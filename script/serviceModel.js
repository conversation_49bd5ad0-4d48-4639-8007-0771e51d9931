const mysql = require('promise-mysql');
const config = require('../src/config.js');

let conn;

async function getConn() {
  const option = {
    host: config.mysql.host,
    port: config.mysql.port,
    user: config.mysql.username,
    password: config.mysql.password,
    database: config.mysql.database
  };
  const mysqlConn = await mysql.createConnection(option);
  return mysqlConn;
}

async function main() {
  try {
    conn = await getConn();
    const companyId = 9262;

    const company = await conn.query(`select * from pt_company where id=${companyId}`);

    const list = await conn.query(`select * from pt_setmodel order by system_id,type_id asc`);
    for (const v of list) {
      const count = await conn.query(`select count(*) as count from pt_map_service_model where company_id=${companyId} and system_id=${v.system_id} and type_id=${v.type_id}`);
      if (count[0].count === 0) {
        await conn.query('insert into pt_map_service_model(`company_id`,`service_admin_id`,`system_id`,`type_id`,`model_id`,`created_at`,`updated_at`) values (?, ?, ?, ?, ?, now(), now())', [companyId, company[0].admin_id, v.system_id, v.type_id, v.id]);
      }
    }

    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
