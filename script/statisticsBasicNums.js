
const config = require('../src/configs.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');

let mongo;
let mysql;

// 检查电子督查单数据状态
async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const data = await mongo.collection(`pt_basic_event_log_202012`).aggregate([
      {
        $group: {
          _id: '$event_id'
        }
      }
    ]).toArray();
    console.log(data.length)
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
