
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');
const request = require('request-promise');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    let api;
    if (config.server_env === 'dev') {
      api = 'http://127.0.0.1:8360';
    } else if (config.server_env === 'demo') {
      api = 'http://api.119.net';
    } else if (config.server_env === 'shanghai') {
      api = 'https://shapi.119.net';
    } else if (config.server_env === 'jingan') {
      api = 'https://jaapi.119.net';
    } else if (config.server_env === 'huangpu') {
      api = 'https://hp.119.net/api';
    }
    const connections = await mongo.listCollections().toArray();
    const tableList = [];
    for (const i of connections) {
      if (i.name.includes('pt_alarm_chain_log_')) {
        tableList.push(i.name);
      }
    }
    for (const i of tableList) {
      const ilist = await mongo.collection(i).find({}).toArray();
      for (const j of ilist) {
        if (j.overtime_reason && j.alarm_reason && j.event_id) {
          const url = api + '/admin/basicEvent/getEventById?id=' + j.event_id;
          const body = await request.get(url);
          const info = JSON.parse(body);
          if (info.id > 0) {
            console.log(info);
            const obj = {
              overtime_reason: j.overtime_reason,
              alarm_reason: j.alarm_reason,
              happen_time: j.created_at,
              pid: info.project_id
            };
            await mysql('pt_record_reason').insert(obj);
            console.log('插入成功');
          }
        }
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
