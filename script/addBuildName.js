
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();

    await mysqlMerge(mysql);
    process.exit();
  } catch (err) {
    console.log(err);
  }
}
function getSmallArr(array, subGroupLength) {
  let index = 0;
  const newArray = [];
  while (index < array.length) {
    newArray.push(array.slice(index, index += subGroupLength));
  }
  return newArray;
}

async function mysqlMerge(mysql) {
  console.log(`开始修复--------------------`);
  const list = await mysql.raw(`select id,floor_id from pt_basic_event where building_id = 0`);
  const floorSet = new Set();
  for (const i of list[0]) {
    floorSet.add(i.floor_id);
  }
  const floorList = [...floorSet];
  const floorStr = floorList.join(',');
  const floor = await mysql.raw(`select a.id,b.bbid,b.b_name,a.name from pt_floor as a left join (select id as bbid,b_name from pt_building) as b on a.building_id = b.bbid  where a.id in (${floorStr})`);
  const floorObj = {};

  for (const f of floor[0]) {
    floorObj[f.id] = f;
  }
  let n = 0;
  for (const i of list[0]) {
    const ob = floorObj[i.floor_id];
    await mysql('pt_basic_event').where('id', '=', i.id).update({floor_name: ob.name, building_id: ob.bbid, building_name: ob.b_name});
    n++;
    console.log(`${i.id} 已修改--------当前第${n}条---------------`);
  }
}
main();
