
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
function getLocalTime(timestamp = 0) {
  const date = new Date(timestamp * 1000); // 时间戳为10位需*1000，时间戳为13位的话不需乘1000
  const Y = date.getFullYear() + '-';
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
  const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
  const s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
  return Y + M + D + h + m + s;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const newTime = Math.ceil(new Date().getTime() / 1000);
    const mysqlList = await mysqlTotal();
    const mongoList = await mongoTotal();
    const allList = [];
    const mysqlObj = {};
    const mongoObj = {};
    let nL = 0;
    for (const i of mysqlList) {
      mysqlObj[i.today_date + i.event_type] = i;
    }
    for (const i of mongoList) {
      mongoObj[i.today_date + i.event_type] = i;
    }
    for (const m in mongoObj) {
      if (m in mysqlObj) {
        nL += mysqlObj[m].times;
        nL += mongoObj[m].times;
        allList.push({ti: Date.parse(new Date(mongoObj[m].today_date)), event_type: mongoObj[m].event_type, today_date: mongoObj[m].today_date + ' 00:00:00', times: mongoObj[m].times += mysqlObj[m].times, updated_at: getLocalTime(newTime)});
      } else {
        nL += mongoObj[m].times;
        allList.push({ti: Date.parse(new Date(mongoObj[m].today_date)), event_type: mongoObj[m].event_type, today_date: mongoObj[m].today_date + ' 00:00:00', times: mongoObj[m].times, updated_at: getLocalTime(newTime)});
      }
    }
    console.log(`当前总计数量：${nL}------------------------`);
    for (const m in mysqlObj) {
      if (m in mongoObj) {
      } else {
        nL += mysqlObj[m].times;
        allList.push({ti: Date.parse(new Date(mysqlObj[m].today_date)), event_type: mysqlObj[m].event_type, today_date: mysqlObj[m].today_date + ' 00:00:00', times: mysqlObj[m].times, updated_at: getLocalTime(newTime)});
      }
    }
    allList.sort((a, b) => {
      return a.ti - b.ti;
    });
    for (const i of allList) {
      delete i.ti;
    }
    console.log(`当前总计数量：${nL}------------------------`);
    await mysql('pt_event_amount').insert(allList);
    console.log(`数据已更新到mysql-----------------`);
    process.exit();
  } catch (err) {
    console.log(err);
  }
}
async function mysqlTotal() {
  const list = await mysql.raw(`select DATE_FORMAT(created_at,'%Y-%m-%d') as today_date,event_type,count(*) as times from pt_basic_event group by today_date,event_type`);
  let n = 0;
  for (const i of list[0]) {
    n += i.times;
  }
  console.log('mysql总计条数:', n);
  return list[0];
}
async function mongoTotal() {
  const list = await mongo.collection('pt_basic_event_log_202004').aggregate([
    {'$group': {'_id': {'date': {'$dateToString': {'format': '%Y-%m-%d', 'date': '$created_at'}}, 'event_type': '$event_type'}, 'count': {'$sum': 1}}}
  ]).toArray();
  const reList = [];
  let m = 0;

  for (const i of list) {
    if (i._id.date && i._id.event_type) {
      const obj = {today_date: i._id.date, event_type: i._id.event_type, times: i.count};
      m += i.count;
      reList.push(obj);
    }
  }
  console.log('mongo总计条数:', m);
  return reList;
}
main();
