
const config = require('../src/configs.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const moment = require('moment');
moment.locale('zh');

let mongo;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function main() {
  try {
    mongo = await getMongo();
    const data = ['04', '05', '06', '07', '08', '09', '10', '11'];
    // const data = ['09'];
    for (const j of data) {
      let beginTimes = '2020-04-01 00:00:00';
      beginTimes = moment(beginTimes).set('month', parseInt(j) - 1);
      let beginTime;
      let endTime;
      let res;
      let ret;
      for (let k = 1; k <= 45; k++) {
        if (k === 1) {
          beginTime = moment(beginTimes).subtract(30, 'days').format('YYYY-MM-DD 00:00:00');
        } else {
          beginTime = moment(beginTimes).format('YYYY-MM-DD 00:00:00');
        }
        endTime = moment(beginTimes).add(1, 'days').format('YYYY-MM-DD 00:00:00');
        beginTimes = endTime;
        const bgTime = moment(beginTime).unix();
        const edTime = moment(endTime).unix();
        console.log(bgTime);
        console.log(bgTime);
        res = await mongo.collection(`pt_basic_event_log_2020${j}`).find({created_at: {$gte: bgTime, $lt: edTime}}).toArray();
        if (res.length > 0) {
          for (const i of res) {
            if (i.created_at && i.reason_time) {
              ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                event_id: i.event_id
              }, {
                $set: {
                  'diff_time': parseInt(i.reason_time) - parseInt(i.created_at)
                }
              });
            } else if (i.created_at && i.relieve_time && !i.reason_time) {
              ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                event_id: i.event_id
              }, {
                $set: {
                  'diff_time': parseInt(i.relieve_time) - parseInt(i.created_at)
                }
              });
            }
            console.log('ret', 1);
          }
        }
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
