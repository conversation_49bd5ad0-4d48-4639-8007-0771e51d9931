
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const connections = await mongo.listCollections().toArray();
    const tableList = [];
    const taskList = [];
    for (const i of connections) {
      if (i.name.includes('pt_alarm_manage_fire_log_')) {
        tableList.push(i.name);
      } else if (i.name.includes('pt_alarm_fault_log_')) {
        tableList.push(i.name);
      } else if (i.name.includes('pt_task_log_')) {
        taskList.push(i.name);
      }
    }
    const eventOld = await countCheck(tableList);
    const taskOld = await countCheck(taskList);
    const eventNew = await mongo.collection(`pt_basic_event_log_202004`).find({}).count();
    const taskNew = await mongo.collection(`pt_task_log`).find({}).count();
    console.log({eventOld, eventNew, taskOld, taskNew});
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

async function countCheck(table) {
  let n = 0;
  for (const i of table) {
    const count = await mongo.collection(i).find({}).count();
    n += count;
    console.log(`${i} 共记录：${count} 条`);
  }
  return n;
}
main();
