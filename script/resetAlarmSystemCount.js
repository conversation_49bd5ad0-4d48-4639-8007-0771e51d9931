
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}



async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function getPorjectList() {
  const list = await mysql('pt_building_group').select('*');
  return list;
}


async function handleData(timestamp, v) {
  const pid = v.id;
  const timestampEnd = timestamp + 86400;
  const startDay = util.format(new Date(timestamp * 1000));
  const endDay = util.format(new Date((timestamp + 86400) * 1000));
  const start = new Date(startDay);
  const end = new Date(endDay);


  const sqlCount0 = await mysql('pt_basic_event').whereRaw(`sid=40 and project_id=? and created_at>=? and created_at<?`, [pid, startDay, endDay]).count('* as c');
  const mCount0 = await await mongo.db(config.mongo.database).collection(`pt_alarm_fault_log_${pid}`).find({sid: 40, created_at: {$gte: start, $lt: end}}).count();

  const sqlCount1 = await mysql('pt_basic_event').whereRaw(`sid=41 and project_id=? and created_at>=? and created_at<?`, [pid, startDay, endDay]).count('* as c');
  const mCount1 = await await mongo.db(config.mongo.database).collection(`pt_alarm_fault_log_${pid}`).find({sid: 41, created_at: {$gte: start, $lt: end}}).count();

  const sqlCount2 = await mysql('pt_basic_event').whereRaw(`sid=42 and project_id=? and created_at>=? and created_at<?`, [pid, startDay, endDay]).count('* as c');
  const mCount2 = await await mongo.db(config.mongo.database).collection(`pt_alarm_manage_fire_log_${pid}`).find({sid: 42, created_at: {$gte: start, $lt: end}}).count();

  const sqlCount6 = await mysql('pt_basic_event').whereRaw(`sid=46 and project_id=? and created_at>=? and created_at<?`, [pid, startDay, endDay]).count('* as c');
  const mCount6 = await await mongo.db(config.mongo.database).collection(`pt_alarm_fault_log_${pid}`).find({sid: 46, created_at: {$gte: start, $lt: end}}).count();

  const sqlCount7 = await mysql('pt_basic_event').whereRaw(`sid=47 and project_id=? and created_at>=? and created_at<?`, [pid, startDay, endDay]).count('* as c');
  const mCount7 = await await mongo.db(config.mongo.database).collection(`pt_alarm_fault_log_${pid}`).find({sid: 47, created_at: {$gte: start, $lt: end}}).count();

  const count0 = sqlCount0[0].c + mCount0;
  const count1 = sqlCount1[0].c + mCount1;
  const count2 = sqlCount2[0].c + mCount2;
  const count6 = sqlCount6[0].c + mCount6;
  const count7 = sqlCount7[0].c + mCount7;

  await saveStat(pid, 40, count0, startDay);
  await saveStat(pid, 41, count1, startDay);
  await saveStat(pid, 42, count2, startDay);
  await saveStat(pid, 46, count6, startDay);
  await saveStat(pid, 47, count7, startDay);

}

const systemMap = {
  '40': '消防给水及消火栓系统',
  '41': '自动喷水灭火系统',
  '42': '火灾自动报警系统',
  '46': '防排烟系统',
  '47': '电气火灾监控系统'
};
async function saveStat(pid, sid, count, dayStr) {
  if (pid === 270) {
    console.log(`${pid}   ${sid}    ${count}     ${dayStr}`)
  }
  const name = systemMap[sid];
  const date = new Date(dayStr);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const week = util.getWeekNumber(date);
  const day = util.format(date, 'Y-m-d');

  const condition = {pid: pid, sid: sid, day: day};
  const info = await mysql('pt_alarm_stat_count').where(condition).first();
  if (count > 0) {
    if (_.isEmpty(info)) {
      const dateTime = util.format(date);
      const obj = {pid: pid, year, month, week, day, sid: sid, name, count: count, created_at: dateTime, updated_at: dateTime};
      await mysql('pt_alarm_stat_count').insert(obj);
    } else {
      await mysql('pt_alarm_stat_count').where(condition).update({count: count}).limit(1);
    }
  }
}

async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const day = '2020-12-03 12:12:12';
    const startDay = util.format(new Date(day), 'Y-m-d');
    const startTimestamp = Math.round(new Date(startDay).getTime() / 1000) - 3600 * 8;
    console.log(startTimestamp);

    const today = util.format(new Date(), 'Y-m-d');
    const todayTimestamp = Math.round(new Date(today).getTime() / 1000) - 3600 * 8;
    console.log(todayTimestamp);

    const list = await getPorjectList();
    for (let timestamp = startTimestamp; timestamp <= todayTimestamp; timestamp = timestamp + 86400) {
      for (const v of list) {
        await handleData(timestamp, v);
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
