
const config = require('../src/config.js');
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');
const request = require('request-promise');

let mysql;

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    // const userId = [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42];
    const userId = [3];
    for (const i in userId) {
      const pid = [];
      const userinfo = await mysql.raw(` select * from pt_admin where id = ${userId[i]} `);
      for (const j of userinfo[0]) {
        const buildInfo = await mysql.raw(` select id from pt_building_group where street_code in (${j.street_code}) `);
        for (const k of buildInfo[0]) {
          pid.push(k.id);
        }
        const sql = ` UPDATE pt_admin set app_building_group = '${pid.toString()}' where id = ${userId[i]} `;
        const res = await mysql.raw(sql);
        console.log('11成功了', res.changedRows);
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
