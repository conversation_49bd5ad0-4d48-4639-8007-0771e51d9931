const Knex = require('knex');
const moment = require(`moment`);
const config = require('../src/configs.js');

let mysql;

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}

async function main() {
  try {
    mysql = await getConn();
    const list = await mysql.raw(`select * from pt_firesystem_position_1 where id not in (select position_id from pt_basic_event where position_id > 0) and state = 1 `);
    const data = { state: 0, val: 0, val_name: '正常', updatetime: moment().unix() };
    console.log('lists', list);
    for (const i of list[0]) {
      await mysql('pt_firesystem_position_1').where({id: i.id}).update(data);
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
