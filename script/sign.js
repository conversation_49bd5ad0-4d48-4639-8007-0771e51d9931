const crypto = require('crypto');

function getSign(params, secret) {
  const keys = Object.keys(params).sort();
  let signStr = secret;
  for (const k of keys) {
    signStr += k;
    if (typeof params[k] === 'object') {
      signStr += JSON.stringify(params[k])
    } else {
      signStr += params[k];
    }
  }
  signStr += secret;
  const hash = crypto.createHash('md5');
  return hash.update(signStr).digest('hex').toLocaleUpperCase();
};

const params = {
  app_key: '825740f203946fe19440d57bdf443cb4',
  timestamp: 1623307921
}

const secret = 'c8e45be7a47c4ba692effd3b2d2f543e'

console.log(getSign(params, secret))