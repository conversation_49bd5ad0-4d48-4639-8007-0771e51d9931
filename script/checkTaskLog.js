
const config = require('../src/configs.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');

let mongo;
let mysql;

// 检查电子督查单数据状态
async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const res = await mysql('pt_task').select();
    console.log(res.length);
    for (const i of res) {
      const basicEvent = await mysql('pt_basic_event').where({event_id: i.event_id}).select();
      // 如果不存在 就开始删除并移到log表中 同时需要修改 type的状态为5
      if (basicEvent.length === 0) {
        console.log(1111111)
        i.type = 5;
        // 移入mongo
        const ret = await mongo.collection(`pt_task_log`).insert(i);
        // 删除task表中的数据
        await mysql('pt_task').where({event_id: i.event_id}).delete();
        console.log('ret', ret.result.n);
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
