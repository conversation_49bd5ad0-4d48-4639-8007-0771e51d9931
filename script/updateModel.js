
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const moment = require('moment');
const amqp = require('amqplib');
const mqConn = config.rabbitmq;
let mongo;
let mysql;
let rabbit;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function getRabbit() {
  const conn = await amqp.connect(mqConn);
  const ch = await conn.createChannel();
  return ch;
}

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}

async function updateModel() {
  console.log('开始修复，获取数据中请稍后-----------');
  const positionModelList = await mysql.raw(`select * from pt_setmodel where devtype = 1  `);
  const deviceModelList = await mysql.raw(`select * from pt_setmodel where devtype = 0  `);
  const pL = positionModelList[0].map(m => m.id);
  const dL = deviceModelList[0].map(m => m.id);
  console.log(`part_1 start------------------------`);
  await startDelete(pL, 'attribute_id != 113');
  console.log(`part_1 end------------------------`);
  console.log(`part_2 start------------------------`);

  await startDelete(dL, 'attribute_id = 113');
  console.log(`part_2 end------------------------`);
  console.log(`part_3 start------------------------`);

  const attrList = [
    {val_id: 23, name: '火警', event_id: 1, record: 0, msg: '触发火警，请尽快处理'},
    {val_id: 16, name: '故障', event_id: 3, record: 0, msg: '触发故障，请尽快处理'},
    {val_id: 24, name: '屏蔽', event_id: 10, record: 0, msg: '触发屏蔽，请尽快处理'},
    {val_id: 26, name: '监管', event_id: 30, record: 1, msg: '点位监管，请关注'},
    {val_id: 13, name: '启动', event_id: 12, record: 1, msg: '点位启动，请关注'},
    {val_id: 48, name: '延时', event_id: 14, record: 1, msg: '点位延时，请关注'},
    {val_id: 50, name: '电源故障', event_id: 32, record: 1, msg: '电源故障，请尽快处理'},
    {val_id: 42, name: '反馈', event_id: 31, record: 0, msg: '点位反馈，请关注'}
  ];
  await addDev(pL, attrList);
  console.log(`part_3 end------------------------`);

  console.log('修复完毕-----------------');
}
async function addDev(list, attrList) {
  if (list.length) {
    for (const i of list) {
      const dev = await mysql.raw(`select * from pt_devmodel_attribute where model_id = ${i} and attribute_id = 113`);
      if (dev[0].length) {
        const d = dev[0][0];
        const r = await mysql.raw(`select * from pt_model_event_rule where devmodel_attribute_id = ${d.id} `);
        const ruleList = r[0];
        const needL = [];
        if (ruleList.length) {
          for (const a of attrList) {
            if (ruleList.some(r => r.event_value === a.val_id)) {

            } else {
              needL.push(a);
            }
          }
          await addRule(needL, i, d.id);
        } else {
          await addRule(attrList, i, d.id);
        }
      } else {
        const d = await mysql(`pt_devmodel_attribute`).insert({
          name: '设备点位状态',
          aid: '',
          model_id: i,
          type: 1,
          attribute_id: 113,
          display: 1,
          sortnum: 1,
          floatdata: 0,
          unitmultiple: '',
          devaddress: 0,
          featuresid: '',
          rgaddress: 0,
          unitid: 0,
          dataformatid: '',
          varcharid: '',
          addressat: '',
          accuracy: 0,
          acqcycle: '',
          zoom: 0,
          isdelete: 0,
          creatorid: 1,
          createtime: moment(moment().format('YYYY-MM-DD HH:mm:ss')).unix(),
          modifierid: 0,
          updatetime: 0

        });
        await addRule(attrList, i, d);
      }
      console.log(`${i} 模型规则添加完毕----------------------`);
    }
  }
}
async function addRule(list, modelid, did) {
  const addList = [];
  for (const i of list) {
    addList.push({
      event_id: i.event_id,
      auto_rec: 0,
      model_id: modelid,
      build_type: 0,
      typeid: 113,
      devmodel_attribute_id: did,
      event_state: 1,
      symbol_id: 3,
      event_value: i.val_id,
      legal_id: 3,
      isdelete: 0,
      only_record: i.record,
      creatorid: 1,
      modifierid: 0,
      created_at: moment(moment().format('YYYY-MM-DD HH:mm:ss')).unix(),
      updated_at: 0,
      type: 1,
      event_content: i.msg
    });
  }
  if (addList.length) {
    await mysql('pt_model_event_rule').insert(addList);
  }
}
async function startDelete(list, where) {
  if (list.length > 0) {
    const plStr = list.join(',');
    const devAList = await mysql.raw(`select * from pt_devmodel_attribute where model_id in (${plStr}) and ${where} `);
    if (devAList[0].length) {
      for (const d of devAList[0]) {
        const ruleProjectDevice = await mysql.raw(`select * from pt_model_event_rule where devmodel_attribute_id =  ${d.id} `);
        if (ruleProjectDevice[0].length) {
          for (const r of ruleProjectDevice[0]) {
            await deleteRule(r.id, 'model', '');
            await mysql('pt_model_event_rule').where('id', r.id).del();
            console.log(`${d.id} 属性相关设备规则 ${r.id} 已删除------------------------`);
          }
        }
        await mysql('pt_devmodel_attribute').where('id', d.id).del();
        console.log(`${d.id} 属性已删除`);
      }
    }
  }
}

async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    rabbit = await getRabbit();
    await updateModel();
    process.exit();
  } catch (err) {
    console.log(err);
  }
}
async function deleteRule(id, from, user) {
  let _sql = ` group_rule_id = 0 `;
  if (from === 'device') {
    _sql += ` and is_alone  = 1`;
  } else {
    _sql += ` and is_alone  = 0`;
  }
  _sql += ` and event_rule_id = ${id}`;
  const l = await mysql.raw(`select * from pt_basic_event where ${_sql}`);
  const list = l[0];
  if (list.length) {
    const idArr = [];
    const suArr = [];
    const updateObj = {
      reason_status: 3,
      reason: `规则${id} 禁用或删除`,
      handler: user.nickname || '',
      reason_time: moment().format('YYYY-MM-DD HH:mm:ss'),
      handler_mobile: user.phonenumber,
      status: 10
    };
    const updateObjT = {
      reason_status: 3,
      reason: `规则${id} 禁用或删除`,
      handler: user.nickname || '',
      reason_time: moment().format('YYYY-MM-DD HH:mm:ss'),
      handler_mobile: user.phonenumber
    };
    for (const i of list) {
      idArr.push(i.id);
      suArr.push(i.supervise_id);
    }
    if (idArr.length) await mysql('pt_basic_event').where('id', 'in', idArr).update(updateObj);
    if (idArr.length) await mysql('pt_task').where('supervise_id', 'in', suArr).update(updateObjT);

    const re = await mysql.raw(`select * from pt_basic_event where ${_sql}`);
    for (const i of re[0]) {
      // await rabbitmq.sendToQueue('lv1_handle_event', i);
      await rabbit.sendToQueue('lv1_handle_event', Buffer.from(JSON.stringify(i)), {persistent: true});
    }
  }
}

main();
