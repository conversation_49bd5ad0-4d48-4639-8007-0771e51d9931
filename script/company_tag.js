
const mysql = require('mysql2');
const monent = require('moment');

const connectionlocal = mysql.createConnection({
  port: 3306,
  host: '*************',
  user: 'root',
  password: 'intel...',
  database: 'ja_net119'
});
async function main() {
  try {
    let localData;
    let localDataHp;
    // const beginTime = monent().format('YYYY-MM-DD HH:00:00');
    // const endTime = monent().set('hours', monent(beginTime).get('hours') + 1).format('YYYY-MM-DD HH:00:00');
    const beginTime = '2020-10-10 15:00:00';
    const endTime = '2020-10-10 16:00:00';
    let InSum;
    let OutSum;
    let totalSum = 0;
    await connectionlocal.promise().query(`select sum(InSum) as InSum, sum(OutSum) as OutSum from intsummary_base where createTime BETWEEN '${beginTime}' and '${endTime}'`).then(([rows, fields]) => {
      localData = rows;
      console.log(localData[0].InSum);
      console.log(localData[0].OutSum);
      InSum = localData[0].InSum;
      OutSum = localData[0].OutSum;
    }).catch(console.log);

    await connectionlocal.promise().query(`select * from ywjk_passagerflow where time = '${monent().format('YYYY-MM-DD')}'`).then(([rows, fields]) => {
      localDataHp = rows;
      for (const i of localDataHp) {
        if (i.spotid === 'YUYUANW') {
          const hoursData = JSON.parse(i.hours);
          for (const j in hoursData) {
            if (parseInt(j) === monent().get('hours')) {
              hoursData[j] = InSum;
            }
            totalSum += parseInt(hoursData[j]);
          }
          i.updatetimer = monent().format('YYYY-MM-DD HH:mm:ss');
          i.totalnumber = totalSum;
          i.realtimenumber = InSum;
          i.hours = JSON.stringify(hoursData);
          i.increase = JSON.stringify(hoursData);
        }
      }
    }).catch(console.log);
    // 更新豫园内圈的数据
    for (const i of localDataHp) {
      if (i.spotid === 'YUYUANW') {
        console.log('进入了修改');
        const modSql = 'UPDATE ywjk_passagerflow SET updatetimer = ?,totalnumber = ?, realtimenumber = ?, hours = ?, increase = ? WHERE spotid = ? and time = ?';
        const modSqlParams = [i.updatetimer, i.totalnumber, i.realtimenumber, i.hours, i.increase, i.spotid, i.time];
        console.log('iii', i);
        console.log('modSqlParams', modSqlParams);
        await connectionlocal.promise().query(modSql, modSqlParams, function(err, result) {
          if (err) {
            console.log('[UPDATE ERROR] - ', err.message);
            return;
          }
          console.log(result.affectedRows);
        });
      }
    }
    // const addSql = 'INSERT INTO ywjk_passagerflow(spotid, updatetimer, totalnumber, realtimenumber, time, hours, increase) values(?, ?, ?, ?, ?, ?, ?)';
    // const addSqlParams = ['YUYUANW', monent().format('YYYY-MM-DD HH:mm:ss'), 0, 0, monent().format('YYYY-MM-DD'), '{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":769,"10":0,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":0}', '{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":769,"10":0,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":0}'];
    // await connectionlocal.promise().query(addSql, addSqlParams, (err, res) => {
    //   if (err) {
    //     console.log('[INSERT ERROR] - ', err.message);
    //   }
    //   console.log(res);
    // });
    connectionlocal.end()
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
