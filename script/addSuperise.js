
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();

    const connections = await mongo.listCollections().toArray();
    const arr = await mongo.collection('pt_ids_list').find({name: 'task_log'}).toArray();
    if (!arr.length) {
      mongo.collection('pt_ids_list').insert({name: 'task_log', value: 0, createTime: Math.ceil(new Date().getTime() / 1000)});
    }
    const tableList = [];
    const artiList = [];
    for (const i of connections) {
      if (i.name.includes('pt_task_log_')) {
        tableList.push(i.name);
      }
      if (i.name.includes('pt_supervise_sheet_artificial_log_')) {
        artiList.push(i.name);
      }
    }
    await taskMerge(tableList);
    await artiMerge(artiList);
    await taskMergeMysql();
    process.exit();
  } catch (err) {
    console.log(err);
  }
}
function getSmallArr(array, subGroupLength) {
  let index = 0;
  const newArray = [];
  while (index < array.length) {
    newArray.push(array.slice(index, index += subGroupLength));
  }
  return newArray;
}
function timestampToTime(timestamp) {
  var date = new Date(timestamp * 1000);// 时间戳为10位需*1000，时间戳为13位的话不需乘1000
  var Y = date.getFullYear() + '-';
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  var D = date.getDate() + ' ';
  var h = date.getHours() + ':';
  var m = date.getMinutes() + ':';
  var s = date.getSeconds();
  return Y + M + D + h + m + s;
}
async function taskMergeMysql() {
  console.log('电子督察单mysql开始迁移---------------------------');
  // mysql 电子督察单改动 所有reason_status 为 0 的记录type改为1，不为 0改为2

  const list = await mysql('pt_task').select('*');
  const devList = [];
  for (const i of list) {
    if (i.pro_dev_id) devList.push(i.pro_dev_id);
  }
  const devObj = {};
  if (devList.length) {
    const devStr = devList.join(',');
    const devL = await mysql.raw(`select id,systemid from pt_projectdev where id in (${devStr})`);
    const dev = devL[0];
    if (dev.length > 0) {
      for (const d of dev) {
        devObj[d.id] = d.systemid;
      }
    }
    for (const i of list) {
      const obj = {};
      if (i.reason_status === 0) {
        obj.type = 1;
      } else {
        obj.type = 2;
      }
      obj.sid = devObj[i.pro_dev_id] || 0;
      await mysql('pt_task').where('id', '=', i.id).update(obj);// mysql现有数据 status = 0 的改为 status =1
      console.log('已完成--------------');
    }
  }
}
async function taskMerge(table) {
  if (table.length > 0) {
    for (const i of table) {
      console.log(`${i} 开始转移---------------------`);
      const list = await mongo.collection(i).find({}).toArray();
      const devList = list.map(m => m.pro_dev_id);
      const devObj = {};
      if (devList.length) {
        const devStr = devList.join(',');
        const devL = await mysql.raw(`select id,systemid from pt_projectdev where id in (${devStr})`);
        const dev = devL[0];
        if (dev.length > 0) {
          for (const d of dev) {
            devObj[d.id] = d.systemid;
          }
        }
      }
      for (const i of list) {
        delete i._id;
        i.type = 5;
        await mongo.collection(`pt_task_log`).insert({
          increid: (await mongo.collection('pt_ids_list').findAndModify(
            {name: 'task_log'},
            [],
            {$inc: {'value': 1}},
            {new: true}
          )).value.value || 0,
          ...i,
          sid: devObj[i.pro_dev_id] || ''
        });
      }
      console.log('已完成----------');
    }
  }
}
async function artiMerge(table) {
  await mysql('pt_supervise_sheet_artificial').where('status', '=', 0).update({status: 1});// mysql现有数据 status = 0 的改为 status =1
  const allList = [];
  let n = 1;
  if (table.length > 0) {
    for (const i of table) {
      const list = await mongo.collection(i).find({}).toArray();
      for (const i of list) {
        delete i._id;
        delete i.id;
        i.status = 5;
        const mo = await mongo.collection(`pt_alarm_chain_log_${i.projectid}`).find({type: 9, artificial_id: i.id}).toArray();
        if (mo.length > 0) {
          const o = mo[0];
          i.handler = o.recorder;
          i.end_time = o.created_at;
        }
        if (/^\d+$/.test(i.created_at)) {
          i.created_at = timestampToTime(i.created_at);
        }
        if (/^\d+$/.test(i.updated_at)) {
          i.updated_at = timestampToTime(i.updated_at);
        }
        allList.push(i);
      }
    }
    console.log(`共 ${allList.length} 条记录 分100条每组进行...`);
    const smallArray = getSmallArr(allList, 100);
    for (const i of smallArray) {
      console.log(`正在执行第${n}组--------------------`);
      await mysql('pt_supervise_sheet_artificial').insert(i);
      n++;
    }
  }
}
main();
