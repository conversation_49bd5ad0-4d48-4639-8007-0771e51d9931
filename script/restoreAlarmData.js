
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

const mysqlP = require('promise-mysql');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function getMysqlConn() {
  const option = {
    host: config.mysql.host,
    port: config.mysql.port,
    user: config.mysql.username,
    password: config.mysql.password,
    database: config.mysql.database
  };
  const mysqlConn = await mysqlP.createConnection(option);
  return mysqlConn;
}

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}

async function getPorjectList() {
  const list = await mysql('pt_building_group').select('*');
  return list;
}

async function handleTotalData(year, timestamp) {
  const startDay = util.format(new Date(timestamp * 1000));
  const preFun = (val) => {
    if (val < 10) {
      return '0' + val;
    }
    return val;
  };
  // const startYear = new Date(timestamp * 1000).getFullYear();
  // const endYear = new Date().getFullYear();
  // const startMonth = preFun(new Date(timestamp * 1000).getMonth() + 1);
  // const endMonth = preFun(new Date().getMonth() + 1);

  for (let m = 1; m <= 12; m = m + 1) {
    const startMonth = preFun(m);
    const endMonth = preFun(m + 1);
    const start = `${year}-${startMonth}-01 00:00:00`;
    const end = `${year}-${endMonth}-01 00:00:00`;

    const condition = {year: year, month: m};
    const info = await mysql('pt_alarm_total').where(condition).first();
    if (_.isEmpty(info)) {
      await mysql('pt_alarm_total').insert({
        year: year,
        month: m,
        handled_fire: 0,
        handled_fault: 0,
        handled_broken: 0,
        handled_shield: 0,
        created_at: `${year}-${startMonth}-01 00:00:00`,
        updated_at: `${year}-${startMonth}-01 00:00:00`
      });
    }
    const fireTotal = await mysql('pt_tendency').sum('fire_handled_num as count').whereRaw(`day>=? and day<?`, [start, end]).first();
    const faultTotal = await mysql('pt_tendency').sum('danger_handled_num as count').whereRaw(`day>=? and day<?`, [start, end]).first();
    const brokenTotal = await mysql('pt_tendency').sum('broken_handled_num as count').whereRaw(`day>=? and day<?`, [start, end]).first();
    const shieldTotal = await mysql('pt_tendency').sum('shield_handled_num as count').whereRaw(`day>=? and day<?`, [start, end]).first();
    const updateData = {
      handled_fire: fireTotal.count || 0,
      handled_fault: faultTotal.count || 0,
      handled_broken: brokenTotal.count || 0,
      handled_shield: shieldTotal.count || 0
    };
    await mysql('pt_alarm_total').where(condition).update(updateData);
  }

}

async function saveTendency(startDay, v, updateData) {
  const condition = {pid: v.id, day: startDay};
  const data = {
    street_code: v.street_code,
    district_code: v.district_code,
    city_code: v.city_code,
    province_code: v.province_code,
    longitude: v.longitude,
    latitude: v.latitude
  };
  let total = 0;
  for (const k in updateData) {
    total = total + updateData[k];
  }
  console.log('total ------------- ', total)
  const info = await mysql('pt_tendency').where(condition).first();
  if (_.isEmpty(info) && total > 0) {
    const timestamp = Math.round(new Date(startDay).getTime() / 1000);
    data.uniqueinfo = `${v.id}_${timestamp}`;
    data.day = startDay;
    data.pid = v.id;
    data.fire_num = 0;
    data.fire_total = 0;
    data.fire_handled_num = 0;
    data.broken_num = 0;
    data.broken_total = 0;
    data.broken_handled_num = 0;
    data.danger_num = 0;
    data.danger_total = 0;
    data.danger_handled_num = 0;
    data.shield_num = 0;
    data.shield_total = 0;
    data.shield_handled_num = 0;
    data.supervision_num = 0;
    data.supervision_total = 0;
    data.supervision_handled_num = 0;
    data.sheet_total = 0;
    data.sheet_num = 0;
    data.sheet_handled_num = 0;
    data.created_at = startDay;
    data.updated_at = startDay;
    await mysql('pt_tendency').insert(data);
  }
  const newData = _.assign(data, updateData);
  await mysql('pt_tendency').where(condition).update(newData).limit(1);
}

async function handleData(timestamp, v) {
  const pid = v.id;
  const timestampEnd = timestamp + 86400;
  const startDay = util.format(new Date(timestamp * 1000));
  const endDay = util.format(new Date((timestamp + 86400) * 1000));
  const start = new Date(startDay);
  const end = new Date(endDay);
  console.log('startDay ------------- ', startDay)
  // console.log('endDay ------------- ', endDay)
  // console.log('start ------------- ', start)
  // console.log('end ------------- ', end)

  const FireCount = await mysql('pt_basic_event').whereRaw(`project_id=? and value=? and created_at>=? and created_at<?`, [pid, 23, startDay, endDay]).count('* as count');
  const handledFireCount = await mongo.db(config.mongo.database).collection(`pt_alarm_manage_fire_log_${pid}`).find({value: 23, created_at: {$gte: start, $lt: end}}).count();

  const brokenCount = await mysql('pt_basic_event').whereRaw(`project_id=? and value=? and created_at>=? and created_at<?`, [pid, 16, startDay, endDay]).count('* as count');
  const handledBrokenCount = await mongo.db(config.mongo.database).collection(`pt_alarm_manage_fire_log_${pid}`).find({value: 16, created_at: {$gte: start, $lt: end}}).count();

  const shieldCount = await mysql('pt_basic_event').whereRaw(`project_id=? and value=? and created_at>=? and created_at<?`, [pid, 24, startDay, endDay]).count('* as count');
  const handledShieldCount = await mongo.db(config.mongo.database).collection(`pt_alarm_manage_fire_log_${pid}`).find({value: 24, created_at: {$gte: start, $lt: end}}).count();

  const faultCount = await mysql('pt_basic_event').whereRaw(`project_id=? and created_at>=? and created_at<?`, [pid, startDay, endDay]).count('* as count');
  const handledFaultCount = await mongo.db(config.mongo.database).collection(`pt_alarm_fault_log_${pid}`).find({created_at: {$gte: start, $lt: end}}).count();

  const taskCount = await mysql('pt_task').whereRaw(`projectid=? and createtime>=? and createtime<?`, [pid, timestamp, timestampEnd]).count('* as count');
  const handledTaskCount = await mongo.db(config.mongo.database).collection(`pt_task_log_${pid}`).find({createtime: {$gte: timestamp, $lt: timestampEnd}}).count();

  const artificialCount = await mysql('pt_supervise_sheet_artificial').whereRaw(`building_group_id=? and created_at>=? and created_at<?`, [pid, startDay, endDay]).count('* as count');
  const handledArtificialCount = await mongo.db(config.mongo.database).collection(`pt_supervise_sheet_artificial_log_${pid}`).find({created_at: {$gte: startDay, $lt: endDay}}).count();

  const updatetendency = {
    fire_num: FireCount[0].count,
    fire_handled_num: handledFireCount,
    fire_total: FireCount[0].count + handledFireCount,

    broken_num: brokenCount[0].count,
    broken_handled_num: handledBrokenCount,
    broken_total: brokenCount[0].count + handledBrokenCount,

    shield_num: shieldCount[0].count,
    shield_handled_num: handledShieldCount,
    shield_total: shieldCount[0].count + handledShieldCount,

    danger_num: faultCount[0].count,
    danger_handled_num: handledFaultCount,
    danger_total: faultCount[0].count + handledFaultCount,

    supervision_num: taskCount[0].count,
    supervision_handled_num: handledTaskCount,
    supervision_total: taskCount[0].count + handledTaskCount,

    sheet_num: artificialCount[0].count,
    sheet_handled_num: handledArtificialCount,
    sheet_total: artificialCount[0].count + handledArtificialCount
  };
  // console.log(updatetendency);
  await saveTendency(startDay, v, updatetendency);
}

async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const day = '2019-12-12 12:12:12';
    const startDay = util.format(new Date(day), 'Y-m-d');
    const startTimestamp = Math.round(new Date(startDay).getTime() / 1000) - 3600 * 8;
    console.log(startTimestamp);

    const today = util.format(new Date(), 'Y-m-d');
    const todayTimestamp = Math.round(new Date(today).getTime() / 1000) - 3600 * 8;
    console.log(todayTimestamp);

    const list = await getPorjectList();
    for (let timestamp = startTimestamp; timestamp <= todayTimestamp; timestamp = timestamp + 86400) {
      for (const v of list) {
        await handleData(timestamp, v);
      }
    }
    await handleTotalData(2020, startTimestamp);
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
