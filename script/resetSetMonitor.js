const Knex = require('knex');
const config = require('../src/configs.js');

let mysql;

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}

async function main() {
  try {
    mysql = await getConn();
    const list = await mysql.raw(` select * from pt_projectdev where stop_status = 0 `);
    for (const i of list[0]) {
      let count = 0;
      const datas = await mysql.raw(` select * from pt_set_monitor_1 where pro_dev_id = ${i.id} `);
      if (datas[0].length > 1) {
        const nums = await mysql.raw(` delete from pt_set_monitor_1 where pro_dev_id = ${i.id} and pro_model_id != ${i.model_id} `)
        count += nums[0].affectedRows;
      }
      console.log('count==', count);
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
