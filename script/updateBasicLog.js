
const config = require('../src/configs.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const moment = require('moment');
moment.locale('zh');

let mongo;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function main() {
  try {
    mongo = await getMongo();
    const data = ['04', '05', '06', '07', '08', '09'];
    // const data = ['09'];
    for (const j of data) {
      let beginTimes = '2020-04-01 00:00:00';
      beginTimes = moment(beginTimes).set('month', parseInt(j) - 1);
      let beginTime;
      let endTime;
      let res;
      let ret;
      for (let k = 1; k <= 45; k++) {
        if (k === 1) {
          beginTime = moment(beginTimes).subtract(1, 'days');
        } else {
          beginTime = moment(beginTimes);
        }
        endTime = moment(beginTimes).add(1, 'days');
        beginTimes = endTime;
        res = await mongo.collection(`pt_basic_event_log_2020${j}`).find({created_at: {$gte: new Date(beginTime), $lt: new Date(endTime)}}).toArray();
        if (res.length > 0) {
          for (const i of res) {
            if (!i.reason_time) {
              // reason_time 不存在
              ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                event_id: i.event_id
              }, {
                $set: {
                  'reason_time': 0
                }
              });
            } else {
              // 是date类型
              if (typeof i.reason_time !== 'number') {
                // console.log(i.reason_time);
                ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                  event_id: i.event_id
                }, {
                  $set: {
                    'reason_time': moment(i.reason_time).unix()
                  }
                });
              } else {
                if ((i.reason_time / 1000000000) > 10) {
                  ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                    event_id: i.event_id
                  }, {
                    $set: {
                      'reason_time': i.reason_time / 1000
                    }
                  });
                }
              }
            }
            if (!i.relieve_time) {
              ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                event_id: i.event_id
              }, {
                $set: {
                  'relieve_time': 0
                }
              });
            } else {
              if (typeof i.relieve_time !== 'number') {
                ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                  event_id: i.event_id
                }, {
                  $set: {
                    'relieve_time': moment(i.relieve_time).unix()
                  }
                });
              } else {
                if ((i.relieve_time / 1000000000) > 10) {
                  ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                    event_id: i.event_id
                  }, {
                    $set: {
                      'relieve_time': i.relieve_time / 1000
                    }
                  });
                }
              }
            }
            if (!i.created_at) {
              ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                event_id: i.event_id
              }, {
                $set: {
                  'created_at': 0
                }
              });
            } else {
              if (typeof i.created_at !== 'number') {
                ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                  event_id: i.event_id
                }, {
                  $set: {
                    'created_at': moment(i.created_at).unix()
                  }
                });
              } else {
                if ((i.created_at / 1000000000) > 10) {
                  ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                    event_id: i.event_id
                  }, {
                    $set: {
                      'created_at': i.created_at / 1000
                    }
                  });
                }
              }
            }
            if (parseInt(i.created_at) === 0) {
              ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                event_id: i.event_id
              }, {
                $set: {
                  'created_at': i.server_time
                }
              });
            }
            if (i.created_at && i.reason_time) {
              ret = await mongo.collection(`pt_basic_event_log_2020${j}`).updateMany({
                event_id: i.event_id
              }, {
                $set: {
                  'diff_time': parseInt(i.reason_time) - parseInt(i.created_at)
                }
              });
            }
            console.log('ret', 1);
          }
        }
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
