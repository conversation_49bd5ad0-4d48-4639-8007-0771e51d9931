
const config = require('../src/configs.js');
const Knex = require('knex');

let mysql;
// 检查人工督查单数据状态
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();

    const res = await mysql('pt_supervise_sheet_artificial').where({status: 0}).select();
    console.log(res.length);
    for (const i of res) {
      const basicEvent = await mysql('pt_basic_event').where({accurate_alarm_id: i.accurate_alarm_id}).select();
      const taskEvent = await mysql('pt_task').where({accurate_alarm_id: i.accurate_alarm_id}).select();
      // 如果不存在 就开始删除并移到log表中 同时需要修改 type的状态为5
      if (basicEvent.length === 0 || taskEvent.length === 0) {
        console.log(1111111)
        i.status = 5;
        // 删除task表中的数据
        await mysql('pt_supervise_sheet_artificial').where({accurate_alarm_id: i.accurate_alarm_id}).delete();
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
