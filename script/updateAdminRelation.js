
const config = require('../src/config.js');
const Knex = require('knex');

let mysql;

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    const res = await mysql.raw(` select * from pt_service_admin_relation `);
    for (const i of res[0]) {
      const bid = [];
      const buildingData = await mysql.raw(` select building_group_id from pt_map_manage_building_group where admin_id = ${i.sub_id} and status = 2 `);
      if (buildingData[0].length > 0) {
        for (const j of buildingData[0]) {
          bid.push(j.building_group_id);
        }
      }
      const sql = ` update pt_service_admin_relation set project_id = '${bid.join(',')}' where sub_id = ${i.sub_id} `;
      const ret = await mysql.raw(sql);
      console.log('ret成功了', ret.changedRows);
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
