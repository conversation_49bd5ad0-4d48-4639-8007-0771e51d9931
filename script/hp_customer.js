
const mysql = require('mysql2');
const monent = require('moment');

const connection = mysql.createConnection({
  port: 5060,
  // host: '***********',
  host: '***********',
  user: 'hp_xfzd',
  password: 'Hp_xfzd20200731',
  database: 'hp_xfzd'
});
const connectionhp = mysql.createConnection({
  port: 8306,
  host: '***********',
  user: 'root',
  password: 'goo.Happy123',
  database: 'hp_net119'
});

async function main() {
  try {
    let frontData = [];
    let localData = [];
    let updateDate = [];
    let localDataHp;
    let InSum = 0;
    let OutSum = 0;
    let totalSum = 0;
    let intsummaryDate = [];
    const nowData = monent().format('YYYY-MM-DD');
    const beginTime = monent().subtract(20, 'minutes').format('YYYY-MM-DD HH:mm:ss');
    const endTime = monent().subtract(10, 'minutes').format('YYYY-MM-DD HH:mm:ss');
    const beginTimes = monent().format('YYYY-MM-DD HH:00:00');
    const endTimes = monent().set('hours', monent(beginTimes).get('hours') + 1).format('YYYY-MM-DD HH:00:00');
    console.log('beginTime', beginTime);
    console.log('endTime', endTime);
    await connectionhp.promise().query(`delete from ywjk_passagerflow where time = '${nowData}' and spotid != 'YUYUANW'`).then(([rows, fields]) => {
      localData = rows;
    }).catch(console.log);
    console.log('nowData', nowData)
    await connection.promise().query(`select * from ywjk_passagerflow where time = '${nowData}'`).then(([rows, fields]) => {
      frontData = rows;
    }).catch(console.log);
    // 查询豫园外圈的数据
    await connection.promise().query(`select sum(InSum) as InSum, sum(OutSum) as OutSum from intsummary_base where createTime BETWEEN '${beginTimes}' and '${endTimes}'`).then(([rows, fields]) => {
      localDataHp = rows;
      InSum = localDataHp[0].InSum ? localDataHp[0].InSum : 0;
      OutSum = localDataHp[0].OutSum ? localDataHp[0].OutSum : 0;
      console.log('InSum', InSum);
      console.log('OutSum', OutSum);
    }).catch(console.log);
    // 新增

    console.log('frontData', frontData.length)
    // 循环前置机数据
    for (const i of frontData) {
      // 修改
      const modSql = 'INSERT INTO ywjk_passagerflow(spotid, updatetimer, totalnumber, realtimenumber, time, hours, increase) values(?, ?, ?, ?, ?, ?, ?)';
      const modSqlParams = [i.spotid, i.updatetimer, i.totalnumber, i.realtimenumber, i.time, i.hours, i.increase];
      await connectionhp.promise().query(modSql, modSqlParams, function(err, result) {
        if (err) {
          console.log('[INSERT ERROR] - ', err.message);
          return;
        }
        console.log(result.affectedRows);
      });
      console.log('---end----');
    }
    // 更新豫园外圈数据
    await connectionhp.promise().query(`select * from ywjk_passagerflow where spotid = 'YUYUANW' and time = '${nowData}'`).then(([rows, fields]) => {
      localData = rows;
    }).catch(console.log);

    if (localData.length === 0) {
      // 如果豫园外圈数据不存在则添加
      const addSqls = 'INSERT INTO ywjk_passagerflow(spotid, updatetimer, totalnumber, realtimenumber, time, hours, increase) values(?, ?, ?, ?, ?, ?, ?)';
      const addSqlParamss = ['YUYUANW', monent().format('YYYY-MM-DD HH:mm:ss'), 0, 0, monent().format('YYYY-MM-DD'), '{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":0}', '{"0":0,"1":0,"2":0,"3":0,"4":0,"5":0,"6":0,"7":0,"8":0,"9":0,"10":0,"11":0,"12":0,"13":0,"14":0,"15":0,"16":0,"17":0,"18":0,"19":0,"20":0,"21":0,"22":0,"23":0}'];
      await connectionhp.promise().query(addSqls, addSqlParamss, (err, res) => {
        if (err) {
          console.log('[INSERT ERROR] - ', err.message);
        }
        console.log(res);
      });
    } else {
      for (const j of localData) {
        console.log(j);
        if (j.spotid === 'YUYUANW') {
          const hoursData = JSON.parse(j.hours);
          for (const k in hoursData) {
            if (parseInt(k) === monent().get('hours')) {
              hoursData[k] = InSum;
            }
            totalSum += parseInt(hoursData[k]);
            console.log('totalSum', totalSum);
          }
          j.updatetimer = monent().format('YYYY-MM-DD HH:mm:ss');
          j.totalnumber = totalSum;
          j.realtimenumber = InSum;
          j.hours = JSON.stringify(hoursData);
          j.increase = JSON.stringify(hoursData);

          console.log('进入了修改');
          const modSql = 'UPDATE ywjk_passagerflow SET updatetimer = ?,totalnumber = ?, realtimenumber = ?, hours = ?, increase = ? WHERE spotid = ? and time = ?';
          const modSqlParams = [j.updatetimer, j.totalnumber, j.realtimenumber, j.hours, j.increase, j.spotid, j.time];
          console.log('modSqlParams', modSqlParams);
          await connectionhp.promise().query(modSql, modSqlParams, function(err, result) {
            if (err) {
              console.log('[UPDATE ERROR] - ', err.message);
              return;
            }
            console.log(result.affectedRows);
          });
        }
      }
    }
    // 获取intsummary_base 豫园内圈数据
    await connection.promise().query(`select * from intsummary_base where createTime BETWEEN '${beginTime}' and '${endTime}'`).then(([rows, fields]) => {
      updateDate = rows;
    }).catch(console.log);

    if (updateDate.length > 0) {
      for (const i of updateDate) {
        const delSql = 'delete from intsummary_base where id = ? ';
        const delSqlParams = [i.id];
        await connectionhp.promise().query(delSql, delSqlParams, (err, res) => {
          if (err) {
            console.log('[DELETE ERROR] - ', err.message);
          }
          console.log(res);
        });
        const addSql = 'INSERT INTO intsummary_base(SYNC_Date, CityId, CountDate, createTime, CustomerCode, id, InSum, jhpt_delete, jhpt_update_time, ModifyTime, OutSum, SiteKey, SiteName, SiteType, SiteTypeName, updateTime) values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
        const addSqlParams = [i.SYNC_Date, i.CityId, i.CountDate, i.createTime, i.CustomerCode, i.id, i.InSum, i.jhpt_delete, i.jhpt_update_time, i.ModifyTime, i.OutSum, i.SiteKey, i.SiteName, i.SiteKey, i.SiteTypeName, i.updateTime];
        await connectionhp.promise().query(addSql, addSqlParams, (err, res) => {
          if (err) {
            console.log('[INSERT ERROR] - ', err.message);
          }
          console.log(res);
        });
        console.log('---end----');
      }
    }
    // 获取intsummary_day 豫园客流数据
    await connection.promise().query(`select * from intsummary_day where createTime BETWEEN '${beginTime}' and '${endTime}'`).then(([rows, fields]) => {
      intsummaryDate = rows;
    }).catch(console.log);
    console.log('intsummaryDate', intsummaryDate.length)
    if (intsummaryDate.length > 0) {
      for (const i of intsummaryDate) {
        const addSql = 'INSERT INTO intsummary_day(SYNC_Date, CityId, CountDate, createTime, CustomerCode, id, InSum, jhpt_delete, jhpt_update_time, ModifyTime, OutSum, SiteKey, SiteName, SiteType, SiteTypeName, updateTime) values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
        const addSqlParams = [i.SYNC_Date, i.CityId, i.CountDate, i.createTime, i.CustomerCode, i.id, i.InSum, i.jhpt_delete, i.jhpt_update_time, i.ModifyTime, i.OutSum, i.SiteKey, i.SiteName, i.SiteKey, i.SiteTypeName, i.updateTime];
        await connectionhp.promise().query(addSql, addSqlParams, (err, res) => {
          if (err) {
            console.log('[INSERT ERROR] - ', err.message);
          }
          console.log(res);
        });
        console.log('---end----');
      }
    }
    // connection.end()
    // connectionhp.end();
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

exports.main = main;
