
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    // const arr = [
    //   {name: '主机火警'},
    //   {name: '精准火警'},
    //   {name: '点位故障'},
    //   {name: '回路故障'},
    //   {name: '主电故障'},
    //   {name: '备电故障'},
    //   {name: '通讯故障'},
    //   {name: '其他'},
    //   {name: '设备故障'},
    //   {name: '点位屏蔽'},
    //   {name: '设备屏蔽'},
    //   {name: '点位启动'},
    //   {name: '设备启动'},
    //   {name: '延时'},
    //   {name: '电弧故障'},
    //   {name: '剩余电流值过大（漏电)'},
    //   {name: '线缆温度过高'},
    //   {name: '可燃气体泄露'},
    //   {name: '设备断电'},
    //   {name: '水压不足'},
    //   {name: '液位不足'},
    //   {name: '风压差不符'},
    //   {name: '流量不足'},
    //   {name: '手动状态'},
    //   {name: '温度过高'},
    //   {name: '缺电'},
    //   {name: '信号差'},
    //   {name: '电池电量过低'},
    //   {name: '设备报警'},
    //   {name: '点位监管'},
    //   {name: '点位反馈'},
    //   {name: '电源故障'}
    // ];
    // await mysql('pt_base_event_type').insert(arr);

    const arr2 = [
      {'attr_name': '开启/关闭', 'attr_id': 1, 'base_event_id': '13'},
      {'attr_name': '电流值', 'attr_id': 3, 'base_event_id': '16'},
      {'attr_name': '通电/断电', 'attr_id': 4, 'base_event_id': '19'},
      {'attr_name': '温度值', 'attr_id': 5, 'base_event_id': '25'},
      {'attr_name': '主电/备电', 'attr_id': 6, 'base_event_id': '5,6'},
      {'attr_name': '压力值', 'attr_id': 7, 'base_event_id': '20'},
      {'attr_name': '液位值', 'attr_id': 8, 'base_event_id': '21'},
      {'attr_name': '手动/自动', 'attr_id': 10, 'base_event_id': '24'},
      {'attr_name': '启动/停止', 'attr_id': 65, 'base_event_id': '13'},
      {'attr_name': '正常/故障', 'attr_id': 66, 'base_event_id': '9'},
      {'attr_name': '正常/缺电', 'attr_id': 69, 'base_event_id': '26'},
      {'attr_name': '动作/停止', 'attr_id': 70, 'base_event_id': '13'},
      {'attr_name': '正常/故障/维修', 'attr_id': 72, 'base_event_id': '9'},
      {'attr_name': '正常/倾倒/撞击', 'attr_id': 74, 'base_event_id': ''},
      {'attr_name': '故障/正常', 'attr_id': 78, 'base_event_id': '9'},
      {'attr_name': '屏蔽/取消', 'attr_id': 79, 'base_event_id': '11'},
      {'attr_name': '电池电量值', 'attr_id': 83, 'base_event_id': '28'},
      {'attr_name': '网络信号值', 'attr_id': 84, 'base_event_id': '27'},
      {'attr_name': '数值型', 'attr_id': 85, 'base_event_id': ''},
      {'attr_name': '正常/报警/故障', 'attr_id': 94, 'base_event_id': '29,9'},
      {'attr_name': '报警/正常', 'attr_id': 101, 'base_event_id': '29'},
      {'attr_name': '通讯故障/回路故障', 'attr_id': 108, 'base_event_id': '7,4'},
      {'attr_name': '正常\\火警\\故障\\屏蔽\\监管\\停止\\启动\\反馈\\延时\\电源正常\\电源故障', 'attr_id': 113, 'base_event_id': '1,3,10,30,12,31,14,32'},
      {'attr_name': '在线/离线', 'attr_id': 116, 'base_event_id': ''}

    ];
    await mysql('pt_map_attribute_base_event').insert(arr2);

    process.exit();
  } catch (err) {
    console.log(err);
  }
}
main();
