
const config = require('../src/configs.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const moment = require('moment');
moment.locale('zh');

let mongo;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function main() {
  try {
    mongo = await getMongo();
    const data = ['04', '05', '06', '07', '08', '09', '10', '11'];
    let res;
    let count = 0;
    let times = 0; // 小于30分钟
    let times2 = 0; // 小于2小时
    let times3 = 0; // 小于6小时
    let times4 = 0; // 大于6小于24小时
    let times5 = 0; // 大于24小时
    let time = 0;
    for (const j of data) {
      res = await mongo.collection(`pt_basic_event_log_2020${j}`).find({diff_time: {$gte: 0}}).toArray();
      count += res.length;
      for (const i of res) {
        const diffTime = parseInt(i.diff_time);
        if (diffTime < 1800) {
          times += 1;
          time += diffTime;
        } else if (diffTime > 1800 && diffTime < 7200) {
          times2 += 1;
          time += diffTime;
        } else if (diffTime > 7200 && diffTime < 21600) {
          times3 += 1;
          time += diffTime;
        } else if (diffTime > 21600 && diffTime < 86400) {
          times4 += 1;
          time += diffTime;
        } else if (diffTime >= 86400) {
          times5 += 1;
        }
      }
    }
    console.log(count);
    console.log(time)
    // console.log(times / count);
    // console.log(times2 / count);
    // console.log(times3 / count);
    // console.log(times4 / count);
    // console.log(times5 / count);
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
