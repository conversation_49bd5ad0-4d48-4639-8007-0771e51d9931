CREATE VIEW v2_view_drawing_catalog AS
-- Community images
SELECT 
    c.id AS communityId,
    NULL AS buildingId,
    'generalPlan' AS imageType,
    '总平图' AS imageTypeName,
    c.generalPlan AS imageUrl,
    NULL AS description
FROM
    v2_community c
WHERE
    c.generalPlan IS NOT NULL
UNION ALL
SELECT 
    c.id AS communityId,
    NULL AS buildingId,
    'adjacentEastImages' AS imageType,
    '东毗邻情况图片' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(c.adjacentEastImages, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    c.adjacentEastDesc AS description
FROM
    v2_community c
    JOIN v2_numbers ON CHAR_LENGTH(c.adjacentEastImages)
      -CHAR_LENGTH(REPLACE(c.adjacentEastImages, ',', ''))>=v2_numbers.n-1
WHERE
    c.adjacentEastImages IS NOT NULL
UNION ALL
SELECT 
    c.id AS communityId,
    NULL AS buildingId,
    'adjacentSouthImages' AS imageType,
    '南毗邻情况图片' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(c.adjacentSouthImages, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    c.adjacentSouthDesc AS description
FROM
    v2_community c
    JOIN v2_numbers ON CHAR_LENGTH(c.adjacentSouthImages)
      -CHAR_LENGTH(REPLACE(c.adjacentSouthImages, ',', ''))>=v2_numbers.n-1
WHERE
    c.adjacentSouthImages IS NOT NULL
UNION ALL
SELECT 
    c.id AS communityId,
    NULL AS buildingId,
    'adjacentWestImages' AS imageType,
    '西毗邻情况图片' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(c.adjacentWestImages, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    c.adjacentWestDesc AS description
FROM
    v2_community c
    JOIN v2_numbers ON CHAR_LENGTH(c.adjacentWestImages)
      -CHAR_LENGTH(REPLACE(c.adjacentWestImages, ',', ''))>=v2_numbers.n-1
WHERE
    c.adjacentWestImages IS NOT NULL
UNION ALL
SELECT 
    c.id AS communityId,
    NULL AS buildingId,
    'adjacentNorthImages' AS imageType,
    '北毗邻情况图片' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(c.adjacentNorthImages, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    c.adjacentNorthDesc AS description
FROM
    v2_community c
    JOIN v2_numbers ON CHAR_LENGTH(c.adjacentNorthImages)
      -CHAR_LENGTH(REPLACE(c.adjacentNorthImages, ',', ''))>=v2_numbers.n-1
WHERE
    c.adjacentNorthImages IS NOT NULL
-- Building images
UNION ALL
SELECT 
    b.communityId AS communityId,
    b.id AS buildingId,
    'standardFloorPlan' AS imageType,
    '标准层平面图' AS imageTypeName,
    b.standardFloorPlan AS imageUrl,
    NULL AS description
FROM
    v2_building b
WHERE
    b.standardFloorPlan IS NOT NULL
UNION ALL
SELECT 
    b.communityId AS communityId,
    b.id AS buildingId,
    'overheadView' AS imageType,
    '俯视图' AS imageTypeName,
    b.overheadView AS imageUrl,
    NULL AS description
FROM
    v2_building b
WHERE
    b.overheadView IS NOT NULL
UNION ALL
SELECT 
    b.communityId AS communityId,
    b.id AS buildingId,
    'surroundingImagesEast' AS imageType,
    '楼栋周边图片：东' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(b.surroundingImagesEast, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    b.surroundingImagesEastDescription AS description
FROM
    v2_building b
    JOIN v2_numbers ON CHAR_LENGTH(b.surroundingImagesEast)
      -CHAR_LENGTH(REPLACE(b.surroundingImagesEast, ',', ''))>=v2_numbers.n-1
WHERE
    b.surroundingImagesEast IS NOT NULL
UNION ALL
SELECT 
    b.communityId AS communityId,
    b.id AS buildingId,
    'surroundingImagesSouth' AS imageType,
    '楼栋周边图片：南' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(b.surroundingImagesSouth, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    b.surroundingImagesSouthDescription AS description
FROM
    v2_building b
    JOIN v2_numbers ON CHAR_LENGTH(b.surroundingImagesSouth)
      -CHAR_LENGTH(REPLACE(b.surroundingImagesSouth, ',', ''))>=v2_numbers.n-1
WHERE
    b.surroundingImagesSouth IS NOT NULL
UNION ALL
SELECT 
    b.communityId AS communityId,
    b.id AS buildingId,
    'surroundingImagesWest' AS imageType,
    '楼栋周边图片：西' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(b.surroundingImagesWest, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    b.surroundingImagesWestDescription AS description
FROM
    v2_building b
    JOIN v2_numbers ON CHAR_LENGTH(b.surroundingImagesWest)
      -CHAR_LENGTH(REPLACE(b.surroundingImagesWest, ',', ''))>=v2_numbers.n-1
WHERE
    b.surroundingImagesWest IS NOT NULL
UNION ALL
SELECT 
    b.communityId AS communityId,
    b.id AS buildingId,
    'surroundingImagesNorth' AS imageType,
    '楼栋周边图片：北' AS imageTypeName,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(b.surroundingImagesNorth, ',', v2_numbers.n), ',', -1)) AS imageUrl,
    b.surroundingImagesNorthDescription AS description
FROM
    v2_building b
    JOIN v2_numbers ON CHAR_LENGTH(b.surroundingImagesNorth)
      -CHAR_LENGTH(REPLACE(b.surroundingImagesNorth, ',', ''))>=v2_numbers.n-1
WHERE
    b.surroundingImagesNorth IS NOT NULL;