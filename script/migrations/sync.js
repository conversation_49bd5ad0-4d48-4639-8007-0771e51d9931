const sequelize = require('../../src/config/sequelize');
const { Community, Organization, FireOrganization, Building, KeyLocation, CommunityOrganization, Resident, ResidentKeyPerson, ResidentHouseType, ResidentRentalInfo, FireStationVehicleDutyRecord } = require('../../src/model/v2/associations');

async function syncDatabase() {
    try {
        await sequelize.authenticate();
        console.log('Connection has been established successfully.');

        // 同步模型到数据库
        await Community.sync({ alter: true });
        console.log('Community model was synchronized successfully.');

        await Organization.sync({ alter: true });
        console.log('Organization model was synchronized successfully.');

        await FireOrganization.sync({ alter: true });
        console.log('FireOrganization model was synchronized successfully.');

        await Building.sync({ alter: true });
        console.log('Building model was synchronized successfully.');

        await KeyLocation.sync({ alter: true });
        console.log('KeyLocation model was synchronized successfully.');

        await CommunityOrganization.sync({ alter: true });
        console.log('CommunityOrganization model was synchronized successfully.');

        await Resident.sync({ alter: true });
        console.log('Resident model was synchronized successfully.');

        // 用 sql 创建，不需要同步
        // CREATE TABLE`v2_community_fire_organization`(
        //     `communityId` INT NOT NULL,
        //     `fireOrganizationId` INT NOT NULL,
        //     PRIMARY KEY(`communityId`, `fireOrganizationId`),
        //     CONSTRAINT`idx_cforg_ids` UNIQUE(`communityId`, `fireOrganizationId`)
        // ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '小区与消防组织的关联表';
        // await CommunityFireOrganization.sync({ alter: true });
        // console.log('CommunityFireOrganization model was synchronized successfully.');

        await ResidentKeyPerson.sync({ alter: true });
        console.log('ResidentKeyPerson model was synchronized successfully.');

        await ResidentHouseType.sync({ alter: true });
        console.log('ResidentHouseType model was synchronized successfully.');

        await ResidentRentalInfo.sync({ alter: true });
        console.log('ResidentRentalInfo model was synchronized successfully.');

        await FireStationVehicleDutyRecord.sync({ alter: true });
        console.log('FireStationVehicleDutyRecord model was synchronized successfully.');

        process.exit();
    } catch (error) {
        console.error('Unable to connect to the database:', error);
        process.exit(1);
    }
}

syncDatabase();