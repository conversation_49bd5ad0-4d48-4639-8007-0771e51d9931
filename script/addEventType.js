
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}

async function addEventType() {
  console.log('开始修复，获取数据中请稍后-----------');
  const fireli = await mysql.raw(`select *,b.alert_id,b.isalone from pt_basic_event as a left join (select event_id,alert_id,isalone from pt_alarm_manage_fire ) as b on a.event_id = b.event_id where a.event_type IS NULL and a.position_index = 1 `);
  const faultli = await mysql.raw(`select *,b.alert_id,b.isalone from pt_basic_event as a left join (select event_id,alert_id,isalone from pt_alarm_fault ) as b on a.event_id = b.event_id where a.event_type IS NULL and a.position_index = 0 `);
  const listA = fireli[0];
  const listF = faultli[0];
  const list = [...listA, ...listF];
  const modelEv = await mysql('pt_devmodel_attribute_value').select('*');
  const deviceEv = await mysql('pt_project_dev_alarm_rule').select('*');
  const modelObj = {};
  const deviceObj = {};
  for (const i of modelEv) {
    modelObj[i.id] = i.basic_type;
  }
  for (const i of deviceEv) {
    deviceObj[i.id] = i.basic_type;
  }
  let nu = 0;
  let b = 1;
  for (const n of list) {
    const obj = {};
    if (n.isalone) {
      obj.event_type = deviceObj[n.alert_id] || 0;
    } else {
      obj.event_type = modelObj[n.alert_id] || 0;
    }
    if (obj.event_type >= 0) {
      nu++;
      if (parseInt(nu / 100) > b) {
        b = parseInt(nu / 100);
        console.log(`已修复${nu} / ${list.length}---------------------------`);
      }
      await mysql('pt_basic_event').where('id', '=', n.id).update(obj);
      await mysql('pt_task').where('supervise_id', '=', n.supervise_id).update(obj);
    }
  }
  console.log('修复完毕-----------------');
}

async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();

    await addEventType();
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
