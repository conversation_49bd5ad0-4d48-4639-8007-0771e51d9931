
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}



async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function getPorjectList() {
  const list = await mysql('pt_building_group').select('*');
  return list;
}

async function fixMongoSid() {
  const list = await getPorjectList();
  let total = 0;
  for (const v of list) {
    //console.log('pid ------', v.id)
    const taskModel = mongo.collection(`pt_task_log_${v.id}`);
    const count = await taskModel.find({}).count();
    //console.log('fire list ---', fireList.length);
    if (count > 0) {
      console.log(`      ${count}           ${v.name} `)
    }
    total = total + count;
  }
  console.log('总共 ', total)
}

async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();

    await fixMongoSid()
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
