
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');
const Number = think.getNumber(new Date().getTime());
let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}



async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function getPorjectList() {
  const list = await mysql('pt_building_group').select('*');
  return list;
}


async function fixMysqlSid() {
  const fireList = await mysql('pt_basic_event').where({sid: 0}).select('*');
  if (fireList.length > 0) {
    for (const v of fireList) {
      const info = await mysql('pt_setmodel').where({id: v.pro_model_id}).first();
      await await mysql('pt_basic_event').where({event_id: v.event_id}).update({sid: 42, tid: info.type_id}).limit(1);
    }
  }

  const dangerList = await mysql('pt_basic_event').where({sid: 0}).select('*');
  if (dangerList.length > 0) {
    for (const v of dangerList) {
      const info = await mysql('pt_setmodel').where({id: v.pro_model_id}).first();
      await await mysql('pt_basic_event').where({event_id: v.event_id}).update({sid: info.system_id, tid: info.type_id}).limit(1);
    }
  }
}


async function fixMongoSid() {
  const list = await getPorjectList();
  for (const v of list) {
    console.log('pid ------', v.id)
    const dangerModel = mongo.collection(`basic_event_log_${Number}`);
    const fireModel = mongo.collection(`basic_event_log_${Number}`);
    const fireList = await fireModel.find({project_id: v.id}).toArray();
    if (fireList.length > 0) {
      for (const fire of fireList) {
        const info = await mysql('pt_setmodel').where({id: fire.pro_model_id}).first();
        await fireModel.updateOne({_id: fire._id}, {$set: {sid: 42, tid: info.type_id}});
      }
    }

    const dangerList = await dangerModel.find({project_id: v.id}).toArray();
    if (dangerList.length > 0) {
      for (const danger of dangerList) {
        const info = await mysql('pt_setmodel').where({id: danger.pro_model_id}).first();
        await dangerModel.updateOne({_id: danger._id}, {$set: {sid: info.system_id, tid: info.type_id}});
      }
    }
  }
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();

    await fixMysqlSid();
    await fixMongoSid()
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
