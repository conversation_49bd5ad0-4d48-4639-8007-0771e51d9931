
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mysql;


async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}


async function getCompanyList() {
  const list = await mysql('pt_company').select();
  return list;
}



async function handleData(v) {
  let info;
  if (parseInt(v.company_type) == 2) {
    const count = await mysql('pt_map_manage_building_group').where({company_id: v.id}).count('* as count');
    if (count[0].count) {
      info = await mysql('pt_company').select('b.*').joinRaw('c left join pt_map_manage_building_group bg on c.id=bg.company_id')
        .joinRaw('left join pt_building_group b on bg.building_group_id=b.id').whereRaw(`c.id=${v.id}`).limit(1).first();

      if (info.street_code) {
        const data = {
          street_code: info.street_code,
          district_code: info.district_code,
          city_code: info.city_code,
          province_code: info.province_code
        };
        await mysql('pt_company').where({id: v.id}).update(data)
      }
    }
  }
  if (parseInt(v.company_type) == 8) {
    const count = await mysql('pt_map_building_company_rent').where({company_id: v.id}).count('* as count');
    if (count[0].count) {
      info = await mysql('pt_company').select('b.*').joinRaw('c left join pt_map_building_company_rent bg on c.id=bg.company_id')
        .joinRaw('left join pt_building_group b on bg.building_group_id=b.id').whereRaw(`c.id=${v.id}`).limit(1).first();
      if (info.street_code) {
        const data = {
          street_code: info.street_code,
          district_code: info.district_code,
          city_code: info.city_code,
          province_code: info.province_code
        };
        await mysql('pt_company').where({id: v.id}).update(data)
      }
    }

  }
}


async function main() {
  try {
    mysql = await getConn();


    const list = await getCompanyList();

    for (const v of list) {
      await handleData(v);
    }

    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
