
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');
const request = require('request-promise');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const res = await mysql('pt_company').select();
    const typename = await mysql('pt_system_type').select();
    const event = await mysql('pt_basic_event_tpl').select();
    const re = {};
    const ret = {};
    const events = {};
    for (const i of res) {
      re[i.id] = i;
    }
    for (const i of typename) {
      ret[i.id] = i;
    }
    for (const i of event) {
      events[i.id] = i;
    }
    for (const i in re) {
      const res = await mongo.collection('pt_task_log').updateMany({
        company_id: re[i].id
      }, {
        $set: {
          'company_name': re[i].o_name
        }
      });
      console.log('执行完毕', res.result.ok);
    }
    for (const i in ret) {
      const res = await mongo.collection('pt_task_log').updateMany({
        sid: ret[i].id
      }, {
        $set: {
          'typename': ret[i].typename
        }
      });
      console.log('执行完毕', res);
    }
    for (const i in events) {
      const res = await mongo.collection('pt_task_log').updateMany({
        event_type: events[i].id
      }, {
        $set: {
          'msgs': events[i].name
        }
      });
      console.log('执行完毕', res.result.ok);
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
