
const config = require('../src/configs.js');
const Knex = require('knex');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;

let mysql;
let mongo;

// 修改报警事件已经填写了记录原因，电子督查单还未关闭
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const res = await mysql.raw(` select * from pt_task where projectid = 729 and createtime BETWEEN 1609948800 and 1617292800 `);
    console.log(res[0].length);
    for (const i of res[0]) {
      const basicInfo = await mysql('pt_basic_event').where({event_id: i.event_id}).select();
      i.type = 5;
      if (basicInfo.id) {
        basicInfo.status = 5;
        await mongo.collection(`pt_basic_event_log_202104`).insert(basicInfo);
        await mysql('pt_basic_event').where({id: basicInfo.id}).delete();
      }
      await mongo.collection(`pt_task_log`).insert(i);

      const sheetInfo = await mysql('pt_supervise_sheet_artificial').where({accurate_alarm_id: i.accurate_alarm_id}).select();
      if (sheetInfo.length === 0) {
        await mysql('pt_supervise_sheet_artificial').where({accurate_alarm_id: i.accurate_alarm_id}).update({status: 5});
      }
      console.log('1111', i.id);
      await mysql('pt_task').where({id: i.id}).delete();
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
