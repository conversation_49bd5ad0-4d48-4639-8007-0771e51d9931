
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    const connections = await mongo.listCollections().toArray();
    const arr = await mongo.collection('pt_ids_list').find({name: 'basic_event_log'}).toArray();
    if (!arr.length) {
      await mongo.collection('pt_ids_list').insert({name: 'basic_event_log', value: 0, createTime: Math.ceil(new Date().getTime() / 1000)});
    }
    const tableList = [];
    for (const i of connections) {
      if (i.name.includes('pt_alarm_manage_fire_log_')) {
        tableList.push(i.name);
      }
    }
    for (const i of connections) {
      if (i.name.includes('pt_alarm_fault_log_')) {
        tableList.push(i.name);
      }
    }
    await mysqlMerge('pt_alarm_fault', mysql);
    await mysqlMerge('pt_alarm_manage_fire', mysql);
    // await mongoMerge(tableList);
    process.exit();
  } catch (err) {
    console.log(err);
  }
}
function getSmallArr(array, subGroupLength) {
  let index = 0;
  const newArray = [];
  while (index < array.length) {
    newArray.push(array.slice(index, index += subGroupLength));
  }
  return newArray;
}

async function mysqlMerge(table, mysql) {
  console.log(`${table}开始迁移--------------------`);
  const delArr = [];
  const tableOld = await mysql.raw(`show columns from ${table} `);
  const tableNew = await mysql.raw('show columns from pt_basic_event');
  const told = tableOld[0].map(m => m.Field);
  told.push('building_id', 'floor_name', 'build_name');
  const tnew = tableNew[0].map(m => m.Field);
  const addArr = [];
  let n = 1;
  for (const i of told) {
    if (tnew.indexOf(i) < 0) {
      delArr.push(i);
    }
  }
  const list = await mysql.raw(`select a.*,b.o_name as company_name,c.username as service_company_name from ${table}  as a left join (select id as cid,o_name from pt_company) as b on a.company_id = b.cid left join (select id as sid,username from pt_service_admin) as c on a.service_company_id = c.sid`);
  // reason_status  = 0 的 status改为0，reason_status!= 0 改为2
  for (const l of list[0]) {
    for (const d of delArr) {
      if (d !== 'build_name' && d !== 'floor_nane' && d !== 'build_id') { delete l[d] };
    }
    l.building_id = l.build_id;
    l.floor_name = l.floor_nane;
    l.building_name = l.build_name;
    delete l.build_id;
    delete l.floor_nane;
    delete l.build_name;
    if (l.reason_status === 0) {
      l.status = 0;
    } else {
      l.status = 2;
    }
    addArr.push(l);
  };
  console.log(`共 ${addArr.length} 条记录`);
  const smallArray = getSmallArr(addArr, 100);
  for (const i of smallArray) {
    console.log(`正在执行第${n}组--------------------`);
    await mysql('pt_basic_event').insert(i);
    n++;
  }
}
function getNumber(timestamp) {
  const date = new Date(timestamp);// 时间戳为10位需*1000，时间戳为13位的话不需乘1000
  const Y = date.getFullYear();
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
  return Y + M;
}
async function mongoMerge(table) {
  const serviceAdminList = await mysql('pt_service_admin').select('*');
  const companyList = await mysql('pt_company').select('*');
  const modelEv = await mysql('pt_devmodel_attribute_value').select('*');
  const deviceEv = await mysql('pt_project_dev_alarm_rule').select('*');
  const modelObj = {};
  const deviceObj = {};
  for (const i of modelEv) {
    modelObj[i.id] = i.basic_type;
  }
  for (const i of deviceEv) {
    deviceObj[i.id] = i.basic_type;
  }
  const sobj = {};
  const cobj = {};
  for (const i of serviceAdminList) {
    sobj[i.id] = i.username;
  }
  for (const i of companyList) {
    cobj[i.id] = i.o_name;
  }
  const ilist = await mongo.collection(`pt_ids_list`).find({name: 'basic_event_log'}).toArray();
  let increid = 0;
  if (ilist.length) {
    increid = ilist[0].value;
  }
  if (table.length > 0) {
    for (const t of table) {
      const list = await mongo.collection(t).find({}).toArray();
      for (const li of list) {
        increid++;
        li.increid = increid;
      }
      console.log(`${t} 开始转移 当前集合共${list.length} 条记录`);
      let num = 1;
      const smallArray = getSmallArr(list, 100);
      for (const i of smallArray) {
        for (const n of i) {
          delete n._id;
          n.floor_name = n.floor_nane;
          n.building_id = n.build_id;
          n.building_name = n.build_name;
          if (n.isalone) {
            n.event_type = deviceObj[n.alert_id] || 0;
          } else {
            n.event_type = modelObj[n.alert_id] || 0;
          }
          n.status = 3;
          delete n.build_id;
          delete n.build_name;
          delete n.floor_nane;
        }
        await mongo.collection(`pt_basic_event_log_202004`).insert(i);
        console.log(`正在处理中第${num}组-----------------------`);
        num++;
      }
      console.log(`${t} 转移 已完成`);
    }
    await mongo.collection(`pt_ids_list`).update({name: 'basic_event_log'}, {$set: {value: increid}});
    console.log(`已经全部完成-----------------------------------`);
  }
}
main();
