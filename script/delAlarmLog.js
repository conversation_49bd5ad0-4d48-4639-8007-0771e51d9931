const mysql = require('promise-mysql');
const config = require('../src/config.js');
const util = require('../src/common/util');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;

let mongo;
let conn;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}

async function getConn() {
  const option = {
    host: config.mysql.host,
    port: config.mysql.port,
    user: config.mysql.username,
    password: config.mysql.password,
    database: config.mysql.database
  };
  const mysqlConn = await mysql.createConnection(option);
  return mysqlConn;
}

async function main() {
  try {
    conn = await getConn();
    mongo = await getMongo();

    const day = '2019-12-12 12:12:12';
    const createAt = new Date(day);

    const list = await conn.query(`select id from pt_building_group order by id asc`);
    for (const v of list) {
      console.log(`pid----------`, v.id);
      await conn.query(`delete from pt_tendency where created_at<='${day}'`);
      await conn.query(`delete from pt_basic_event where project_id=${v.id} and created_at<='${day}'`);
      await conn.query(`delete from pt_basic_event where project_id=${v.id} and created_at<='${day}'`);
      await mongo.db(config.mongo.database).collection(`pt_alarm_fault_log_${v.id}`).deleteMany({created_at: {$lte: createAt}});
      await mongo.db(config.mongo.database).collection(`pt_alarm_manage_fire_log_${v.id}`).deleteMany({created_at: {$lte: createAt}});
      await mongo.db(config.mongo.database).collection(`pt_alarm_fault_log_${v.id}`).deleteMany({created_at: null});
      await mongo.db(config.mongo.database).collection(`pt_alarm_manage_fire_log_${v.id}`).deleteMany({created_at: null});
    }

    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
