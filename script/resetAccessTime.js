// 修复建筑群接入时间和接入状态
const mysql = require('promise-mysql');
const config = require('../src/config.js');
const util = require('../src/common/util');
let conn;

async function getConn() {
  const option = {
    host: config.mysql.host,
    port: config.mysql.port,
    user: config.mysql.username,
    password: config.mysql.password,
    database: config.mysql.database
  };
  const mysqlConn = await mysql.createConnection(option);
  return mysqlConn;
}

async function main() {
  try {
    conn = await getConn();
    const list = await conn.query(`select * from pt_building_group`);
    for (const v of list) {
      if (v.accessstatus === 1 || v.accessstatus === 0) {
        const accessTime = await conn.query(`select createtime from pt_projectdev where pid=${v.id} order by createtime asc LIMIT 1`);
        if (accessTime.length && accessTime[0].createtime) {
          const startDay = util.format(new Date(accessTime[0].createtime * 1000));
          await conn.query(`update pt_building_group set accessstatus=1,accesstime='${startDay}' where id=${v.id}`);
        } else {
          await conn.query(`update pt_building_group set accessstatus=0 where id=${v.id}`);
        }
      }
    }
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

main();
// if (i.accessstatus === 1) {
//   const accessTime = await this.model('projectdev').alias('a').field('a.createtime').where({'pid': i.id}).order('a.createtime asc').find();
//   if (!think.isEmpty(accessTime)) {
//     i['accessTime'] = accessTime.createtime;
//   }
// }
