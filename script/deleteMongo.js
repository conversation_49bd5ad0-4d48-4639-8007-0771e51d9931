
const config = require('../src/config.js');
const mongodb = require('mongodb');
const MongoClient = mongodb.MongoClient;
const Knex = require('knex');
const util = require('../src/common/util');
const _ = require('underscore');

let mongo;
let mysql;

async function getMongo() {
  let url;
  if (config.mongo.username && config.mongo.authSource) {
    url = `mongodb://${config.mongo.username}:${config.mongo.password}@${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  } else if (config.mongo.authSource) {
    url = `mongodb://${config.mongo.host}:${config.mongo.port}/${config.mongo.database}?authSource=${config.mongo.authSource}`;
  }
  mongo = await MongoClient.connect(url);
  return mongo;
}
async function getConn() {
  const mysqlConn = Knex({
    connection: {
      port: config.mysql.port,
      host: config.mysql.host,
      user: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database
    },
    client: 'mysql2',
    pool: {min: 0, max: 10},
    debug: false
  });
  return mysqlConn;
}
async function main() {
  try {
    mysql = await getConn();
    mongo = await getMongo();
    await deleteM();
    process.exit();
  } catch (err) {
    console.log(err);
  }
}

async function deleteM(table) {
  const connections = await mongo.listCollections().toArray();
  const list = connections.map(m => m.name);
  for (let i = 1; i <= 13718; i++) {
    if (list.indexOf(`pt_basic_event_log_${i}`) > -1) {
      await mongo.collection(`pt_basic_event_log_${i}`).drop();
    }
    console.log(i);
  }
}
main();
