# FastNode 项目接口汇总文档

## 项目概述
- **框架**: ThinkJS
- **架构**: MVC模式，采用模块化设计
- **路由模式**: 约定路由 + 自定义路由
- **认证方式**: JWT Token + 签名验证

---

## 一、查询接口 (GET)

### 1.1 V2模块查询接口
```
GET /v2/community                           # 社区列表
GET /v2/community/:id                       # 获取单个社区
GET /v2/mobile/community                    # 移动端社区列表
GET /v2/mobile/community/:id                # 移动端社区详情
GET /v2/mobile/communityRisk/:id            # 移动端社区风险详情
GET /v2/organization                        # 组织列表
GET /v2/organization/:id                    # 获取单个组织
GET /v2/building                            # 建筑列表
GET /v2/building/:id                        # 获取单个建筑
GET /v2/buildingFloor                       # 建筑楼层列表
GET /v2/key_location                        # 重点部位列表
GET /v2/key_location/:id                    # 获取单个重点部位
GET /v2/fire_organization                   # 消防组织列表
GET /v2/fire_organization/:id               # 获取单个消防组织
GET /v2/resident                            # 人员列表
GET /v2/resident/:id                        # 获取单个人员
GET /v2/drawing-catalog                     # 图纸目录列表
GET /v2/real_time_police                    # 实时警情
GET /v2/fire_station_vehicle_duty_record    # 消防站车辆执勤记录列表
GET /v2/fire_station_vehicle_duty_record/:id # 获取单条执勤记录
GET /v2/get_fire_station_vehicle_duty_record_group # 执勤记录分组
```

### 1.2 Admin模块查询接口
```
GET /admin/[controller]/list                # 管理后台列表查询(通用)
GET /admin/map/total                        # 地图区域总数统计
GET /admin/firehydrant/searchtype           # 消火栓数据词典
GET /admin/map/tracCar/getstatList          # 车辆统计列表
GET /admin/map/tracCar/getDeviceInfo        # 获取车辆信息
GET /admin/menu/list                        # 所有菜单按钮接口
GET /admin/menu/top                         # 顶部菜单按钮接口
GET /admin/rolemanager/list                 # 角色管理列表
GET /admin/rolemanager/roleList             # 角色列表
GET /admin/company/infos                    # 公司信息
GET /admin/law/list                         # 法规列表
GET /admin/law/detailed                     # 法规详细内容
GET /admin/testplan/listData                # 测试计划列表
```

### 1.3 Application模块查询接口
```
GET /application/index/number               # 角标数字
GET /application/user/user/getuser          # 获取用户信息
GET /application/user/user/projectuser      # 项目用户列表
GET /application/user/user/codes            # 获取验证码
GET /application/project/select             # 项目选择
GET /application/project/alarmlist          # 报警列表
GET /application/project/projectreport      # 项目报表
GET /application/project/monthreport        # 月度报表
GET /application/project/taskcenter         # 任务中心统计
GET /application/project/alarmmsg           # 实时报警消息
GET /application/project/alarmmsgRg         # 监管报警消息
GET /application/projectdev/nfcselect       # NFC设备选择
GET /application/rtdetection/systemcard     # 系统卡片
GET /application/monitor/alarmHostList      # 报警主机列表
GET /application/home/<USER>/uavinfo         # 无人机信息
```

### 1.4 Frontend模块查询接口
```
GET /frontend/project/list                  # 前端项目列表
GET /frontend/testplan/listData             # 前端测试计划列表
GET /frontend/autoCheck/getList             # 自主验收列表
GET /frontend/removeshield/torelievelist    # 屏蔽设备待解除列表
GET /frontend/file/upload                   # 前端文件上传页面
```

### 1.5 小程序模块查询接口
```
GET /miniprogram/rota/list                  # 小程序轮值列表
GET /miniprogram/rota/street                # 小程序街道列表
GET /miniprogram/buildingmanage/contralRoom # 建筑群房间信息
GET /miniprogram/buildingmanage/findByAddressByPid # 根据pid查询地址
```

### 1.6 扩展模块查询接口
```
GET /ext/emanage/logistics/costType/list    # 费用类型列表
GET /ext/emanage/logistics/costType/deepSearch # 父级树形列表
GET /ext/emini/petition/index/queryAll      # 查看所有人的信访记录
```

### 1.7 文件相关查询接口
```
GET /file/getfile                           # 获取文件
GET /file/getfilegroup                      # 获取文件组
GET /common/file/getfile                    # 获取文件(通用)
```

### 1.8 通用查询接口
```
GET /wx                                     # 微信消息接收
GET /common/basic/cycle                     # 获取巡查周期选项
GET /common/test/excel                      # Excel测试
GET /common/test/syslist                    # 系统列表测试
GET /beian/area/getname                     # 获取区域名称
GET /beian/area/getcode                     # 获取区域编码
GET /beian/company/getInfo                  # 获取公司信息
GET /api/route                              # API测试接口
```

---

## 二、新增接口 (POST)

### 2.1 基础新增接口
```
POST /[controller]/add                      # 添加数据(所有模块通用)
POST /[controller]/import                   # 导入数据(所有模块通用)
```

### 2.2 V2模块新增接口
```
POST /v2/community                          # 新增社区
POST /v2/organization                       # 新增组织
POST /v2/building                           # 新增建筑
POST /v2/key_location                       # 新增重点部位
POST /v2/fire_organization                  # 新增消防组织
POST /v2/resident                           # 新增人员
POST /v2/resident/import-excel              # 批量导入人员(Excel)
POST /v2/fire_station_vehicle_duty_record   # 新增执勤记录
```

### 2.3 Admin模块新增接口
```
POST /admin/[controller]/add                # 管理后台添加数据(通用)
POST /admin/testplan/add                    # 添加测试计划
```

### 2.4 Application模块新增接口
```
POST /application/home/<USER>/uavReport      # 无人机数据上报
POST /application/alarm/other/sendrepair    # 发送维修
POST /application/alarm/other/addmanyremarks # 批量添加备注
```

### 2.5 Frontend模块新增接口
```
POST /frontend/project/addproject           # 添加前端项目
POST /frontend/testplan/add                 # 添加前端测试计划
```

### 2.6 文件上传接口
```
POST /file/upload                           # 文件上传
POST /frontend/file/upload                  # 前端文件上传
POST /common/file/upload                    # 通用文件上传
```

### 2.7 用户认证接口
```
POST /application/user/user/logout          # 用户退出登录
POST /application/user/user/checkcode       # 验证验证码
POST /application/user/user/checkPhone      # 验证手机号
POST /wx                                    # 微信消息接收(POST)
```

---

## 三、修改接口 (PUT)

### 3.1 基础修改接口
```
PUT /[controller]/edit                      # 修改数据(所有模块通用)
```

### 3.2 V2模块修改接口
```
PUT /v2/community/:id                       # 修改社区
PUT /v2/organization/:id                    # 修改组织
PUT /v2/building/:id                        # 修改建筑
PUT /v2/key_location/:id                    # 修改重点部位
PUT /v2/fire_organization/:id               # 修改消防组织
PUT /v2/resident/:id                        # 修改人员
PUT /v2/fire_station_vehicle_duty_record/:id # 修改执勤记录
```

### 3.3 Admin模块修改接口
```
PUT /admin/[controller]/edit                # 管理后台修改数据(通用)
```

---

## 四、删除接口 (DELETE)

### 4.1 基础删除接口
```
DELETE /[controller]/delete                 # 删除数据(所有模块通用)
```

### 4.2 V2模块删除接口
```
DELETE /v2/community/:id                    # 删除社区
DELETE /v2/organization/:id                 # 删除组织
DELETE /v2/building/:id                     # 删除建筑
DELETE /v2/key_location/:id                 # 删除重点部位
DELETE /v2/fire_organization/:id            # 删除消防组织
DELETE /v2/resident/:id                     # 删除人员
DELETE /v2/fire_station_vehicle_duty_record/:id # 删除执勤记录
```

### 4.3 Admin模块删除接口
```
DELETE /admin/[controller]/delete           # 管理后台删除数据(通用)
```

### 4.4 文件删除接口
```
POST /file/delfile                          # 删除文件
POST /frontend/file/delfile                 # 前端删除文件
POST /common/file/delfile                   # 通用删除文件
```

---

## 路由规则说明

1. **约定路由**: `/{module}/{controller}/{action}`
2. **RESTful路由**: 
   - GET `/{resource}` - 列表
   - GET `/{resource}/:id` - 详情
   - POST `/{resource}` - 新增
   - PUT `/{resource}/:id` - 修改
   - DELETE `/{resource}/:id` - 删除

3. **模块前缀**:
   - `/admin/*` - 管理后台
   - `/v2/*` - API v2版本
   - `/application/*` - 应用端
   - `/frontend/*` - 前端
   - `/common/*` - 通用功能
   - `/miniprogram/*` - 小程序
   - `/ext/*` - 扩展模块
   - `/weixin/*` - 微信相关

---

## 统计汇总

- **查询接口**: 约60个
- **新增接口**: 约25个  
- **修改接口**: 约15个
- **删除接口**: 约15个
- **总计**: 约115个接口

*注：实际接口数量可能更多，因为很多控制器使用了通用的增删改查方法*
