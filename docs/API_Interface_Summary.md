# FastNode 项目接口汇总文档

## 项目概述
- **框架**: ThinkJS
- **架构**: MVC模式，采用模块化设计
- **路由模式**: 约定路由 + 自定义路由
- **认证方式**: JWT Token + 签名验证

---

## 一、查询接口 (GET)

```
GET /v2/community                           # 社区列表
GET /v2/community/:id                       # 获取单个社区
GET /v2/mobile/community                    # 移动端社区列表
GET /v2/mobile/community/:id                # 移动端社区详情
GET /v2/mobile/communityRisk/:id            # 移动端社区风险详情
GET /v2/organization                        # 组织列表
GET /v2/organization/:id                    # 获取单个组织
GET /v2/building                            # 建筑列表
GET /v2/building/:id                        # 获取单个建筑
GET /v2/buildingFloor                       # 建筑楼层列表
GET /v2/key_location                        # 重点部位列表
GET /v2/key_location/:id                    # 获取单个重点部位
GET /v2/fire_organization                   # 消防组织列表
GET /v2/fire_organization/:id               # 获取单个消防组织
GET /v2/resident                            # 人员列表
GET /v2/resident/:id                        # 获取单个人员
GET /v2/drawing-catalog                     # 图纸目录列表
GET /v2/real_time_police                    # 实时警情
GET /v2/fire_station_vehicle_duty_record    # 消防站车辆执勤记录列表
GET /v2/fire_station_vehicle_duty_record/:id # 获取单条执勤记录
GET /v2/get_fire_station_vehicle_duty_record_group # 执勤记录分组

GET /admin/activity/list                    # 活动列表
GET /admin/activityperson/list              # 活动人员列表
GET /admin/alarmInformationRecord/list      # 报警信息记录列表
GET /admin/alarmTrend/list                  # 报警趋势列表
GET /admin/alarmfault/list                  # 报警故障列表
GET /admin/announcement/list                # 公告列表
GET /admin/authrule/list                    # 权限规则列表
GET /admin/basemanage/list                  # 基础管理列表
GET /admin/basicEvent/list                  # 基础事件列表
GET /admin/building/list                    # 建筑列表
GET /admin/buildinggroup/list               # 建筑群列表
GET /admin/buildingkeypart/list             # 建筑重点部位列表
GET /admin/checkwork/list                   # 检查工作列表
GET /admin/company/list                     # 公司列表
GET /admin/company/infos                    # 公司信息
GET /admin/companyTagCategoryRelation/list  # 公司标签分类关系列表
GET /admin/dangersurvey/list                # 危险调查列表
GET /admin/datafraud/list                   # 数据欺诈列表
GET /admin/deviceEnter/list                 # 设备录入列表
GET /admin/dispatchingVehicles/list         # 调度车辆列表
GET /admin/dutyRecord/list                  # 值班记录列表
GET /admin/employee/list                    # 员工列表
GET /admin/equipExamine/list                # 设备检查列表
GET /admin/equipmanage/list                 # 设备管理列表
GET /admin/eventCategory/list               # 事件分类列表
GET /admin/eventTpl/list                    # 事件模板列表
GET /admin/file/list                        # 文件列表
GET /admin/firebuds/list                    # 消防芽列表
GET /admin/firehouse/list                   # 消防站列表
GET /admin/firehouseduty/list               # 消防站值班列表
GET /admin/firehouseequipment/list          # 消防站设备列表
GET /admin/firehydrant/list                 # 消火栓列表
GET /admin/firehydrant/searchtype           # 消火栓搜索类型
GET /admin/fireinspect/list                 # 消防检查列表
GET /admin/fireplan/list                    # 消防计划列表
GET /admin/firesystemPosition/list          # 消防系统位置列表
GET /admin/firetrainscore/list              # 消防训练分数列表
GET /admin/floor/list                       # 楼层列表
GET /admin/getSearch/list                   # 搜索列表
GET /admin/gradingconfig/list               # 评分配置列表
GET /admin/heartBeat/list                   # 心跳列表
GET /admin/hiddendanger/list                # 隐患列表
GET /admin/improvement_announcement/list    # 改进公告列表
GET /admin/information/list                 # 信息列表
GET /admin/inspectemplate/list              # 检查模板列表
GET /admin/largeScreenTool/list             # 大屏工具列表
GET /admin/law/list                         # 法规列表
GET /admin/law/detailed                     # 法规详细内容
GET /admin/lbmanage/list                    # 负载均衡管理列表
GET /admin/maintenance/list                 # 维护列表
GET /admin/maintenancecontract/list         # 维护合同列表
GET /admin/maintenancedata/list             # 维护数据列表
GET /admin/maintenancequalification/list   # 维护资质列表
GET /admin/majoreventplan/list              # 重大事件计划列表
GET /admin/map/list                         # 地图列表
GET /admin/map/total                        # 地图区域总数统计
GET /admin/map/getVideo                     # 获取视频
GET /admin/map/budingsearch                 # 建筑搜索
GET /admin/map/tracCar/getDeviceInfo        # 获取车辆信息
GET /admin/map/tracCar/getstatList          # 车辆统计列表
GET /admin/map/deviceshield/getList         # 设备屏蔽列表
GET /admin/mapAttributeUnit/list            # 地图属性单位列表
GET /admin/menu/list                        # 所有菜单按钮接口
GET /admin/menu/top                         # 顶部菜单按钮接口
GET /admin/message/list                     # 消息列表
GET /admin/messagereply/list                # 消息回复列表
GET /admin/nodemailer/list                  # 邮件列表
GET /admin/notificationmanagement/list     # 通知管理列表
GET /admin/operationrule/list               # 操作规则列表
GET /admin/outfireplan/list                 # 外部消防计划列表
GET /admin/patrol/list                      # 巡逻列表
GET /admin/patrolmanage/list                # 巡逻管理列表
GET /admin/patrolrecord/list                # 巡逻记录列表
GET /admin/planpeopel/list                  # 计划人员列表
GET /admin/prem/list                        # 权限列表
GET /admin/projectgroup/list                # 项目组列表
GET /admin/public/list                      # 公共列表
GET /admin/punishmanage/list                # 处罚管理列表
GET /admin/qtripReport/list                 # 出行报告列表
GET /admin/qualityscore/list                # 质量分数列表
GET /admin/questionbank/list                # 题库列表
GET /admin/questiontype/list                # 题型列表
GET /admin/refuelStatistics/list            # 加油统计列表
GET /admin/refuelcardmanage/list            # 加油卡管理列表
GET /admin/refuelingrecord/list             # 加油记录列表
GET /admin/replyDuration/list               # 回复持续时间列表
GET /admin/rescueWorkers/list               # 救援工人列表
GET /admin/rolemanager/list                 # 角色管理列表
GET /admin/rolemanager/roleList             # 角色列表
GET /admin/securityduty/list                # 安全值班列表
GET /admin/securityinstitution/list         # 安全机构列表
GET /admin/securitytraining/list            # 安全培训列表
GET /admin/service/list                     # 服务列表
GET /admin/sixfamiliar/list                 # 六熟悉列表
GET /admin/soundconfiguration/list          # 声音配置列表
GET /admin/statistics/list                  # 统计列表
GET /admin/street/list                      # 街道列表
GET /admin/streetcode/list                  # 街道编码列表
GET /admin/sytemlog/list                    # 系统日志列表
GET /admin/testplan/list                    # 测试计划列表
GET /admin/testplan/listData                # 测试计划数据列表
GET /admin/unit/list                        # 单位列表
GET /admin/unitlist/list                    # 单位列表
GET /admin/user/list                        # 用户列表
GET /admin/user/signin                      # 用户登录
GET /admin/user/getuser                     # 获取用户信息
GET /admin/user/logout                      # 用户退出
GET /admin/user/existsname                  # 检查用户名是否存在
GET /admin/user/existsnamephone             # 检查用户名手机号是否存在
GET /admin/user/existspassword              # 检查密码是否存在
GET /admin/user/resetPass                   # 重置密码
GET /admin/user/ipConfig                    # IP配置
GET /admin/user/getAccessTokens             # 获取访问令牌
GET /admin/user/groupUser                   # 组用户
GET /admin/value/list                       # 值列表
GET /admin/vehicle/list                     # 车辆列表
GET /admin/vehicleRecord/list               # 车辆记录列表
GET /admin/vehicleinformation/list          # 车辆信息列表
GET /admin/violationhandling/list           # 违章处理列表
GET /admin/violationhandling/handle         # 处理违章
GET /admin/wordbook/list                    # 词典列表
GET /admin/zjstatistics/list                # 浙江统计列表
GET /admin/emini/petitionmanage/list        # 小程序信访管理列表
GET /admin/miniprogram/consultingmanage/list # 小程序咨询管理列表
GET /admin/miniprogram/rehearsemanage/list  # 小程序演练管理列表

GET /application/index/number               # 角标数字
GET /application/user/user/getuser          # 获取用户信息
GET /application/user/user/projectuser      # 项目用户列表
GET /application/user/user/codes            # 获取验证码
GET /application/project/select             # 项目选择
GET /application/project/alarmlist          # 报警列表
GET /application/project/projectreport      # 项目报表
GET /application/project/monthreport        # 月度报表
GET /application/project/taskcenter         # 任务中心统计
GET /application/project/alarmmsg           # 实时报警消息
GET /application/project/alarmmsgRg         # 监管报警消息
GET /application/project/taskMsg            # 任务消息
GET /application/project/serverMsg          # 服务器消息
GET /application/project/taskinfo           # 任务信息
GET /application/projectdev/nfcselect       # NFC设备选择
GET /application/rtdetection/systemcard     # 系统卡片
GET /application/monitor/alarmHostList      # 报警主机列表
GET /application/home/<USER>/headerTotal     # 头部数据
GET /application/home/<USER>/today           # 全部概况事件概况
GET /application/home/<USER>/uavinfo         # 无人机信息
GET /application/file/list                  # 应用文件列表
GET /application/position/list              # 位置列表
GET /application/qywx/list                  # 企业微信列表
GET /application/superviseSheetArtificial/list # 人工监督表列表
GET /application/wordbook/list              # 应用词典列表

GET /frontend/project/list                  # 前端项目列表
GET /frontend/project/project/name          # 查询项目名称
GET /frontend/project/project/projectcount  # 项目总数
GET /frontend/project/projectdev/modelDataAccess # 建筑物数据对接
GET /frontend/testplan/listData             # 前端测试计划列表
GET /frontend/autoCheck/getList             # 自主验收列表
GET /frontend/removeshield/torelievelist    # 屏蔽设备待解除列表
GET /frontend/building/list                 # 前端建筑列表
GET /frontend/buildinggroup/list            # 前端建筑群列表
GET /frontend/buildingkeypart/list          # 前端建筑重点部位列表
GET /frontend/company/list                  # 前端公司列表
GET /frontend/deviceEnter/list              # 前端设备录入列表
GET /frontend/employee/list                 # 前端员工列表
GET /frontend/file/list                     # 前端文件列表
GET /frontend/file/upload                   # 前端文件上传页面
GET /frontend/firehouse/list                # 前端消防站列表
GET /frontend/firehouseduty/list            # 前端消防站值班列表
GET /frontend/firehouseequipment/list       # 前端消防站设备列表
GET /frontend/fireplan/list                 # 前端消防计划列表
GET /frontend/floor/list                    # 前端楼层列表
GET /frontend/improvement_announcement/list # 前端改进公告列表
GET /frontend/maintenance/list              # 前端维护列表
GET /frontend/maintenanceInformation/list   # 前端维护信息列表
GET /frontend/maintenancecontract/list      # 前端维护合同列表
GET /frontend/manage/list                   # 前端管理列表
GET /frontend/mapManageBuildingGroup/list   # 前端地图管理建筑群列表
GET /frontend/mapmaintenancebuildinggroup/list # 前端地图维护建筑群列表
GET /frontend/material/list                 # 前端材料列表
GET /frontend/materialcategory/list         # 前端材料分类列表
GET /frontend/outfireplan/list              # 前端外部消防计划列表
GET /frontend/scope/list                    # 前端范围列表
GET /frontend/securityduty/list             # 前端安全值班列表
GET /frontend/securityinstitution/list      # 前端安全机构列表
GET /frontend/securitytraining/list         # 前端安全培训列表
GET /frontend/service/list                  # 前端服务列表
GET /frontend/serviceAdminRelation/list     # 前端服务管理关系列表
GET /frontend/servicechecklog/list          # 前端服务检查日志列表
GET /frontend/settingconfig/list            # 前端设置配置列表
GET /frontend/unitlist/list                 # 前端单位列表
GET /frontend/user/list                     # 前端用户列表

GET /miniprogram/advisory/list              # 小程序咨询列表
GET /miniprogram/appointment/list           # 小程序预约列表
GET /miniprogram/bookingmanage/list         # 小程序预订管理列表
GET /miniprogram/buildingmanage/list        # 小程序建筑管理列表
GET /miniprogram/buildingmanage/alarmmsg    # 小程序建筑管理报警消息
GET /miniprogram/buildingmanage/getList     # 小程序建筑管理获取列表
GET /miniprogram/buildingmanage/contralRoom # 建筑群房间信息
GET /miniprogram/buildingmanage/findByAddressByPid # 根据pid查询地址
GET /miniprogram/cleanReport/list           # 小程序清洁报告列表
GET /miniprogram/company/list               # 小程序公司列表
GET /miniprogram/detachmentReport/list      # 小程序分队报告列表
GET /miniprogram/electricalReport/list      # 小程序电气报告列表
GET /miniprogram/exercise/list              # 小程序演练列表
GET /miniprogram/fireDeclaration/list       # 小程序消防申报列表
GET /miniprogram/fireRecoveryReport/list    # 小程序消防恢复报告列表
GET /miniprogram/maintainReport/list        # 小程序维护报告列表
GET /miniprogram/markingReport/list         # 小程序标记报告列表
GET /miniprogram/publicity/list             # 小程序宣传列表
GET /miniprogram/rota/list                  # 小程序轮值列表
GET /miniprogram/rota/street                # 小程序街道列表
GET /miniprogram/safeReport/list            # 小程序安全报告列表
GET /miniprogram/safetytraining/list        # 小程序安全培训列表
GET /miniprogram/selfCheckReport/list       # 小程序自检报告列表
GET /miniprogram/user/list                  # 小程序用户列表
GET /miniprogram/venue/list                 # 小程序场地列表

GET /ext/emanage/logistics/costType/list    # 费用类型列表
GET /ext/emanage/logistics/costType/deepSearch # 父级树形列表
GET /ext/emanage/logistics/articalType/list # 物品类型列表
GET /ext/emanage/logistics/articalType/deepSearch # 物品类型父级树形列表
GET /ext/emanage/logistics/dishType/list    # 菜品类型列表
GET /ext/emanage/logistics/dishType/deepSearch # 菜品类型父级树形列表
GET /ext/emanage/logistics/costTotal/costExpensesProportion # 支出费用占比
GET /ext/emanage/petition/total/partTotal   # 信访部门统计
GET /ext/emanage/utils/getmember            # 模糊查询人员
GET /ext/emanage/utils/getAllmember         # 获取所有人员
GET /ext/emini/petition/index/queryAll      # 查看所有人的信访记录

GET /fireapi/company/list                   # 消防API公司列表
GET /fireapi/getbuildinginfo/list           # 获取建筑信息列表
GET /fireapi/getcompanyinfo/list            # 获取公司信息列表
GET /fireapi/getdev/list                    # 获取设备列表
GET /fireapi/getdevstatus/list              # 获取设备状态列表
GET /fireapi/getevent/list                  # 获取事件列表
GET /fireapi/getfan/list                    # 获取风扇列表
GET /fireapi/gethost/list                   # 获取主机列表
GET /fireapi/getuser/list                   # 获取用户列表
GET /fireapi/getwater/list                  # 获取水源列表
GET /fireapi/map/list                       # 消防API地图列表
GET /fireapi/task/list                      # 消防API任务列表

GET /beian/area/list                        # 备案区域列表
GET /beian/area/getname                     # 获取区域名称
GET /beian/area/getcode                     # 获取区域编码
GET /beian/buildinggroup/list               # 备案建筑群列表
GET /beian/buildingkeypart/list             # 备案建筑重点部位列表
GET /beian/company/list                     # 备案公司列表
GET /beian/company/getInfo                  # 获取公司信息
GET /beian/floor/list                       # 备案楼层列表
GET /beian/maintenanceRecord/list           # 备案维护记录列表
GET /beian/securityduty/list                # 备案安全值班列表
GET /beian/securityinstitution/list         # 备案安全机构列表
GET /beian/securitytraining/list            # 备案安全培训列表
GET /beian/servicechecklog/list             # 备案服务检查日志列表
GET /beian/unitlist/list                    # 备案单位列表

GET /common/area/getList                    # 获取行政区域列表
GET /common/area/codeName                   # 根据code查询街道名称
GET /common/attribute/list                  # 通用属性列表
GET /common/auth/list                       # 通用认证列表
GET /common/basic/list                      # 通用基础列表
GET /common/basic/cycle                     # 获取巡查周期选项
GET /common/crontab/list                    # 通用定时任务列表
GET /common/file/list                       # 通用文件列表
GET /common/file/getfile                    # 获取文件(通用)
GET /common/file/test                       # 文件测试
GET /common/message/list                    # 通用消息列表
GET /common/test/excel                      # Excel测试
GET /common/test/syslist                    # 系统列表测试
GET /common/verification/list               # 通用验证列表
GET /common/wordbook/list                   # 通用词典列表

GET /company/admin/list                     # 公司管理列表
GET /company/file/list                      # 公司文件列表

GET /equipment/datareport/list              # 设备数据报告列表
GET /equipment/warehouse_stock/list         # 设备仓库库存列表

GET /platform/improvement_announcement/list # 平台改进公告列表
GET /platform/public/list                  # 平台公共列表
GET /platform/public/systemcard            # 平台系统卡片
GET /platform/public/getSystem             # 获取系统
GET /platform/public/getWordbook           # 获取词典

GET /studymanage/course/list                # 学习管理课程列表
GET /studymanage/courseware/list            # 学习管理课件列表

GET /weixin/alarmfault/list                 # 微信报警故障列表
GET /weixin/center/list                     # 微信中心列表
GET /weixin/givealarm/list                  # 微信报警列表
GET /weixin/repair/list                     # 微信维修列表
GET /weixin/workorder/list                  # 微信工单列表

GET /file/getfile                           # 获取文件
GET /file/getfilegroup                      # 获取文件组
GET /file/list                              # 文件列表

GET /wx                                     # 微信消息接收
GET /api/route                              # API测试接口
GET /alarm-info/list                        # 报警信息列表
GET /crontab/list                           # 定时任务列表
GET /sms/list                               # 短信列表
```

---

## 二、新增接口 (POST)

```
POST /v2/community                          # 新增社区
POST /v2/organization                       # 新增组织
POST /v2/building                           # 新增建筑
POST /v2/key_location                       # 新增重点部位
POST /v2/fire_organization                  # 新增消防组织
POST /v2/resident                           # 新增人员
POST /v2/resident/import-excel              # 批量导入人员(Excel)
POST /v2/fire_station_vehicle_duty_record   # 新增执勤记录

POST /admin/activity/add                    # 添加活动
POST /admin/activityperson/add              # 添加活动人员
POST /admin/alarmInformationRecord/add      # 添加报警信息记录
POST /admin/alarmTrend/add                  # 添加报警趋势
POST /admin/alarmfault/add                  # 添加报警故障
POST /admin/announcement/add                # 添加公告
POST /admin/authrule/add                    # 添加权限规则
POST /admin/basemanage/add                  # 添加基础管理
POST /admin/basicEvent/add                  # 添加基础事件
POST /admin/building/add                    # 添加建筑
POST /admin/buildinggroup/add               # 添加建筑群
POST /admin/buildingkeypart/add             # 添加建筑重点部位
POST /admin/checkwork/add                   # 添加检查工作
POST /admin/company/add                     # 添加公司
POST /admin/companyTagCategoryRelation/add  # 添加公司标签分类关系
POST /admin/dangersurvey/add                # 添加危险调查
POST /admin/datafraud/add                   # 添加数据欺诈
POST /admin/deviceEnter/add                 # 添加设备录入
POST /admin/dispatchingVehicles/add         # 添加调度车辆
POST /admin/dutyRecord/add                  # 添加值班记录
POST /admin/employee/add                    # 添加员工
POST /admin/equipExamine/add                # 添加设备检查
POST /admin/equipmanage/add                 # 添加设备管理
POST /admin/eventCategory/add               # 添加事件分类
POST /admin/eventTpl/add                    # 添加事件模板
POST /admin/file/add                        # 添加文件
POST /admin/firebuds/add                    # 添加消防芽
POST /admin/firehouse/add                   # 添加消防站
POST /admin/firehouseduty/add               # 添加消防站值班
POST /admin/firehouseequipment/add          # 添加消防站设备
POST /admin/firehydrant/add                 # 添加消火栓
POST /admin/fireinspect/add                 # 添加消防检查
POST /admin/fireplan/add                    # 添加消防计划
POST /admin/firesystemPosition/add          # 添加消防系统位置
POST /admin/firetrainscore/add              # 添加消防训练分数
POST /admin/floor/add                       # 添加楼层
POST /admin/getSearch/add                   # 添加搜索
POST /admin/gradingconfig/add               # 添加评分配置
POST /admin/heartBeat/add                   # 添加心跳
POST /admin/hiddendanger/add                # 添加隐患
POST /admin/improvement_announcement/add    # 添加改进公告
POST /admin/information/add                 # 添加信息
POST /admin/inspectemplate/add              # 添加检查模板
POST /admin/largeScreenTool/add             # 添加大屏工具
POST /admin/law/add                         # 添加法规
POST /admin/lbmanage/add                    # 添加负载均衡管理
POST /admin/maintenance/add                 # 添加维护
POST /admin/maintenancecontract/add         # 添加维护合同
POST /admin/maintenancedata/add             # 添加维护数据
POST /admin/maintenancequalification/add   # 添加维护资质
POST /admin/majoreventplan/add              # 添加重大事件计划
POST /admin/map/add                         # 添加地图
POST /admin/map/deviceshield/add            # 添加设备屏蔽
POST /admin/mapAttributeUnit/add            # 添加地图属性单位
POST /admin/menu/add                        # 添加菜单
POST /admin/message/add                     # 添加消息
POST /admin/messagereply/add                # 添加消息回复
POST /admin/nodemailer/add                  # 添加邮件
POST /admin/notificationmanagement/add     # 添加通知管理
POST /admin/operationrule/add               # 添加操作规则
POST /admin/outfireplan/add                 # 添加外部消防计划
POST /admin/patrol/add                      # 添加巡逻
POST /admin/patrolmanage/add                # 添加巡逻管理
POST /admin/patrolrecord/add                # 添加巡逻记录
POST /admin/planpeopel/add                  # 添加计划人员
POST /admin/prem/add                        # 添加权限
POST /admin/projectgroup/add                # 添加项目组
POST /admin/public/add                      # 添加公共
POST /admin/punishmanage/add                # 添加处罚管理
POST /admin/qtripReport/add                 # 添加出行报告
POST /admin/qualityscore/add                # 添加质量分数
POST /admin/questionbank/add                # 添加题库
POST /admin/questiontype/add                # 添加题型
POST /admin/refuelStatistics/add            # 添加加油统计
POST /admin/refuelcardmanage/add            # 添加加油卡管理
POST /admin/refuelingrecord/add             # 添加加油记录
POST /admin/replyDuration/add               # 添加回复持续时间
POST /admin/rescueWorkers/add               # 添加救援工人
POST /admin/rolemanager/add                 # 添加角色管理
POST /admin/securityduty/add                # 添加安全值班
POST /admin/securityinstitution/add         # 添加安全机构
POST /admin/securitytraining/add            # 添加安全培训
POST /admin/service/add                     # 添加服务
POST /admin/sixfamiliar/add                 # 添加六熟悉
POST /admin/soundconfiguration/add          # 添加声音配置
POST /admin/statistics/add                  # 添加统计
POST /admin/street/add                      # 添加街道
POST /admin/streetcode/add                  # 添加街道编码
POST /admin/sytemlog/add                    # 添加系统日志
POST /admin/testplan/add                    # 添加测试计划
POST /admin/unit/add                        # 添加单位
POST /admin/unitlist/add                    # 添加单位列表
POST /admin/user/add                        # 添加用户
POST /admin/user/addUser                    # 管理添加用户
POST /admin/value/add                       # 添加值
POST /admin/vehicle/add                     # 添加车辆
POST /admin/vehicleRecord/add               # 添加车辆记录
POST /admin/vehicleinformation/add          # 添加车辆信息
POST /admin/violationhandling/add           # 添加违章处理
POST /admin/wordbook/add                    # 添加词典
POST /admin/zjstatistics/add                # 添加浙江统计
POST /admin/emini/petitionmanage/add        # 添加小程序信访管理
POST /admin/emini/petitionmanage/disposal   # 处理信访
POST /admin/miniprogram/consultingmanage/add # 添加小程序咨询管理
POST /admin/miniprogram/consultingmanage/report # 咨询报告
POST /admin/miniprogram/rehearsemanage/add  # 添加小程序演练管理
POST /admin/miniprogram/rehearsemanage/reports # 演练报告
POST /admin/excel/serverlistimport          # 服务列表导入
POST /admin/excel/userlistimport            # 用户列表导入
POST /admin/excel/maintenanceimport         # 维护数据导入
POST /admin/excel/com                       # 公司数据处理

POST /application/home/<USER>/uavReport      # 无人机数据上报
POST /application/alarm/other/sendrepair    # 发送维修
POST /application/alarm/other/addmanyremarks # 批量添加备注
POST /application/project/handle            # 处理项目
POST /application/user/user/logout          # 用户退出登录
POST /application/user/user/checkcode       # 验证验证码
POST /application/user/user/checkPhone      # 验证手机号
POST /application/alarm/other/list          # 监管中心列表
POST /application/alarm/other/sendrepair    # 发送维修
POST /application/alarm/other/addmanyremarks # 批量添加备注

POST /frontend/project/addproject           # 添加前端项目
POST /frontend/project/project/add          # 添加项目
POST /frontend/project/projectdev/modelDataAccess # 建筑物数据对接
POST /frontend/testplan/add                 # 添加前端测试计划
POST /frontend/building/add                 # 添加前端建筑
POST /frontend/buildinggroup/add            # 添加前端建筑群
POST /frontend/buildingkeypart/add          # 添加前端建筑重点部位
POST /frontend/company/add                  # 添加前端公司
POST /frontend/deviceEnter/add              # 添加前端设备录入
POST /frontend/employee/add                 # 添加前端员工
POST /frontend/file/add                     # 添加前端文件
POST /frontend/firehouse/add                # 添加前端消防站
POST /frontend/firehouseduty/add            # 添加前端消防站值班
POST /frontend/firehouseequipment/add       # 添加前端消防站设备
POST /frontend/fireplan/add                 # 添加前端消防计划
POST /frontend/floor/add                    # 添加前端楼层
POST /frontend/improvement_announcement/add # 添加前端改进公告
POST /frontend/maintenance/add              # 添加前端维护
POST /frontend/maintenanceInformation/add   # 添加前端维护信息
POST /frontend/maintenancecontract/add      # 添加前端维护合同
POST /frontend/manage/add                   # 添加前端管理
POST /frontend/mapManageBuildingGroup/add   # 添加前端地图管理建筑群
POST /frontend/mapmaintenancebuildinggroup/add # 添加前端地图维护建筑群
POST /frontend/material/add                 # 添加前端材料
POST /frontend/materialcategory/add         # 添加前端材料分类
POST /frontend/outfireplan/add              # 添加前端外部消防计划
POST /frontend/scope/add                    # 添加前端范围
POST /frontend/securityduty/add             # 添加前端安全值班
POST /frontend/securityinstitution/add      # 添加前端安全机构
POST /frontend/securitytraining/add         # 添加前端安全培训
POST /frontend/service/add                  # 添加前端服务
POST /frontend/serviceAdminRelation/add     # 添加前端服务管理关系
POST /frontend/servicechecklog/add          # 添加前端服务检查日志
POST /frontend/settingconfig/add            # 添加前端设置配置
POST /frontend/unitlist/add                 # 添加前端单位列表
POST /frontend/user/add                     # 添加前端用户
POST /frontend/servicechecklog/getInfo      # 获取服务检查日志信息
POST /frontend/servicechecklog/update       # 更新服务检查日志

POST /miniprogram/advisory/add              # 添加小程序咨询
POST /miniprogram/appointment/add           # 添加小程序预约
POST /miniprogram/bookingmanage/add         # 添加小程序预订管理
POST /miniprogram/buildingmanage/add        # 添加小程序建筑管理
POST /miniprogram/cleanReport/add           # 添加小程序清洁报告
POST /miniprogram/company/add               # 添加小程序公司
POST /miniprogram/detachmentReport/add      # 添加小程序分队报告
POST /miniprogram/electricalReport/add      # 添加小程序电气报告
POST /miniprogram/exercise/add              # 添加小程序演练
POST /miniprogram/fireDeclaration/add       # 添加小程序消防申报
POST /miniprogram/fireRecoveryReport/add    # 添加小程序消防恢复报告
POST /miniprogram/maintainReport/add        # 添加小程序维护报告
POST /miniprogram/markingReport/add         # 添加小程序标记报告
POST /miniprogram/publicity/add             # 添加小程序宣传
POST /miniprogram/rota/add                  # 添加小程序轮值
POST /miniprogram/safeReport/add            # 添加小程序安全报告
POST /miniprogram/safetytraining/add        # 添加小程序安全培训
POST /miniprogram/selfCheckReport/add       # 添加小程序自检报告
POST /miniprogram/user/add                  # 添加小程序用户
POST /miniprogram/venue/add                 # 添加小程序场地

POST /file/upload                           # 文件上传
POST /file/delfile                          # 删除文件
POST /frontend/file/upload                  # 前端文件上传
POST /frontend/file/delfile                 # 前端删除文件
POST /common/file/upload                    # 通用文件上传
POST /common/file/delfile                   # 通用删除文件

POST /weixin/center/addremark               # 记录原因(适用于火灾报警屏蔽)

POST /wx                                    # 微信消息接收(POST)

# 基础通用接口(所有模块都有)
POST /*/add                                 # 添加数据(通用)
POST /*/edit                                # 修改数据(通用)
POST /*/delete                              # 删除数据(通用)
POST /*/import                              # 导入数据(通用)
POST /*/export                              # 导出数据(通用)
```

---

## 三、修改接口 (PUT)

```
PUT /v2/community/:id                       # 修改社区
PUT /v2/organization/:id                    # 修改组织
PUT /v2/building/:id                        # 修改建筑
PUT /v2/key_location/:id                    # 修改重点部位
PUT /v2/fire_organization/:id               # 修改消防组织
PUT /v2/resident/:id                        # 修改人员
PUT /v2/fire_station_vehicle_duty_record/:id # 修改执勤记录

PUT /admin/user/editUser                    # 管理修改用户

# 基础通用接口(所有模块都有)
PUT /*/edit                                 # 修改数据(通用)
```

---

## 四、删除接口 (DELETE)

```
DELETE /v2/community/:id                    # 删除社区
DELETE /v2/organization/:id                 # 删除组织
DELETE /v2/building/:id                     # 删除建筑
DELETE /v2/key_location/:id                 # 删除重点部位
DELETE /v2/fire_organization/:id            # 删除消防组织
DELETE /v2/resident/:id                     # 删除人员
DELETE /v2/fire_station_vehicle_duty_record/:id # 删除执勤记录

# 基础通用接口(所有模块都有)
DELETE /*/delete                            # 删除数据(通用)
```

---

## 路由规则说明

1. **约定路由**: `/{module}/{controller}/{action}`
2. **RESTful路由**:
   - GET `/{resource}` - 列表
   - GET `/{resource}/:id` - 详情
   - POST `/{resource}` - 新增
   - PUT `/{resource}/:id` - 修改
   - DELETE `/{resource}/:id` - 删除

3. **模块前缀**:
   - `/admin/*` - 管理后台
   - `/v2/*` - API v2版本
   - `/application/*` - 应用端
   - `/frontend/*` - 前端
   - `/common/*` - 通用功能
   - `/miniprogram/*` - 小程序
   - `/ext/*` - 扩展模块
   - `/weixin/*` - 微信相关

---

## 统计汇总

- **查询接口**: 约220个
- **新增接口**: 约200个
- **修改接口**: 约10个
- **删除接口**: 约9个
- **总计**: 约439个接口

*注：此统计基于实际代码分析得出的具体接口数量，包含基础通用接口*
