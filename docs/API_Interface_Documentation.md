# FastNode API接口文档

## 项目概述
- **框架**: ThinkJS
- **架构**: MVC模式，采用模块化设计
- **路由模式**: 约定路由 + 自定义路由
- **认证方式**: JWT Token + 签名验证

## 接口分类说明

### 一、查询接口 (GET)

#### 1.1 基础查询接口
```
GET /[controller]/list           # 列表查询(所有模块通用)
GET /[controller]/find           # 单条数据查询(所有模块通用)
```

#### 1.2 V2模块查询接口
```
GET /v2/community                # 社区列表
GET /v2/community/:id            # 获取单个社区
GET /v2/mobile/community         # 移动端社区列表
GET /v2/mobile/community/:id     # 移动端社区详情
GET /v2/mobile/communityRisk/:id # 移动端社区风险详情
GET /v2/organization             # 组织列表
GET /v2/organization/:id         # 获取单个组织
GET /v2/building                 # 建筑列表
GET /v2/building/:id             # 获取单个建筑
GET /v2/buildingFloor            # 建筑楼层列表
GET /v2/key_location             # 重点部位列表
GET /v2/key_location/:id         # 获取单个重点部位
GET /v2/fire_organization        # 消防组织列表
GET /v2/fire_organization/:id    # 获取单个消防组织
GET /v2/resident                 # 人员列表
GET /v2/resident/:id             # 获取单个人员
GET /v2/drawing-catalog          # 图纸目录列表
GET /v2/real_time_police         # 实时警情
GET /v2/fire_station_vehicle_duty_record # 消防站车辆执勤记录列表
GET /v2/fire_station_vehicle_duty_record/:id # 获取单条执勤记录
GET /v2/get_fire_station_vehicle_duty_record_group # 执勤记录分组
```

#### 1.3 Admin模块查询接口
```
GET /admin/[controller]/list     # 管理后台列表查询
GET /admin/map/total             # 地图区域总数统计
GET /admin/firehydrant/searchtype # 消火栓数据词典
GET /admin/map/tracCar/getstatList # 车辆统计列表
```

#### 1.4 Application模块查询接口
```
GET /application/user/user/getuser # 获取用户信息
GET /application/user/user/projectuser # 项目用户列表
GET /application/user/user/codes   # 获取验证码
GET /application/project/select    # 项目选择
GET /application/project/alarmlist # 报警列表
GET /application/projectdev/nfcselect # NFC设备选择
GET /application/rtdetection/systemcard # 系统卡片
```

#### 1.5 文件相关查询接口
```
GET /file/getfile                # 获取文件
GET /file/getfilegroup           # 获取文件组
GET /common/file/getfile         # 获取文件(通用)
GET /frontend/file/upload        # 前端文件上传页面
```

#### 1.6 通用查询接口
```
GET /wx                          # 微信消息接收
GET /common/basic/cycle          # 获取巡查周期选项
GET /beian/area/getname          # 获取区域名称
GET /beian/area/getcode          # 获取区域编码
GET /beian/company/getInfo       # 获取公司信息
```

### 二、新增接口 (POST)

#### 2.1 基础新增接口
```
POST /[controller]/add           # 添加数据(所有模块通用)
POST /[controller]/import        # 导入数据(所有模块通用)
```

#### 2.2 V2模块新增接口
```
POST /v2/community               # 新增社区
POST /v2/organization            # 新增组织
POST /v2/building                # 新增建筑
POST /v2/key_location            # 新增重点部位
POST /v2/fire_organization       # 新增消防组织
POST /v2/resident                # 新增人员
POST /v2/resident/import-excel   # 批量导入人员(Excel)
POST /v2/fire_station_vehicle_duty_record # 新增执勤记录
```

#### 2.3 Admin模块新增接口
```
POST /admin/[controller]/add     # 管理后台添加数据
POST /admin/violationhandling/add # 添加违章记录
POST /admin/dutyRecord/add       # 添加值班记录
POST /admin/inspectemplate/add   # 添加检查模板
POST /admin/basemanage/addType   # 添加装备类型
```

#### 2.4 Application模块新增接口
```
POST /application/user/user/login # 用户登录
POST /application/user/user/register # 用户注册
POST /application/user/user/bindUser # 绑定用户
POST /application/user/user/findpass # 找回密码
POST /application/alarm/other/addremark # 添加报警备注
POST /application/alarm/other/addrepair # 添加维修记录
```

#### 2.5 文件上传接口
```
POST /file/upload                # 文件上传
POST /frontend/file/upload       # 前端文件上传
POST /common/file/upload         # 通用文件上传
```

#### 2.6 其他新增接口
```
POST /wx                         # 微信消息推送
POST /admin/map/total            # 地图统计数据提交
POST /fireapi/[controller]/[action] # 消防API接口
```

### 三、修改接口 (PUT/POST)

#### 3.1 基础修改接口
```
POST /[controller]/edit          # 编辑数据(所有模块通用)
PUT  /[controller]/:id           # RESTful风格修改
```

#### 3.2 V2模块修改接口
```
PUT /v2/community/:id            # 修改社区
PUT /v2/organization/:id         # 修改组织
PUT /v2/building/:id             # 修改建筑
PUT /v2/key_location/:id         # 修改重点部位
PUT /v2/fire_organization/:id    # 修改消防组织
PUT /v2/resident/:id             # 修改人员信息
PUT /v2/fire_station_vehicle_duty_record/:id # 修改执勤记录
```

#### 3.3 Admin模块修改接口
```
POST /admin/[controller]/edit    # 管理后台编辑数据
POST /admin/violationhandling/edit # 编辑违章记录
POST /admin/dutyRecord/edit      # 编辑值班记录
POST /admin/inspectemplate/edit  # 编辑检查模板
```

#### 3.4 Application模块修改接口
```
POST /application/user/user/editpic # 修改用户头像
POST /application/user/user/editpassword # 修改用户密码
POST /application/alarm/other/edittype # 编辑报警类型
POST /application/alarm/other/editmanytype # 批量编辑报警类型
```

### 四、删除接口 (DELETE/POST)

#### 4.1 基础删除接口
```
POST /[controller]/delete        # 删除数据(所有模块通用)
DELETE /[controller]/:id         # RESTful风格删除
```

#### 4.2 V2模块删除接口
```
DELETE /v2/community/:id         # 删除社区
DELETE /v2/organization/:id      # 删除组织
DELETE /v2/building/:id          # 删除建筑
DELETE /v2/key_location/:id      # 删除重点部位
DELETE /v2/fire_organization/:id # 删除消防组织
DELETE /v2/resident/:id          # 删除人员
DELETE /v2/fire_station_vehicle_duty_record/:id # 删除执勤记录
```

#### 4.3 Admin模块删除接口
```
POST /admin/[controller]/delete  # 管理后台删除数据
GET  /admin/violationhandling/delete # 删除违章记录
POST /admin/dutyRecord/delete    # 删除值班记录
POST /admin/inspectemplate/delete # 删除检查模板
POST /admin/basemanage/deleteType # 删除装备类型
POST /admin/datafraud/delete     # 删除数据防伪记录
```

#### 4.4 文件删除接口
```
POST /file/delfile               # 删除文件
POST /frontend/file/delfile      # 前端删除文件
POST /common/file/delfile        # 通用删除文件
```

### 五、特殊功能接口

#### 5.1 用户认证相关
```
POST /application/user/user/logout # 用户退出登录
POST /application/user/user/checkcode # 验证验证码
POST /application/user/user/checkPhone # 验证手机号
```

#### 5.2 业务处理接口
```
GET  /admin/violationhandling/handle # 处理违章
POST /application/alarm/other/sendrepair # 发送维修
POST /application/alarm/other/addmanyremarks # 批量添加备注
```

#### 5.3 统计报表接口
```
GET /application/project/projectreport # 项目报表
GET /application/project/monthreport # 月度报表
GET /application/project/taskcenter # 任务中心统计
```

#### 5.4 实时数据接口
```
GET /application/project/alarmmsg    # 实时报警消息
GET /application/project/alarmmsgRg  # 监管报警消息
GET /application/monitor/alarmHostList # 报警主机列表
```

## 路由规则说明

1. **约定路由**: `/{module}/{controller}/{action}`
2. **RESTful路由**: 
   - GET `/{resource}` - 列表
   - GET `/{resource}/:id` - 详情
   - POST `/{resource}` - 新增
   - PUT `/{resource}/:id` - 修改
   - DELETE `/{resource}/:id` - 删除

3. **模块前缀**:
   - `/admin/*` - 管理后台
   - `/v2/*` - API v2版本
   - `/application/*` - 应用端
   - `/frontend/*` - 前端
   - `/common/*` - 通用功能
   - `/miniprogram/*` - 小程序

## 认证说明

- 大部分接口需要JWT Token认证
- 部分接口需要签名验证
- 不同模块使用不同的中间件进行权限控制

## 参数说明

### 通用查询参数
- `_page`: 分页页码，默认1
- `_limit`: 每页条数，默认10，最大100
- `_sort`: 排序字段，默认ID  
- `_order`: 排序方式，0正序1倒序
- `_search`: 搜索字段列表
- `_like`: 模糊查询字段
- `_between`: 区间查询字段

### 通用响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {}
}
```

---
**文档生成时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**项目框架**: ThinkJS  
**总接口数量**: 约200+个接口 