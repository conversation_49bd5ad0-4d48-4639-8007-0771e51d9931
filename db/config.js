
const config = require('../src/config.js');

console.log(config);

module.exports = {
  'dev': {
    'username': config.mysql.username,
    'password': config.mysql.password,
    'database': config.mysql.database,
    'host': config.mysql.host,
    'port': config.mysql.port,
    'dialect': 'mysql'
  },
  'test': {
    'username': '',
    'password': null,
    'database': 'database_test',
    'host': '127.0.0.1',
    'dialect': 'mysql'
  },
  'demo': {
    'username': config.mysql.username,
    'password': config.mysql.password,
    'database': config.mysql.database,
    'host': config.mysql.host,
    'port': config.mysql.port,
    'dialect': 'mysql'
  },
  'prod': {
    'username': config.mysql.username,
    'password': config.mysql.password,
    'database': config.mysql.database,
    'host': config.mysql.host,
    'port': config.mysql.port,
    'dialect': 'mysql'
  }
};
