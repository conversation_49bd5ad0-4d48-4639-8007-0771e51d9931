'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_firesystem_position_1', 'zid', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '防火分区'
      }),
      queryInterface.addColumn('pt_firesystem_position_1', 'rid', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '房间'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_firesystem_position_1', 'zid'),
      queryInterface.removeColumn('pt_firesystem_position_1', 'rid')
    ]);
  }
};
