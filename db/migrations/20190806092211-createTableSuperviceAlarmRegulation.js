'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_supervise_alarm_regulation', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '法律法规名称'},
      val_id: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '法律法规val值'},
      code: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '条例编号'},
      remarks: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '备注信息'},
      creator: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '管理员账号'},
      creator_name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '管理员昵称'},
      content: {type: Sequelize.TEXT, allowNull: true, defaultValue: '', comment: '条例详情'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_supervise_alarm_regulation');
  }
};
