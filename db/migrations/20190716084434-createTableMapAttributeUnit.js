'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_map_attribute_unit', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      attribute_id: {type: Sequelize.INTEGER, allowNull: true, comment: '属性id'},
      unit_id: {type: Sequelize.INTEGER, allowNull: true, comment: '合同id'},
      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_map_attribute_unit');
  }
};
