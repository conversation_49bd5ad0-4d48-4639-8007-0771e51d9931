'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_alarm_record_log', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      belong_detachment: {type: Sequelize.STRING, allowNull: true, comment: '所属中队'},
      alarm_information_id: {type: Sequelize.INTEGER, allowNull: true, comment: '关联出警记录表中的id'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_alarm_record_log');
  }
};
