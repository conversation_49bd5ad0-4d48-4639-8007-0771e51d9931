'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_alarm_num', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      belong_detachment: {type: Sequelize.STRING, allowNull: true, comment: '所属中队'},
      num: {type: Sequelize.INTEGER, allowNull: true, comment: '期数'},
      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_alarm_num');
  }
};

