'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_task', 'auto_rec', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '是否可以自动恢复：1可以，0不可以'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_task', 'auto_rec')
    ]);
  }
};
