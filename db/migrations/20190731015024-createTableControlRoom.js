'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_control_room', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      rid: {type: Sequelize.STRING(32), allowNull: true, comment: '消控室id'},
      company_id: {type: Sequelize.INTEGER, allowNull: true, comment: '单位id'},
      company_type: {type: Sequelize.INTEGER, allowNull: true, comment: '单位类型'},
      building_group_id: {type: Sequelize.INTEGER, allowNull: true, comment: '建筑群id'},
      building_id: {type: Sequelize.INTEGER, allowNull: true, comment: '建筑id'},
      floor_id: {type: Sequelize.INTEGER, allowNull: true, comment: '楼层id'},
      name: {type: Sequelize.STRING, allowNull: true, comment: '消控室名称'},
      location: {type: Sequelize.STRING, allowNull: true, comment: '位置描述'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_control_room');
  }
};
