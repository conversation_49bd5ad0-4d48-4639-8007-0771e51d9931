'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_building_group_address', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      town_code: {type: Sequelize.INTEGER, allowNull: true, comment: '居委会编码'},
      street_code: {type: Sequelize.STRING(50), allowNull: true, comment: '街道编码'},
      district_code: {type: Sequelize.STRING(50), allowNull: true, comment: '区域编码'},
      city_code: {type: Sequelize.STRING(50), allowNull: true, comment: '城市编码'},
      province_code: {type: Sequelize.STRING(50), allowNull: true, comment: '省份编码'},
      address: {type: Sequelize.STRING(50), allowNull: true, comment: '建筑详细地址'},
      pid: {type: Sequelize.INTEGER, allowNull: true, comment: '建筑群id'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_building_group_address');
  }
};
