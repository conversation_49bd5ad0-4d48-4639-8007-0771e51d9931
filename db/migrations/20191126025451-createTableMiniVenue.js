'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_mini_venue', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '名称'},
      address: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '地址'},
      image: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '图片'},
      score: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '评分'},
      description: {type: Sequelize.TEXT, allowNull: true, comment: '描述'},
      content: {type: Sequelize.TEXT('long'), allowNull: true, comment: '内容'},
      longitude: {type: Sequelize.DECIMAL(12, 8), allowNull: true, comment: '经度'},
      latitude: {type: Sequelize.DECIMAL(12, 8), allowNull: true, comment: '纬度'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_mini_venue');
  }
};
