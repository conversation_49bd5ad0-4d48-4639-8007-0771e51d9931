'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_business', {
      id: { type: Sequelize.INTEGER(11), primaryKey: true, autoIncrement: true, allowNull: false },
      o_name: {type: Sequelize.STRING(50), comment: '企业名称'},
      o_license: {type: Sequelize.STRING(50), comment: '统一社会信用代码'},
      legal_name: {type: Sequelize.STRING(50), comment: '企业法人姓名'},
      legal_phone: {type: Sequelize.STRING(50), comment: '企业法人电话'},
      legal_idcard: {type: Sequelize.STRING(50), comment: '企业法人身份证号'},
      business_license_image: {type: Sequelize.STRING(255), comment: '营业执照照片'},
      create_at: {type: Sequelize.INTEGER(11), defaultValue: 0, comment: '创建时间'},
      creatorid: {type: Sequelize.INTEGER(11), defaultValue: 0, comment: '创建人'},
      update_at: {type: Sequelize.INTEGER(11), defaultValue: 0, comment: '修改时间'},
      modifierid: {type: Sequelize.INTEGER(11), defaultValue: 0, comment: '修改人'}
    }, {
      comment: '事件组合规则'
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_business');
  }
};
