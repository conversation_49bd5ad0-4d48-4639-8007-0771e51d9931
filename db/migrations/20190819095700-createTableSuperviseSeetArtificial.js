'use strict';

// 人工督察单
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_supervise_sheet_artificial', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      company_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '单位id'},
      alarm_strategy_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '报警策略id'},
      recordcode: {type: Sequelize.STRING(50), allowNull: true, defaultValue: '', comment: '电子督察单检查记录编号'},
      overview: {type: Sequelize.TEXT, allowNull: true, defaultValue: '', comment: '电子督察单概述'},
      status: {type: Sequelize.TINYINT, allowNull: true, defaultValue: 0, comment: '任务状态：0待处理，1手动处理，2自动处理'},
      dealing_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '处理人id'},
      dealing_people: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '处理人'},
      assist_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '协助人id'},
      assist_people: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '协助人'},
      check_record_img: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '签字检查记录单'},
      rectification_notice_img: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '责令整改通知书'},
      forensic_img: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '取证图片'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_supervise_sheet_artificial');
  }
};
