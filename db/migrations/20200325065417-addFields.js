'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_group_event_sub_rule', 'type', {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        comment: '属性值状态 状态量或模拟量'
      }),

      queryInterface.addColumn('pt_model_event_rule', 'type', {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        comment: '属性值状态 状态量或模拟量'
      }),

      queryInterface.addColumn('pt_device_event_rule', 'type', {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        comment: '属性值状态 状态量或模拟量'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_group_event_sub_rule', 'type'),
      queryInterface.removeColumn('pt_model_event_rule', 'type'),
      queryInterface.removeColumn('pt_device_event_rule', 'type')
    ]);
  }
};
