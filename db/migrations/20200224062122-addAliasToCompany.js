'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_company', 'o_alias', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '单位简称'
      }),
      queryInterface.addColumn('pt_company', 'entrance_address', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '入口位置'
      }),
      queryInterface.addColumn('pt_company', 'industry_nature', {
        type: Sequelize.STRING(4),
        allowNull: true,
        comment: '行业性质'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_company', 'o_alias'),
      queryInterface.removeColumn('pt_company', 'entrance_address'),
      queryInterface.removeColumn('pt_company', 'industry_nature')
    ]);
  }
};
