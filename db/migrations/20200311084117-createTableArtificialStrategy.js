'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_supervise_sheet_strategy', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      name: {type: Sequelize.STRING, allowNull: false, defaultValue: 0, comment: '策略名称'},
      remark: {type: Sequelize.TEXT, allowNull: false, comment: '备注'},
      creator: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '创建人id'},
      creator_name: {type: Sequelize.STRING(50), allowNull: false, defaultValue: '', comment: '创建人名称'},
      status: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '0禁用，1启用 '},
      alarm_type: {type: Sequelize.INTEGER, allowNull: false,  defaultValue: 0, comment: '事件判断类型'},
      alarm_rules: {type: Sequelize.TEXT('long'), allowNull: true, comment: '生成策略'},
      laws_type: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '法规分类id'},
      scenario_id: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '应用场景id'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_supervise_sheet_strategy');
  }
};