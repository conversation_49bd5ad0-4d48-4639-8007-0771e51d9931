'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_devmodel_attribute_value', 'scenario', {
        type: Sequelize.STRING(255),
        allowNull: true,
        defaultValue: '',
        comment: '法规分类对应场景id，多个用逗号分隔 默认值'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_devmodel_attribute_value', 'scenario')
    ]);
  }
};
