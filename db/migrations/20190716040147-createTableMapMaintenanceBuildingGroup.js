'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_map_maintenance_building_group', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      admin_id: {type: Sequelize.INTEGER, allowNull: true, comment: '账号id'},
      company_id: {type: Sequelize.INTEGER, allowNull: true, comment: '单位id'},
      building_group_id: {type: Sequelize.INTEGER, allowNull: true, comment: '建筑群id'},
      status: {type: Sequelize.INTEGER, allowNull: true, comment: '0待审核 1驳回 2通过'},
      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_map_maintenance_building_group');
  }
};
