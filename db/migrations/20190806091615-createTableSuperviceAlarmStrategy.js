'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_supervise_alarm_strategy', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '报警策略名称'},
      remark: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '备注'},
      creator: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '管理员账号'},
      creator_name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '管理员昵称'},
      status: {type: Sequelize.TINYINT, allowNull: true, defaultValue: 0, comment: '启用状态： 0启用，1禁用'},
      alarm_rules: {type: Sequelize.TEXT, allowNull: true, defaultValue: '', comment: '报警规则配置'},
      alarm_tips: {type: Sequelize.TEXT, allowNull: true, defaultValue: '', comment: '报警提示配置'},
      alarm_notice: {type: Sequelize.TEXT, allowNull: true, defaultValue: '', comment: '报警通知配置'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_supervise_alarm_strategy');
  }
};