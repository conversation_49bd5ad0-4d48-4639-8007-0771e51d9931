'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_control_room_onduty', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      current_date: {type: Sequelize.STRING, allowNull: true, comment: '日期'},
      status: {type: Sequelize.TINYINT, allowNull: true, comment: '0正常, 1异常'},
      company_id: {type: Sequelize.INTEGER, allowNull: true, comment: '单位id'},
      company_type: {type: Sequelize.INTEGER, allowNull: true, comment: '单位类型'},
      building_group_id: {type: Sequelize.INTEGER, allowNull: true, comment: '建筑群id'},
      building_id: {type: Sequelize.INTEGER, allowNull: true, comment: '建筑id'},
      floor_id: {type: Sequelize.INTEGER, allowNull: true, comment: '楼层id'},
      control_room_id: {type: Sequelize.INTEGER, allowNull: true, comment: '消控室id'},
      control_room_num: {type: Sequelize.INTEGER, allowNull: true, comment: '消控室个数'},
      class_number: {type: Sequelize.STRING(30), allowNull: true, comment: '班次'},
      class_duration: {type: Sequelize.INTEGER, allowNull: true, comment: '当班时长'},
      from_employee: {type: Sequelize.INTEGER, allowNull: true, comment: '交班人'},
      to_employee: {type: Sequelize.INTEGER, allowNull: true, comment: '接班人'},
      change_time: {type: Sequelize.INTEGER, allowNull: true, comment: '交班时间'},
      onduty_info: {type: Sequelize.TEXT, allowNull: true, comment: '演练方案'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_control_room_onduty');
  }
};
