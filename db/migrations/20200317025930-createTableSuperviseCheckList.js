'use strict';

// 行政处罚单
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_supervise_check_list', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      checkcode: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '检查单编号'},
      project_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '建筑群id'},
      company_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '单位id'},
      task_list: {type: Sequelize.TEXT('long'), allowNull: true, comment: '电子督察单编号列表'},
      status: {type: Sequelize.TINYINT, allowNull: true, defaultValue: 0, comment: '任务状态：1待处理，2处理中，3已整改，4已执法，5已关闭'},
      creator_id: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '创建人id'},
      creator_name: {type: Sequelize.STRING(50), allowNull: false, defaultValue: '', comment: '创建人名称'},
      dealing_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '处理人id'},
      dealing_people: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '处理人'},
      assist_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '协助人id'},
      assist_people: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '协助人'},
      lawcode: {type: Sequelize.STRING, allowNull: false, defaultValue: '', comment: '行政处罚单编号'},
      noticecode: {type: Sequelize.STRING, allowNull: false, defaultValue: '', comment: '整改通知书编号'},
      result_msg: {type: Sequelize.TEXT, allowNull: true, defaultValue: '', comment: '撤销结语'},
      result_time: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '撤销时间'},
      result_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '撤销人id'},
      result_handler: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '撤销人'},
      dispatch_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '分派人id'},
      dispatch_handler: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '分派人'},
      punishment_signature: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '被处罚人签字'},
      artificial_id: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '人工督察单id'},
      delete: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '是否撤销：0未撤销，1已撤销'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_supervise_check_list');
  }
};