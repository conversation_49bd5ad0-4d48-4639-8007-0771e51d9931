'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_outfire_plan', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      name: {type: Sequelize.STRING, allowNull: true, comment: '灭火疏散预案名称'},
      key_part_id: {type: Sequelize.INTEGER, allowNull: true, comment: '重点部位id'},
      company_id: {type: Sequelize.INTEGER, allowNull: true, comment: '单位id'},
      company_type: {type: Sequelize.INTEGER, allowNull: true, comment: '单位类型'},
      status: {type: Sequelize.TINYINT, allowNull: true, comment: '0未完成, 1已完成'},
      department: {type: Sequelize.STRING, allowNull: true, comment: '组织部门'},
      leader: {type: Sequelize.STRING, allowNull: true, comment: '演练负责人'},
      person: {type: Sequelize.TEXT, allowNull: true, comment: '参与人员'},
      description: {type: Sequelize.TEXT, allowNull: true, comment: '演练方案'},
      result: {type: Sequelize.TEXT, allowNull: true, comment: '演练结果'},
      plan_images: {type: Sequelize.STRING, allowNull: true, comment: '演练照片'},

      record_id: {type: Sequelize.INTEGER, allowNull: true, comment: '记录人id'},
      record_type: {type: Sequelize.TINYINT, allowNull: true, comment: '记录人id'},
      record_name: {type: Sequelize.STRING, allowNull: true, comment: '记录人名称'},
      creator: {type: Sequelize.INTEGER, allowNull: true, comment: '管理员账号'},
      creator_name: {type: Sequelize.STRING, allowNull: true, comment: '管理员昵称'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_outfire_plan');
  }
};
