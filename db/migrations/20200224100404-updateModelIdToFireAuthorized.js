'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.changeColumn(
        'pt_fire_authorized',
        'model_id',
        {
          type: Sequelize.TEXT('long'),
          allowNull: true
        }
      )
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.changeColumn(
        'pt_fire_authorized',
        'model_id',
        {
          type: Sequelize.STRING,
          allowNull: true,
          comment: '模型id'
        }
      )
    ]);
  }
};
