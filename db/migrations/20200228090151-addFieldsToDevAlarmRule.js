'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_project_dev_alarm_rule', 'scenario', {
        type: Sequelize.STRING(255),
        allowNull: true,
        defaultValue: '',
        comment: '法规分类对应场景id，多个用逗号分隔 默认值'
      }),
      queryInterface.addColumn('pt_project_dev_alarm_rule', 'build_type', {
        type: Sequelize.STRING(255),
        allowNull: true,
        defaultValue: '0',
        comment: '建筑性质'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_project_dev_alarm_rule', 'scenario'),
      queryInterface.removeColumn('pt_project_dev_alarm_rule', 'build_type')
    ]);
  }
};
