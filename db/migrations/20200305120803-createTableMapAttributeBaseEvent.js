'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_map_attribute_base_event', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      attr_id: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '属性id'},
      attr_name: {type: Sequelize.STRING(255), allowNull: false, defaultValue: '', comment: '属性名称'},
      base_event_id: {type: Sequelize.STRING(255), allowNull: false, defaultValue: '', comment: '基础事件id'},
      isuse: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 1, comment: '是否启用，1:是，0，否，默认值1'},
      create_time: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '创建时间'},
      creatorid: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '创建人'},
      update_time: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '修改时间'},
      modifierid: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '修改人'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_map_attribute_base_event');
  }
};
