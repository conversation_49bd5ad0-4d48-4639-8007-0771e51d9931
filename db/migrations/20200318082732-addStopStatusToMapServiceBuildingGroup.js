'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_map_service_building_group', 'stop_status', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '停止接入状态：0未停止接入，1停止接入'
      }),
      queryInterface.addColumn('pt_map_service_building_group', 'stop_reason', {
        type: Sequelize.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: '停止接入原因'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_map_service_building_group', 'stop_status'),
      queryInterface.removeColumn('pt_map_service_building_group', 'stop_reason')
    ]);
  }
};
