'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_basic_event', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true},
      event_id: {type: Sequelize.STRING(64), comment: '事件UUID'},
      event_type: {type: Sequelize.INTEGER, comment: '基础事件类型Id'},
      auto_rec: {type: Sequelize.INTEGER, defaultValue: '1', comment: '是否可以自动恢复（0=否，1=是）'},
      company_id: {type: Sequelize.INTEGER, comment: '建筑管理单位id'},
      company_name: {type: Sequelize.STRING(255), defaultValue: '', comment: '建筑管理单位名称'},
      project_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '建筑群id'},
      project_name: {type: Sequelize.STRING(50), defaultValue: '', comment: '建筑群名称'},
      building_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '建筑id'},
      building_name: {type: Sequelize.STRING(50), defaultValue: '', comment: '建筑名称'},
      floor_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '楼层id'},
      floor_name: {type: Sequelize.STRING(50), defaultValue: '', comment: '楼层名称'},
      pro_device_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '检测装置id'},
      pro_dev_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '主机Id/设备Id'},
      pro_dev_name: {type: Sequelize.STRING(50), defaultValue: '', comment: '设备名称'},
      pro_model_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '模型Id'},
      attr_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '属性id'},
      attr_name: {type: Sequelize.STRING(50), defaultValue: '', comment: '属性名称'},
      attr_type: {type: Sequelize.INTEGER, defaultValue: '0', comment: '属性type'},
      loop_number: {type: Sequelize.STRING(50), defaultValue: '0', comment: '回路号'},
      position_number: {type: Sequelize.STRING(50), defaultValue: '0', comment: '点位号'},
      position_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '点位id'},
      position_index: {type: Sequelize.INTEGER, defaultValue: '0', comment: '为0则是普通设备，不为0则是点位'},
      position_name: {type: Sequelize.STRING(50), defaultValue: '', comment: '点位名称，或设备名称'},
      position_type: {type: Sequelize.INTEGER, defaultValue: '0', comment: '点位类型'},
      is_alone: {type: Sequelize.INTEGER, defaultValue: '0', comment: '是否独立规则（0=否，1=是）'},
      event_rule_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '规则id（组规则Id为0为模型或设备规则，不为0则为组的子规则）'},
      group_rule_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '组规则Id'},
      value: {type: Sequelize.FLOAT(12, 0), allowNull: true, comment: '当前值'},
      safe_value: {type: Sequelize.FLOAT(12,0), defaultValue: '0', comment: '规则警戒值'},
      attr_value_name: {type: Sequelize.STRING(50), defaultValue: '', comment: '属性值名称'},
      unit: {type: Sequelize.STRING(50), defaultValue: '', comment: '单位'},
      desc_info: {type: Sequelize.STRING(50), defaultValue: '', comment: '报警内容'},
      continued_time: {type: Sequelize.INTEGER, defaultValue: '0', comment: '间隔时间'},
      host_reset_status: {type: Sequelize.INTEGER, defaultValue: '0'},
      host_reset_time: {type: Sequelize.INTEGER, defaultValue: '0'},
      server_time: {type: Sequelize.INTEGER, defaultValue: '0', comment: '服务器接收时间'},
      server_end_time: {type: Sequelize.INTEGER, defaultValue: '0', comment: '服务器最后接收时间'},
      status: {type: Sequelize.INTEGER, defaultValue: '0', comment: '类型（0:待处理；1:已发起报修 2:已处理）；人工处理状态，维修完成+其他已处理移入log表'},
      desc_reason: {type: Sequelize.STRING(255), defaultValue: '', comment: '备注原因'},
      reason_image: {type: Sequelize.STRING(255), defaultValue: '', comment: '记录图片'},
      reason_time: {type: Sequelize.DATE, defaultValue: '0', comment: '记录时间'},
      reason_status: {type: Sequelize.INTEGER, defaultValue: '0', comment: '记录类型'},
      relieve_time: {type: Sequelize.DATE, defaultValue: '0', comment: '屏蔽解除时间'},
      reason: {type: Sequelize.STRING(255), defaultValue: '', comment: '记录原因'},
      handler: {type: Sequelize.STRING(255), defaultValue: '', comment: '记录人姓名'},
      repair_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '维修工单id'},
      frequency: {type: Sequelize.INTEGER, defaultValue: '0', comment: '本次次数'},
      created_at: {type: Sequelize.DATE, comment: '创建时间'},
      service_company_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '服务商id'},
      service_company_name: {type: Sequelize.STRING(255), defaultValue: '', comment: '服务商名称'},
      accurate_alarm_id: {type: Sequelize.STRING(255), defaultValue: '', comment: '人工督查单uuid'},
      supervise_id: {type: Sequelize.STRING(255), defaultValue: '', comment: '电子督查单uuid'},
      artificial_id: {type: Sequelize.INTEGER, defaultValue: '0', comment: '人工督查单id'},
      sid: {type: Sequelize.INTEGER, defaultValue: '0', comment: '系统id'},
      tid: {type: Sequelize.INTEGER, defaultValue: '0', comment: '设备类型id'},
      handler_mobile: {type: Sequelize.STRING(50), defaultValue: '', comment: '处理人手机号'},
      userid: {type: Sequelize.INTEGER, defaultValue: '0', comment: '建筑管理单位用户id'},
      operation_time: {type: Sequelize.INTEGER, defaultValue: '0', comment: '操作时间'}
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_activity_event');
  }
};
