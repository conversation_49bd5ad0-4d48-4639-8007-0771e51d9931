'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_system_setmodel', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      model_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '模型id'},
      system_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '系统类型id'},
      deleted: {type: Sequelize.TINYINT, allowNull: true, defaultValue: 0, comment: '0启用，1删除'},

      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_system_setmodel');
  }
};
