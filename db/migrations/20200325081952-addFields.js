'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_group_event_rule', 'model_id', {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        comment: '模型id'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_group_event_rule', 'model_id')
    ]);
  }
};
