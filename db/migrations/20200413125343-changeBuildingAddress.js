'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.changeColumn(
        'pt_building_group_address',
        'town_code',
        {
          type: Sequelize.STRING(50),
          allowNull: true
        }
      )
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.changeColumn(
        'pt_building_group_address',
        'town_code',
        {
          type: Sequelize.STRING(50),
          allowNull: true
        }
      )
    ]);
  }
};
