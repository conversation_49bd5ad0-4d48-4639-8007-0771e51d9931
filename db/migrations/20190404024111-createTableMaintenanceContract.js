'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_maintenance_contract', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      maintenance_id: {type: Sequelize.INTEGER, allowNull: true, comment: '维保单位id'},
      company_id: {type: Sequelize.INTEGER, allowNull: true, comment: '签合同单位id'},
      company_type: {type: Sequelize.INTEGER, allowNull: true, comment: '签合同单位类型'},
      company_name: {type: Sequelize.STRING, allowNull: true, comment: '签合同单位名称'},
      service_scope: {type: Sequelize.STRING, allowNull: true, comment: '服务范围'},
      contract_number: {type: Sequelize.STRING, allowNull: true, comment: '合同编号'},
      contract_sign_date: {type: Sequelize.DATE, allowNull: true, comment: '签订日期'},
      contract_start_date: {type: Sequelize.DATE, allowNull: true, comment: '起效日期'},
      contract_end_date: {type: Sequelize.DATE, allowNull: true, comment: '结束日期'},
      contract_file: {type: Sequelize.STRING, allowNull: true, comment: '合同文件'},
      status: {type: Sequelize.INTEGER, allowNull: true, comment: '状态 0待审核，1驳回，2通过'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_maintenance_contract');
  }
};
