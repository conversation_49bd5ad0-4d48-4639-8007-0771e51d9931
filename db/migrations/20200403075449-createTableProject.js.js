'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_project', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      bid: {type: Sequelize.INTEGER, allowNull: true, comment: '建筑id'},
      sid: {type: Sequelize.INTEGER, allowNull: true, comment: '服务商id'},
      pname: {type: Sequelize.STRING(50), allowNull: true, comment: '项目名称'},
      people: {type: Sequelize.STRING(50), allowNull: true, comment: '项目负责人'},
      phone: {type: Sequelize.INTEGER, allowNull: true, comment: '负责人联系方式'},
      starttime: {type: Sequelize.DATE, allowNull: true, comment: '施工时间'},
      address: {type: Sequelize.STRING(50), allowNull: true, comment: '项目地址'},
      img: {type: Sequelize.STRING(50), allowNull: true, comment: '项目图片'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_project');
  }
};
