'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_control_room', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      company_id: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '服务商单位id'},
      name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '房间名称'},
      bid: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '建筑id'},
      pid: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '建筑群id'},
      fid: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '楼层id'},
      states: {type: Sequelize.TINYINT(4), allowNull: true, defaultValue: 0, comment: '房间状态 0:未启用 1:已启用'},
      createTime: {type: Sequelize.INTEGER, allowNull: true, comment: '创建时间'},
      createUser: {type: Sequelize.INTEGER, allowNull: true, comment: '创建人'},
      updateTime: {type: Sequelize.INTEGER, allowNull: true, comment: '修改时间'},
      updateUser: {type: Sequelize.INTEGER, allowNull: true, comment: '修改人'},
      location: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '房间位置描述'},
      type: {type: Sequelize.TINYINT(4), defaultValue: 0, allowNull: true, comment: '设备房间类型: 1消控室, 2风机房, 3水泵房'},
      ext_id: {type: Sequelize.STRING(32), allowNull: true, defaultValue: 0, comment: '二级房间id'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_control_room');
  }
};
