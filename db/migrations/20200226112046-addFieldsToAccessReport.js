'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    /*
      Add altering commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.createTable('users', { id: Sequelize.INTEGER });
    */
    return Promise.all([
      queryInterface.addColumn('pt_access_report', 'has_get_supervise', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '电子督查单是否对接成功 1：已接通 0：未接通'
      }),
      queryInterface.addColumn('pt_access_report', 'has_get_atifical', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '人工督查单是否对接成功 1：已接通 0：未接通'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    /*
      Add reverting commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.dropTable('users');
    */
    return Promise.all([
      queryInterface.removeColumn('pt_access_report', 'has_get_supervise'),
      queryInterface.removeColumn('pt_access_report', 'has_get_atifical')
    ]);
  }
};
