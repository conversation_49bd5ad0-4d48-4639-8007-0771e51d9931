'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    /*
      Add altering commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.createTable('users', { id: Sequelize.INTEGER });
    */
    return Promise.all([
      queryInterface.addColumn('pt_project_dev_alarm_rule', 'basic_type', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '基础事件类型'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    /*
      Add reverting commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.dropTable('users');
    */
    return Promise.all([
      queryInterface.removeColumn('pt_project_dev_alarm_rule', 'basic_type')
    ]);
  }
};
