'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'lawtype', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '法规分类id'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'scenario', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '应用场景id'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'enforcement_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '执法单id'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'review_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '复核单id'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'handle_deadline', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '处理截止时间'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'people_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '处理人id'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'end_people', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '处理人'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'end_time', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '处理时间'
      }),
      queryInterface.addColumn('pt_supervise_sheet_artificial', 'alarm_level', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '报警级别'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'lawtype'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'scenario'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'enforcement_id'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'review_id'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'handle_deadline'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'people_id'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'end_people'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'end_time'),
      queryInterface.removeColumn('pt_supervise_sheet_artificial', 'alarm_level')
    ]);
  }
};
