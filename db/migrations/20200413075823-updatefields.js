'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.changeColumn('pt_model_event_rule', 'event_value', {
        type: Sequelize.DOUBLE(11, 4),
        allowNull: true
      }),
      queryInterface.changeColumn('pt_device_event_rule', 'event_value', {
        type: Sequelize.DOUBLE(11, 4),
        allowNull: true
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.changeColumn('pt_model_event_rule', 'event_value', {
        type: Sequelize.INTEGER,
        allowNull: true
      }),
      queryInterface.changeColumn('pt_device_event_rule', 'event_value', {
        type: Sequelize.INTEGER,
        allowNull: true
      })
    ]);
  }
};
