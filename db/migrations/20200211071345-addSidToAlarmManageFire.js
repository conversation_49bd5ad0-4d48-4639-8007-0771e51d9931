'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_alarm_manage_fire', 'sid', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '系统id'
      }),
      queryInterface.addColumn('pt_alarm_manage_fire', 'tid', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '设备类型id'
      }),
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_alarm_manage_fire', 'sid'),
      queryInterface.removeColumn('pt_alarm_manage_fire', 'tid'),
    ]);
  }
};
