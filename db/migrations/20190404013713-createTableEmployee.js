'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_employee', {
      id: { type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      company_type: {type: Sequelize.INTEGER, allowNull: true},
      company_id: {type: Sequelize.INTEGER, allowNull: true, comment: '公司id'},
      sex: {type: Sequelize.INTEGER, allowNull: true, comment: '1男，2女'},
      age: {type: Sequelize.INTEGER, allowNull: true, comment: '年龄'},
      name: {type: Sequelize.STRING(50), allowNull: true, comment: '姓名'},
      identity_card_number: {type: Sequelize.STRING(20), allowNull: true, comment: '身份证号'},
      mobile: {type: Sequelize.STRING(20), allowNull: true, comment: '手机号'},
      education: {type: Sequelize.STRING(20), allowNull: true, comment: '文化程度'},
      position: {type: Sequelize.STRING(50), allowNull: true, comment: '岗位'},
      position_type: {type: Sequelize.STRING(50), allowNull: true, comment: '岗位类型'},
      responsibility: {type: Sequelize.STRING, allowNull: true, comment: '职责'},
      entry_date: {type: Sequelize.DATE, allowNull: true, comment: '入职时间'},
      certificate_type: {type: Sequelize.INTEGER, allowNull: true, comment: '证书类型，1注册消防工程师，2构建筑物消防员,3灭火救援员'},
      certificate_level: {type: Sequelize.INTEGER, allowNull: true, comment: '证书级别，1一级，2二级,3三级，4四级，5五级'},
      certificate_number: {type: Sequelize.STRING(50), allowNull: true, comment: '证书编号'},
      certificate_start: {type: Sequelize.DATE, allowNull: true, comment: '证书有效期'},
      certificate_end: {type: Sequelize.DATE, allowNull: true, comment: '证书有效期'},
      fire_training: {type: Sequelize.INTEGER, allowNull: true, comment: '是否接受过消防培训'},
      evacuation_guider: {type: Sequelize.INTEGER, allowNull: true, comment: '是否为疏散引导员'},
      is_leader: {type: Sequelize.INTEGER, allowNull: true, comment: '是否是分管领导'},
      remark: {type: Sequelize.STRING(50), allowNull: true, comment: '备注'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_employee');
  }
};
