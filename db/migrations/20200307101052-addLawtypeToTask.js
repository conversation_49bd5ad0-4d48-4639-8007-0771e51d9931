'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_task', 'lawtype', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '法规分类id'
      }),
      queryInterface.addColumn('pt_task', 'scenario', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '应用场景id'
      }),
      queryInterface.addColumn('pt_task', 'enforcement_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '执法单id'
      }),
      queryInterface.addColumn('pt_task', 'review_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '复核单id'
      }),
      queryInterface.addColumn('pt_task', 'handle_deadline', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '处理截止时间'
      }),
      queryInterface.addColumn('pt_task', 'sid', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '报警系统id'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_task', 'lawtype'),
      queryInterface.removeColumn('pt_task', 'scenario'),
      queryInterface.removeColumn('pt_task', 'enforcement_id'),
      queryInterface.removeColumn('pt_task', 'review_id'),
      queryInterface.removeColumn('pt_task', 'handle_deadline'),
      queryInterface.removeColumn('pt_task', 'sid')
    ]);
  }
};
