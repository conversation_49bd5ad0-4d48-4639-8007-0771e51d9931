'use strict';

// 规则配置
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_setting_config', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      type: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '类型值'},
      val: {type: Sequelize.TEXT, allowNull: true, defaultValue: '', comment: '配置内容'},
      remark: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '备注信息'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_setting_config');
  }
};
