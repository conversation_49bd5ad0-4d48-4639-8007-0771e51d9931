'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_project_group', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},

      pid: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '上级分组id'},
      status: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '1禁用，0启用'},
      name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '分组名称'},
      description: {type: Sequelize.TEXT, allowNull: true, comment: '分组描述'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_project_group');
  }
};
