'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return queryInterface.createTable('pt_regulation_classification', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},

      lawtype: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '法规类型，1:设施设备，2：运维管理'},
      scenario: {type: Sequelize.STRING(255), allowNull: false, defaultValue: '', comment: '应用场景'},
      lawname: {type: Sequelize.STRING(255), allowNull: false, defaultValue: '', comment: '法规名称'},
      catalogue: {type: Sequelize.STRING(255), allowNull: false, defaultValue: '', comment: '法规目录'},
      content: {type: Sequelize.STRING(255), allowNull: false, defaultValue: '', comment: '法规内容'},
      description: {type: Sequelize.STRING(255), allowNull: false, defaultValue: '', comment: '描述'},
      isuse: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 1, comment: '是否启用，1:是，0，否，默认值1'},
      create_time: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '创建时间'},
      creatorid: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '创建人'},
      update_time: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '修改时间'},
      modifierid: {type: Sequelize.INTEGER, allowNull: false, defaultValue: 0, comment: '修改人'}
    });
  },

  down: function (queryInterface, Sequelize) {
    return queryInterface.dropTable('pt_map_project_admin');
  }
};
