'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_supervise_alarm_upgrade', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      david: {type: Sequelize.INTEGER, allowNull: true, comment: '报警规则id'},
      front_level_id: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '原始等级'},
      after_level_id: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '升级后等级'},
      time_interval: {type: Sequelize.INTEGER, allowNull: true, comment: '升级时间间隔'},
      isdelete: {type: Sequelize.TINYINT, allowNull: true, defaultValue: 0, comment: '是否被删除'},
      msgset: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '通知升级后的通知方式，多个以，号分隔'},
      creator: {type: Sequelize.INTEGER, allowNull: true, defaultValue: 0, comment: '管理员账号'},
      creator_name: {type: Sequelize.STRING, allowNull: true, defaultValue: '', comment: '管理员昵称'},
      created_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, defaultValue: Sequelize.NOW, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_supervise_alarm_upgrade');
  }
};
