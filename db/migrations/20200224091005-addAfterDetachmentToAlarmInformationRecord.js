'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_alarm_information_record', 'after_detachment', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '所属中队'
      }),
      queryInterface.addColumn('pt_alarm_information_record', 'after_information_id', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: '出警记录id'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_alarm_information_record', 'after_detachment'),
      queryInterface.removeColumn('pt_alarm_information_record', 'after_information_id')
    ]);
  }
};
