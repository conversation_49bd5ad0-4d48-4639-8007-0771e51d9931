'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('pt_value', {
      id: {type: Sequelize.INTEGER, primaryKey: true, autoIncrement: true, allowNull: false},
      code: {type: Sequelize.STRING(20), allowNull: true, comment: '值'},
      name: {type: Sequelize.STRING, allowNull: true, comment: '属性值名称'},
      sort: {type: Sequelize.INTEGER, allowNull: true, comment: '排序'},

      created_at: {type: Sequelize.DATE, allowNull: true, comment: '创建时间'},
      updated_at: {type: Sequelize.DATE, allowNull: true, comment: '修改时间'}
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_value');
  }
};
