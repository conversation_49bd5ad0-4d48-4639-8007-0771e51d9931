'use strict';

module.exports = {
  up: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.addColumn('pt_mini_wx_user', 'venue_id', {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '场馆id'
      })
    ]);
  },

  down: function (queryInterface, Sequelize) {
    return Promise.all([
      queryInterface.removeColumn('pt_mini_wx_user', 'venue_id')
    ]);
  }
};
