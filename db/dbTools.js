const {program} = require('commander');
const Knex = require('knex');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const camelCase = require('camelcase');

program
    .command('doc <table> [env]')
    .description('生成数据字典')
    .action(async (table, env) => {
        env = env || 'dev';
        table = table.trim();
        console.log(`生成${env}环境${table}表的数据字典\n\n`);
        const knex = await initDB(env);
        const databaseName = getConfig(env).database;
        await outPutDoc(table, databaseName, knex);
        process.exit(0);
    });

program.command('script <table> [env]')
    .description('生成建表脚本')
    .action(async (table, env) => {
        env = env || 'dev';
        table = table.trim();
        const knex = await initDB(env);
        const databaseName = getConfig(env).database;
        console.log(`生成${env}环境${table}表的建表脚本`);
        await outPutScript(table, databaseName, knex)
    });

program.parse(process.argv);

function getConfig(env) {
    process.env.NODE_ENV = env;
    const allConfig = require('./config');
    const config = allConfig[env];
    return config;
}

async function initDB(env) {
    process.env.NODE_ENV = env;
    const config = getConfig(env);
    const knex = new Knex({
        client: 'mysql2',
        connection: {
            host: config.host,
            user: config.username,
            password: config.password,
            database: config.database
        }
    });
    return knex;
}


async function outPutDoc(table, db, knex) {
    const data = await knex.withSchema('information_schema')
        .table('COLUMNS')
        .where('TABLE_SCHEMA', db)
        .where('TABLE_NAME', table)
        .orderBy('ORDINAL_POSITION', 'ASC')
        .select('COLUMN_NAME', 'COLUMN_TYPE', 'is_NULLABLE', 'COLUMN_DEFAULT', 'COLUMN_COMMENT');
    const dict = data.map(item => `|${Object.values(item).map(item => {
        if (!item) {
            item = '-'
        }
        return item;
    }).join('|')}|`).join('\r\n');
    console.log('|字段|类型|空|默认|注释|\n' +
        '|:----    |:-------    |:--- |-- -|------      |');
    console.log(dict);
}

async function outPutScript(table, db, knex) {
    const fileName = `${moment().format('YYYYMMDDHHmmSS')}-` +
        `createTable${camelCase(table, {pascalCase: true})}.js`;
    const data = await knex.withSchema('information_schema')
        .table('COLUMNS')
        .where('TABLE_SCHEMA', db)
        .where('TABLE_NAME', table)
        .orderBy('ORDINAL_POSITION', 'ASC')
        .select('COLUMN_NAME', 'COLUMN_TYPE', 'IS_NULLABLE', 'COLUMN_DEFAULT', 'COLUMN_COMMENT', 'COLUMN_KEY',
            'ORDINAL_POSITION', 'CHARACTER_MAXIMUM_LENGTH', 'CHARACTER_OCTET_LENGTH', 'NUMERIC_PRECISION',
            'NUMERIC_SCALE', 'DATETIME_PRECISION', 'CHARACTER_SET_NAME', 'COLLATION_NAME', 'EXTRA', 'DATA_TYPE');
    const columns = [];
    for (let item of data) {
        let info = columnStr(item);
        columns.push(info);
    }
    const content =
        `'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('${table}',{\n${columns.join(',\n            ')}\n})
    },
     down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('pt_activity_event');
  }
};
`;
    const file = path.join(__dirname, './migrations', fileName);
    fs.writeFileSync(file, content);
    console.log(`生成文件：${fileName}`);
    process.exit(0);
}


function columnStr(item) {
    let str = `${item.COLUMN_NAME}: {`;
    str += `type: ${typeCase(item.DATA_TYPE)}${typeLength(item.COLUMN_TYPE)}, `;
    if (item.COLUMN_KEY === 'PRI') {
        str += 'primaryKey: true, '
    }
    if (item.EXTRA === 'auto_increment') {
        str += 'autoIncrement: true, '
    }
    if (item.IS_NULLABLE === 'YES') {
        str += 'allowNull :true, '
    }
    if (item.COLUMN_DEFAULT !== null) {
        if (typeof item.COLUMN_COMMENT === 'string') {
            str += `defaultValue: '${item.COLUMN_DEFAULT}', `
        } else {
            str += `defaultValue: ${item.COLUMN_DEFAULT}, `
        }
    }
    if (item.COLUMN_COMMENT) {
        str += `comment: '${item.COLUMN_COMMENT}'`
    }
    str += '}';
    return str;
}

function typeLength(str) {
    const index = str.indexOf('(');
    if (index !== -1) {
        return str.substr(index);
    } else {
        return ''
    }
}


function typeCase(type) {
    switch (type) {
        case 'varchar':
            return 'Sequelize.STRING';
        case 'int':
            return 'Sequelize.INTEGER';
        case  'bigint':
            return 'Sequelize.BIGINT';
        case 'blob':
            return 'Sequelize.BLOB';
        case 'datetime':
            return 'Sequelize.DATE';
        case 'date':
            return 'Sequelize.DATEONLY';
        case 'decimal':
            return 'Sequelize.DECIMAL';
        case 'double':
            return 'Sequelize.DOUBLE';
        case 'float':
            return 'Sequelize.FLOAT';

        case 'longtext':
        case 'mediumtext':
        case 'text':
            return 'Sequelize.TEXT';
        default:
            return 'unknown';
    }
}



